{"compileOnSave": false, "compilerOptions": {"baseUrl": ".", "outDir": "build", "sourceMap": true, "jsx": "react", "declaration": false, "module": "ESNext", "moduleResolution": "node", "emitDecoratorMetadata": false, "experimentalDecorators": true, "importHelpers": true, "target": "es5", "typeRoots": ["node_modules/@types"], "lib": ["es2018", "dom"], "allowSyntheticDefaultImports": true, "rootDirs": ["/src", "/test", "/mock", "./typings"], "forceConsistentCasingInFileNames": true, "noImplicitReturns": true, "noUnusedLocals": true, "allowJs": true, "strict": true, "skipLibCheck": true, "types": ["node"], "paths": {"@/*": ["src/*"], "@rome/stone/*": ["node_modules/@rome/cli-plugin-stone/lib/*"]}}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "src/**/*.jsx", "tests/**/*.ts", "tests/**/*.tsx"], "exclude": ["node_modules", "**/*.spec.ts"]}