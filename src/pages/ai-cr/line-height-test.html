<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Line-height对Placeholder对齐影响测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            margin: 0;
            padding: 40px;
        }

        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.9);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
        }

        .test-title {
            font-size: 24px;
            font-weight: 700;
            color: #1a1a1a;
            margin-bottom: 30px;
            text-align: center;
        }

        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .test-item {
            padding: 20px;
            border-radius: 12px;
            border: 1px solid rgba(226, 232, 240, 0.4);
            background: rgba(255, 255, 255, 0.5);
        }

        .test-item h4 {
            margin: 0 0 15px 0;
            font-size: 14px;
            font-weight: 600;
            color: #475569;
        }

        .test-input {
            width: 100%;
            height: 48px;
            padding: 0 16px;
            border: 2px solid rgba(226, 232, 240, 0.4);
            border-radius: 16px;
            font-size: 14px;
            font-weight: 500;
            background: rgba(255, 255, 255, 0.95);
            outline: none;
            box-shadow: none;
            box-sizing: border-box;
            margin-bottom: 10px;
            vertical-align: middle;
            display: block;
        }

        .test-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
        }

        .test-input::placeholder {
            color: #64748b;
            font-weight: 400;
            opacity: 1;
        }

        /* 不同line-height测试 */
        .lh-normal { line-height: normal; }
        .lh-1 { line-height: 1; }
        .lh-44 { line-height: 44px; }
        .lh-46 { line-height: 46px; }
        .lh-48 { line-height: 48px; }
        .lh-1-2 { line-height: 1.2; }
        .lh-1-4 { line-height: 1.4; }
        .lh-1-5 { line-height: 1.5; }

        /* placeholder对应的line-height */
        .lh-normal::placeholder { line-height: normal; }
        .lh-1::placeholder { line-height: 1; }
        .lh-44::placeholder { line-height: 44px; }
        .lh-46::placeholder { line-height: 46px; }
        .lh-48::placeholder { line-height: 48px; }
        .lh-1-2::placeholder { line-height: 1.2; }
        .lh-1-4::placeholder { line-height: 1.4; }
        .lh-1-5::placeholder { line-height: 1.5; }

        .best-practice {
            background: #f0fdf4;
            border-color: #bbf7d0;
        }

        .info-box {
            background: rgba(102, 126, 234, 0.1);
            border: 1px solid rgba(102, 126, 234, 0.2);
            border-radius: 12px;
            padding: 20px;
            margin-top: 30px;
        }

        .info-box h3 {
            margin: 0 0 10px 0;
            color: #667eea;
            font-size: 16px;
        }

        .comparison-text {
            font-size: 12px;
            color: #64748b;
            margin-top: 5px;
        }

        .visual-guide {
            position: relative;
            margin: 10px 0;
        }

        .visual-guide::before {
            content: '';
            position: absolute;
            left: 16px;
            right: 16px;
            top: 50%;
            height: 1px;
            background: rgba(102, 126, 234, 0.3);
            z-index: 1;
        }

        .visual-guide::after {
            content: '中心线';
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 10px;
            color: #667eea;
            background: rgba(255, 255, 255, 0.9);
            padding: 2px 4px;
            border-radius: 4px;
            z-index: 2;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">Line-height对Placeholder对齐影响测试</h1>
        
        <div class="test-grid">
            <div class="test-item">
                <h4>line-height: normal</h4>
                <div class="visual-guide">
                    <input type="text" class="test-input lh-normal" placeholder="测试placeholder对齐" value="">
                    <input type="text" class="test-input lh-normal" placeholder="测试placeholder对齐" value="输入的文本">
                </div>
                <div class="comparison-text">默认值，可能在不同浏览器表现不一致</div>
            </div>

            <div class="test-item">
                <h4>line-height: 1</h4>
                <div class="visual-guide">
                    <input type="text" class="test-input lh-1" placeholder="测试placeholder对齐" value="">
                    <input type="text" class="test-input lh-1" placeholder="测试placeholder对齐" value="输入的文本">
                </div>
                <div class="comparison-text">相对值，可能过于紧凑</div>
            </div>

            <div class="test-item best-practice">
                <h4>line-height: 44px ⭐</h4>
                <div class="visual-guide">
                    <input type="text" class="test-input lh-44" placeholder="测试placeholder对齐" value="">
                    <input type="text" class="test-input lh-44" placeholder="测试placeholder对齐" value="输入的文本">
                </div>
                <div class="comparison-text">推荐值：48px高度 - 4px边框 = 44px</div>
            </div>

            <div class="test-item">
                <h4>line-height: 46px</h4>
                <div class="visual-guide">
                    <input type="text" class="test-input lh-46" placeholder="测试placeholder对齐" value="">
                    <input type="text" class="test-input lh-46" placeholder="测试placeholder对齐" value="输入的文本">
                </div>
                <div class="comparison-text">稍微宽松一点的设置</div>
            </div>

            <div class="test-item">
                <h4>line-height: 48px</h4>
                <div class="visual-guide">
                    <input type="text" class="test-input lh-48" placeholder="测试placeholder对齐" value="">
                    <input type="text" class="test-input lh-48" placeholder="测试placeholder对齐" value="输入的文本">
                </div>
                <div class="comparison-text">与容器高度相同，可能导致文本溢出</div>
            </div>

            <div class="test-item">
                <h4>line-height: 1.2</h4>
                <div class="visual-guide">
                    <input type="text" class="test-input lh-1-2" placeholder="测试placeholder对齐" value="">
                    <input type="text" class="test-input lh-1-2" placeholder="测试placeholder对齐" value="输入的文本">
                </div>
                <div class="comparison-text">相对值，约16.8px</div>
            </div>

            <div class="test-item">
                <h4>line-height: 1.4</h4>
                <div class="visual-guide">
                    <input type="text" class="test-input lh-1-4" placeholder="测试placeholder对齐" value="">
                    <input type="text" class="test-input lh-1-4" placeholder="测试placeholder对齐" value="输入的文本">
                </div>
                <div class="comparison-text">相对值，约19.6px</div>
            </div>

            <div class="test-item">
                <h4>line-height: 1.5</h4>
                <div class="visual-guide">
                    <input type="text" class="test-input lh-1-5" placeholder="测试placeholder对齐" value="">
                    <input type="text" class="test-input lh-1-5" placeholder="测试placeholder对齐" value="输入的文本">
                </div>
                <div class="comparison-text">相对值，约21px，常用的阅读行高</div>
            </div>
        </div>

        <div class="info-box">
            <h3>🎯 最佳实践建议</h3>
            <ul style="margin: 0; padding-left: 20px; color: #475569;">
                <li><strong>推荐使用 line-height: 44px</strong> - 为48px高度的输入框提供最佳对齐</li>
                <li><strong>计算公式</strong>：容器高度 - 上下边框宽度 = 48px - 4px = 44px</li>
                <li><strong>vertical-align: middle</strong> - 确保垂直居中对齐</li>
                <li><strong>display: block</strong> - 避免内联元素的对齐问题</li>
                <li><strong>统一placeholder样式</strong> - 确保placeholder与输入文本使用相同的line-height</li>
            </ul>
        </div>
    </div>
</body>
</html>
