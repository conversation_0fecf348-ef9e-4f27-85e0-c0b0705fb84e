// 使用 Stone 统一依赖管理
// 使用 Stone 统一依赖管理
import React from '@rome/stone/react'
import { BrowserRouter as Router, Switch, Route, Redirect } from '@rome/stone/react-router-dom'
import { observer } from '@rome/stone/mobx-react'
import Layout from './components/Layout'
import ErrorBoundary from './components/ErrorBoundary'
import RouteGuard from './components/RouteGuard'
import AIAssistant from './pages/AIAssistant'
import Dashboard from './pages/Dashboard'
import CodeReviewPage from './pages/CodeReviewPage'
import Settings from './pages/Settings'
import RuleConfig from './pages/RuleConfig'
import TestCodeReview from './pages/TestCodeReview'
import { routes } from './config/routes'

const App: React.FC = observer(() => {
  return (
    <ErrorBoundary>
      <Router basename="/ai-cr">
        <Layout>
          <RouteGuard>
            <ErrorBoundary>
              <Switch>
                {/* 动态生成路由 */}
                {routes.map((route) => (
                  <Route
                    key={route.path}
                    exact={route.exact}
                    path={route.path}
                    component={getComponentByName(route.component)}
                  />
                ))}

                {/* 404 重定向到首页 */}
                <Route path="*">
                  <Redirect to="/" />
                </Route>
              </Switch>
            </ErrorBoundary>
          </RouteGuard>
        </Layout>
      </Router>
    </ErrorBoundary>
  )
})

// 根据组件名称获取组件
const getComponentByName = (componentName?: string) => {
  const components: Record<string, React.ComponentType> = {
    AIAssistant,
    Dashboard,
    CodeReviewPage,
    Settings,
    RuleConfig,
    TestCodeReview
  }

  return components[componentName || 'AIAssistant'] || AIAssistant
}

export default App
