import React, { useState } from '@rome/stone/react'
import { <PERSON>, Button } from '@ss/mtd-react'
import AgentInfoCard from './components/AgentInfoCard'

const TestPage: React.FC = () => {
  const [showAgentCard, setShowAgentCard] = useState(false)

  const handleShowAgent = () => {
    setShowAgentCard(true)
  }

  const handleAgentSubmit = (data: any) => {
    console.log('Agent data:', data)
    setShowAgentCard(false)
    alert('Agent 启动成功！数据：' + JSON.stringify(data, null, 2))
  }

  const handleAgentCancel = () => {
    setShowAgentCard(false)
  }

  return (
    <div style={{
      padding: '20px',
      background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
      minHeight: '100vh'
    }}>
      <h1 style={{
        color: '#1a1a1a',
        marginBottom: '16px',
        fontSize: '28px',
        fontWeight: 600
      }}>
        🚀 闪购 AI 代码审查系统
      </h1>

      <p style={{
        color: '#666',
        fontSize: '16px',
        marginBottom: '24px'
      }}>
        企业级AI代码审查服务，智能化开发流程助手
      </p>

      <Card style={{
        marginTop: '20px',
        padding: '24px',
        borderRadius: '12px',
        background: 'rgba(255, 255, 255, 0.9)',
        backdropFilter: 'blur(10px)',
        border: '1px solid rgba(226, 232, 240, 0.6)',
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)'
      }}>
        <h3 style={{
          color: '#1a1a1a',
          marginBottom: '16px',
          fontSize: '20px',
          fontWeight: 600
        }}>
          🎯 核心功能模块
        </h3>

        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '12px',
          marginBottom: '24px'
        }}>
          <div style={{
            padding: '12px',
            background: 'rgba(82, 196, 26, 0.1)',
            borderRadius: '8px',
            border: '1px solid rgba(82, 196, 26, 0.2)'
          }}>
            📋 需求分析 Agent
          </div>
          <div style={{
            padding: '12px',
            background: 'rgba(235, 47, 150, 0.1)',
            borderRadius: '8px',
            border: '1px solid rgba(235, 47, 150, 0.2)'
          }}>
            🐛 代码审查 Agent
          </div>
          <div style={{
            padding: '12px',
            background: 'rgba(24, 144, 255, 0.1)',
            borderRadius: '8px',
            border: '1px solid rgba(24, 144, 255, 0.2)'
          }}>
            📖 PRD 生成 Agent
          </div>
          <div style={{
            padding: '12px',
            background: 'rgba(114, 46, 209, 0.1)',
            borderRadius: '8px',
            border: '1px solid rgba(114, 46, 209, 0.2)'
          }}>
            🎨 UI 原型 Agent
          </div>
          <div style={{
            padding: '12px',
            background: 'rgba(250, 140, 22, 0.1)',
            borderRadius: '8px',
            border: '1px solid rgba(250, 140, 22, 0.2)'
          }}>
            ⚙️ 技术文档 Agent
          </div>
          <div style={{
            padding: '12px',
            background: 'rgba(19, 194, 194, 0.1)',
            borderRadius: '8px',
            border: '1px solid rgba(19, 194, 194, 0.2)'
          }}>
            🔌 API 文档 Agent
          </div>
        </div>

        <div style={{
          display: 'flex',
          gap: '12px',
          flexWrap: 'wrap'
        }}>
          <Button
            type="primary"
            onClick={handleShowAgent}
            style={{
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              border: 'none',
              borderRadius: '6px',
              padding: '8px 16px',
              fontSize: '14px',
              fontWeight: 500
            }}
          >
            🚀 测试代码审查 Agent
          </Button>

          <Button style={{
            borderRadius: '6px',
            padding: '8px 16px',
            fontSize: '14px'
          }}>
            📚 查看文档
          </Button>

          <Button style={{
            borderRadius: '6px',
            padding: '8px 16px',
            fontSize: '14px'
          }}>
            ⚙️ 系统设置
          </Button>
        </div>
      </Card>

      {/* Agent 信息卡片弹窗 */}
      {showAgentCard && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            width: '500px',
            maxWidth: '90vw'
          }}>
            <AgentInfoCard
              agentType="code-review"
              onSubmit={handleAgentSubmit}
              onCancel={handleAgentCancel}
            />
          </div>
        </div>
      )}
    </div>
  )
}

export default TestPage
