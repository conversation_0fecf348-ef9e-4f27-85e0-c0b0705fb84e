// 企业级AI代码审查服务 - 主样式文件 (SCSS) - 基于参考项目的精确复制

// 导入 SCSS 模块
@import 'variables';
@import 'mixins';
@import 'global';

/* 应用根容器 - 与参考项目完全一致 */
#root {
  width: 100%;
  min-width: 0;
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  overflow-x: auto;
}

/* 全局页面容器样式 - 统一设计 */
.page-container {
  height: calc(100vh - 80px);
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  border: 1px solid rgba(226, 232, 240, 0.6);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  margin: 16px;
  padding: 0;
  box-sizing: border-box;
}

.page-content {
  flex: 1;
  overflow: hidden;
  padding: 24px;
}

/* 应用主容器 */
.app-container {
  width: 100%;
  min-width: 0;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  overflow-x: auto;
}

/* 页面过渡动画 */
.page-transition-enter {
  opacity: 0;
  transform: translateY(20px);
}

.page-transition-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms ease-out, transform 300ms ease-out;
}

.page-transition-exit {
  opacity: 1;
  transform: translateY(0);
}

.page-transition-exit-active {
  opacity: 0;
  transform: translateY(-20px);
  transition: opacity 200ms ease-in, transform 200ms ease-in;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 64px;
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid #f1f5f9;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 24px;
  color: #475569;
  font-size: 16px;
  font-weight: 500;
}

/* 错误状态样式 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 64px;
  text-align: center;
}

.error-icon {
  width: 64px;
  height: 64px;
  color: #ef4444;
  margin-bottom: 24px;
}

.error-title {
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 16px;
}

.error-message {
  color: #475569;
  font-size: 16px;
  margin-bottom: 32px;
  max-width: 500px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-container {
    padding: 0;
  }

  .loading-container,
  .error-container {
    padding: 32px;
  }

  .error-title {
    font-size: 20px;
  }

  .error-message {
    font-size: 14px;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  #root {
    --color-border-main: #000000;
    --color-text-secondary: #000000;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
