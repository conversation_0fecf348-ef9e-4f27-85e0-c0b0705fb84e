/* 企业级全局样式 - 基于参考项目的精确复制 */

/* CSS变量定义 - 与参考项目完全一致 */
:root {
  /* 颜色系统 */
  --color-primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --color-primary-gradient-hover: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  --color-background-primary: #ffffff;
  --color-background-secondary: #f8fafc;
  --color-background-tertiary: #f1f5f9;
  --color-background-gradient: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  --color-text-primary: #1e293b;
  --color-text-secondary: #475569;
  --color-text-tertiary: #64748b;
  --color-border-light: #f1f5f9;
  --color-border-main: #e2e8f0;

  /* 间距系统 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;
  --spacing-3xl: 64px;

  /* 圆角系统 */
  --radius-sm: 6px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 24px;

  /* 阴影系统 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-glow: 0 0 20px rgba(102, 126, 234, 0.4);
  --shadow-glow-hover: 0 0 30px rgba(102, 126, 234, 0.6);

  /* 动画系统 */
  --duration-fast: 150ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;
  --easing-out: cubic-bezier(0.0, 0, 0.2, 1);
  --easing-in: cubic-bezier(0.4, 0, 1, 1);
  --easing-in-out: cubic-bezier(0.4, 0, 0.2, 1);
}

/* 全局重置和基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  color: var(--color-text-primary);
  background: var(--color-background-gradient);
  min-height: 100vh;
  overflow-x: hidden;
}

/* 页面淡入动画 */
.page-enter {
  opacity: 0;
  transform: translateY(20px);
}

.page-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity var(--duration-normal) var(--easing-out),
              transform var(--duration-normal) var(--easing-out);
}

.page-exit {
  opacity: 1;
  transform: translateY(0);
}

.page-exit-active {
  opacity: 0;
  transform: translateY(-20px);
  transition: opacity var(--duration-fast) var(--easing-in),
              transform var(--duration-fast) var(--easing-in);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-background-secondary);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--color-border-main);
  border-radius: var(--radius-sm);
  transition: background var(--duration-normal) var(--easing-out);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-tertiary);
}

/* 选择文本样式 */
::selection {
  background: rgba(102, 126, 234, 0.2);
  color: var(--color-text-primary);
}

/* 焦点样式 */
:focus-visible {
  outline: 2px solid rgba(102, 126, 234, 0.5);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

/* 输入框焦点样式特殊处理 - 避免与自定义样式冲突 */
input:focus-visible,
textarea:focus-visible,
select:focus-visible,
.enhanced-input:focus-visible,
[class*="enhanced-input"]:focus-visible {
  outline: none !important;
  outline-offset: 0 !important;
}

/* 全局增强输入框样式 - 统一设计系统 */
.enhanced-input,
input.enhanced-input {
  width: 100%;
  height: 48px;
  padding: 0 16px;
  border: 2px solid rgba(226, 232, 240, 0.4);
  border-radius: 16px;
  font-size: 14px;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.95);
  transition: all 0.2s ease;
  outline: none;
  box-shadow: none;
  line-height: 44px;
  vertical-align: middle;
  display: block;
  box-sizing: border-box;
  margin: 0;
  color: var(--color-text-primary);
  font-family: inherit;
}

.enhanced-input:focus,
input.enhanced-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
  background: rgba(255, 255, 255, 1);
  outline: none;
}

.enhanced-input:hover,
input.enhanced-input:hover {
  border-color: #667eea;
}

.enhanced-input::placeholder,
input.enhanced-input::placeholder {
  color: var(--color-text-tertiary);
  font-weight: 400;
  line-height: 44px;
  vertical-align: middle;
  opacity: 1;
}

/* 全局MTD组件样式重置 - 最强覆盖 */
/* 针对所有MTD输入框组件的强制样式覆盖 */
.mtd-input,
input[class*="mtd"],
[class*="mtd-input"],
.mtd-input-wrapper input,
.mtd-input-affix-wrapper input,
.mtd-input-content input {
  all: unset !important;
  display: block !important;
  width: 100% !important;
  height: 48px !important;
  padding: 0 16px !important;
  border: 2px solid rgba(226, 232, 240, 0.4) !important;
  border-radius: 16px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  background: rgba(255, 255, 255, 0.95) !important;
  color: #1e293b !important;
  line-height: 44px !important;
  transition: all 0.2s ease !important;
  outline: none !important;
  box-shadow: none !important;
  font-family: inherit !important;
  box-sizing: border-box !important;
}

.mtd-input:focus,
input[class*="mtd"]:focus,
[class*="mtd-input"]:focus,
.mtd-input-wrapper input:focus,
.mtd-input-affix-wrapper input:focus,
.mtd-input-content input:focus {
  border-color: #667eea !important;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1) !important;
  background: rgba(255, 255, 255, 1) !important;
  outline: none !important;
}

.mtd-input:hover,
input[class*="mtd"]:hover,
[class*="mtd-input"]:hover,
.mtd-input-wrapper input:hover,
.mtd-input-affix-wrapper input:hover,
.mtd-input-content input:hover {
  border-color: #667eea !important;
}

.mtd-input::placeholder,
input[class*="mtd"]::placeholder,
[class*="mtd-input"]::placeholder,
.mtd-input-wrapper input::placeholder,
.mtd-input-affix-wrapper input::placeholder,
.mtd-input-content input::placeholder {
  color: #64748b !important;
  font-weight: 400 !important;
  line-height: 44px !important;
  opacity: 1 !important;
}

/* 重置MTD组件容器样式 */
.mtd-input-wrapper,
.mtd-input-affix-wrapper,
.mtd-input-content,
[class*="mtd-input-wrapper"],
[class*="mtd-input-affix"],
[class*="mtd-input-content"] {
  all: unset !important;
  display: block !important;
  width: 100% !important;
  background: transparent !important;
}

/* 覆盖MTD Select组件 */
.mtd-select,
.mtd-select-selection,
[class*="mtd-select"] {
  all: unset !important;
  display: block !important;
  width: 100% !important;
  height: 48px !important;
  padding: 0 16px !important;
  border: 2px solid rgba(226, 232, 240, 0.4) !important;
  border-radius: 12px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  background: rgba(255, 255, 255, 0.95) !important;
  color: #1e293b !important;
  line-height: 44px !important;
  transition: all 0.2s ease !important;
  outline: none !important;
  box-shadow: none !important;
  font-family: inherit !important;
  cursor: pointer !important;
}

.mtd-select:hover,
.mtd-select-selection:hover,
[class*="mtd-select"]:hover {
  border-color: #667eea !important;
}

.mtd-select:focus,
.mtd-select-selection:focus,
[class*="mtd-select"]:focus,
.mtd-select-selection.mtd-select-selection-focused {
  border-color: #667eea !important;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1) !important;
  background: rgba(255, 255, 255, 1) !important;
  outline: none !important;
}

/* 通用卡片样式 */
.card {
  background: var(--color-background-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-border-light);
  transition: all var(--duration-normal) var(--easing-out);
  overflow: hidden;
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.card-interactive {
  cursor: pointer;
}

.card-interactive:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-4px);
}

/* 玻璃态效果 */
.glassmorphism {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-lg);
}

/* 渐变文字 */
.gradient-text {
  background: var(--color-primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
}

/* 加载动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-bounce {
  animation: bounce 1s infinite;
}

/* 响应式工具类 */
.container {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

@media (min-width: 640px) {
  .container {
    padding: 0 var(--spacing-lg);
  }
}

@media (min-width: 1024px) {
  .container {
    padding: 0 var(--spacing-xl);
  }
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }

.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }
