// 通用 Mixins - 遵循 BEM 命名规范 - 高级感增强版本

// 卡片样式 mixin - 增强版本
@mixin card-base {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: radius(xl);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  
  // 悬浮光效
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.6s ease;
  }

  &:hover {
    transform: translateY(-4px) scale(1.01);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12);
    border-color: rgba(102, 126, 234, 0.2);

    &::before {
      left: 100%;
    }
  }
  
  &--clickable {
    cursor: pointer;
    
    &:hover {
      transform: translateY(-6px) scale(1.02);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    &:active {
      transform: translateY(-2px) scale(1.01);
    }
  }
}

// 高级卡片样式 mixin
@mixin card-premium {
  @include card-base;

  // 渐变边框效果
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    border-radius: inherit;
  }

  &:hover::after {
    opacity: 1;
  }
}

// 按钮样式 mixin - 增强版本
@mixin button-base {
  border: none;
  border-radius: radius(md);
  font-weight: map-get($font-weight, semibold);
  padding: spacing(md) spacing(xl);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: inherit;
  position: relative;
  overflow: hidden;
  
  // 按钮光效
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
  }

  &:hover::before {
    left: 100%;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;

    &::before {
      display: none;
    }
  }
}

@mixin button-primary {
  @include button-base;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  
  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
  }

  &:active:not(:disabled) {
    transform: translateY(-1px);
  }
}

@mixin button-secondary {
  @include button-base;
  background: transparent;
  border: 2px solid rgba(226, 232, 240, 0.6);
  color: color(secondary);
  
  &:hover:not(:disabled) {
    background: rgba(102, 126, 234, 0.1);
    border-color: rgba(102, 126, 234, 0.3);
    color: $primary-color;
    transform: translateY(-1px);
  }
}

@mixin button-ghost {
  @include button-base;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: color(primary);

  &:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
  }
}

// 输入框样式 mixin - 增强版本
@mixin input-base {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(226, 232, 240, 0.6);
  border-radius: radius(md);
  padding: spacing(md) spacing(lg);
  font-size: map-get($font-size, base);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-family: inherit;
  outline: none;
  
  &:focus {
    border-color: $primary-color;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
    background: rgba(255, 255, 255, 1);
    transform: translateY(-1px);
  }
  
  &:hover {
    border-color: rgba(102, 126, 234, 0.3);
  }

  &::placeholder {
    color: color(tertiary);
    font-weight: map-get($font-weight, medium);
  }
}

// 高级输入框样式
@mixin input-premium {
  @include input-base;
  position: relative;

  &:focus {
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1), 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

// 滚动条样式 mixin - 增强版本
@mixin scrollbar-custom($color: rgba(102, 126, 234, 0.3)) {
  scrollbar-width: thin;
  scrollbar-color: $color transparent;
  
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  
  &::-webkit-scrollbar-thumb {
    background: $color;
    border-radius: 3px;
    transition: background 0.3s ease;
  }
  
  &::-webkit-scrollbar-thumb:hover {
    background: rgba(102, 126, 234, 0.5);
  }
}

// 玻璃态效果 mixin - 增强版本
@mixin glassmorphism($opacity: 0.9) {
  background: rgba(255, 255, 255, $opacity);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: radius(xl);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

@mixin glassmorphism-dark($opacity: 0.1) {
  background: rgba(0, 0, 0, $opacity);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: radius(xl);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

// 渐变文字 mixin - 增强版本
@mixin gradient-text($gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%)) {
  background: $gradient;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: map-get($font-weight, bold);
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.02);
  }
}

// 动画渐变文字
@mixin gradient-text-animated {
  background: linear-gradient(45deg, #667eea, #764ba2, #667eea, #764ba2);
  background-size: 400% 400%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 3s ease-in-out infinite;

  @keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }
}

// 页面容器 mixin - 增强版本
@mixin page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #f1f5f9 100%);
  position: relative;
  overflow-x: hidden;

  // 动态背景效果
  &::before {
    content: '';
    position: fixed;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle at 25% 25%, rgba(102, 126, 234, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(118, 75, 162, 0.03) 0%, transparent 50%);
    animation: backgroundFloat 20s ease-in-out infinite;
    pointer-events: none;
    z-index: 0;
  }

  @keyframes backgroundFloat {
    0%, 100% { transform: translate(0, 0) rotate(0deg); }
    33% { transform: translate(30px, -30px) rotate(120deg); }
    66% { transform: translate(-20px, 20px) rotate(240deg); }
  }
}

// 页面头部 mixin - 增强版本
@mixin page-header {
  position: relative;
  z-index: 1;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(226, 232, 240, 0.6);
  padding: spacing(2xl) spacing(3xl);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);

  // 微妙的顶部光效
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.4), transparent);
  }
}

// 消息动画 mixin - 增强版本
@mixin message-slide-in($delay: 0s) {
  opacity: 0;
  animation: messageSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1) $delay forwards;
  
  @keyframes messageSlideIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

// 页面进入动画
@mixin page-enter-animation($delay: 0s) {
  opacity: 0;
  animation: pageEnter 0.8s cubic-bezier(0.16, 1, 0.3, 1) $delay forwards;

  @keyframes pageEnter {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

// 加载动画 mixin - 增强版本
@mixin loading-pulse($color: $primary-color) {
  animation: loadingPulse 1.4s ease-in-out infinite both;
  
  @keyframes loadingPulse {
    0%, 80%, 100% {
      transform: scale(0.8);
      opacity: 0.5;
    }
    40% {
      transform: scale(1);
      opacity: 1;
    }
  }
}

@mixin loading-spinner($size: 32px, $color: $primary-color) {
  width: $size;
  height: $size;
  border: 3px solid rgba($color, 0.2);
  border-top: 3px solid $color;
  border-radius: 50%;
  animation: spin 1s linear infinite;

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
}

// 悬浮效果 mixin
@mixin hover-lift($distance: 4px) {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    transform: translateY(-$distance);
    box-shadow: 0 ($distance * 2) ($distance * 6) rgba(0, 0, 0, 0.1);
  }
}

@mixin hover-glow($color: $primary-color) {
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 0 20px rgba($color, 0.3);
  }
}

// 脉冲效果
@mixin pulse-effect($color: $primary-color) {
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba($color, 0.3) 0%, transparent 70%);
    transform: translate(-50%, -50%);
    border-radius: inherit;
    animation: pulse 2s ease-in-out infinite;
    pointer-events: none;
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1);
    }
    50% {
      opacity: 0.8;
      transform: translate(-50%, -50%) scale(1.05);
    }
  }
}

// 响应式断点 mixin - 增强版本
@mixin mobile {
  @media (max-width: 768px) {
    @content;
  }
}

@mixin tablet {
  @media (max-width: 1024px) and (min-width: 769px) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: 1025px) {
    @content;
  }
}

@mixin large-desktop {
  @media (min-width: 1440px) {
    @content;
  }
}

// 文字省略 mixin - 增强版本
@mixin text-ellipsis($lines: 1) {
  @if $lines == 1 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  } @else {
    display: -webkit-box;
    -webkit-line-clamp: $lines;
    -webkit-box-orient: vertical;
    overflow: hidden;
    line-height: 1.4;
  }
}

// 居中对齐 mixin - 增强版本
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin flex-column-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

// 头像样式 mixin - 增强版本
@mixin avatar($size: 36px) {
  width: $size;
  height: $size;
  border-radius: radius(md);
  @include flex-center;
  font-size: $size * 0.45;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  flex-shrink: 0;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  // 内部高光
  &::after {
    content: '';
    position: absolute;
    top: 15%;
    left: 15%;
    width: 70%;
    height: 70%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%);
    border-radius: inherit;
    pointer-events: none;
  }

  &:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  }
}

// 徽章样式 mixin
@mixin badge($color: $primary-color) {
  display: inline-flex;
  align-items: center;
  padding: spacing(xs) spacing(sm);
  background: rgba($color, 0.1);
  color: $color;
  border-radius: radius(sm);
  font-size: map-get($font-size, xs);
  font-weight: map-get($font-weight, semibold);
  border: 1px solid rgba($color, 0.2);
  transition: all 0.3s ease;

  &:hover {
    background: rgba($color, 0.2);
    transform: scale(1.05);
  }
}

// 工具提示样式
@mixin tooltip {
  position: relative;

  &::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: spacing(xs) spacing(sm);
    border-radius: radius(xs);
    font-size: map-get($font-size, xs);
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
    z-index: 1000;
  }

  &:hover::after {
    opacity: 1;
  }
}

// 网格布局 mixin
@mixin grid-responsive($min-width: 280px, $gap: spacing(xl)) {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax($min-width, 1fr));
  gap: $gap;

  @include mobile {
    grid-template-columns: 1fr;
    gap: spacing(lg);
  }
}

// 阴影层级 mixin
@mixin shadow-level($level: 1) {
  @if $level == 1 {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  } @else if $level == 2 {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  } @else if $level == 3 {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  } @else if $level == 4 {
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  }
}

// 渐变背景 mixin
@mixin gradient-bg($direction: 135deg, $colors: (#667eea, #764ba2)) {
  background: linear-gradient($direction, $colors);
}

// 文字发光效果
@mixin text-glow($color: $primary-color) {
  text-shadow: 0 0 10px rgba($color, 0.5), 0 0 20px rgba($color, 0.3);
}

// 边框发光效果
@mixin border-glow($color: $primary-color) {
  box-shadow: 0 0 0 1px rgba($color, 0.3), 0 0 10px rgba($color, 0.2);
}
