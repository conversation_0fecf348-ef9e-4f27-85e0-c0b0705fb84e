// 企业级全局样式 - SCSS版本
@import 'variables';
@import 'mixins';

// 全局重置和基础样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: map-get($line-height, normal);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  color: color(primary);
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
  overflow-x: hidden;
}

// 页面淡入动画
.page-enter {
  opacity: 0;
  transform: translateY(20px);
}

.page-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity map-get($transition, normal),
              transform map-get($transition, normal);
}

.page-exit {
  opacity: 1;
  transform: translateY(0);
}

.page-exit-active {
  opacity: 0;
  transform: translateY(-20px);
  transition: opacity map-get($transition, fast),
              transform map-get($transition, fast);
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: $card-background;
  border-radius: radius(sm);
}

::-webkit-scrollbar-thumb {
  background: $border-color;
  border-radius: radius(sm);
  transition: background map-get($transition, normal);
}

::-webkit-scrollbar-thumb:hover {
  background: map-get($text-colors, tertiary);
}

// 选择文本样式
::selection {
  background: rgba(102, 126, 234, 0.2);
  color: color(primary);
}

// 焦点样式
:focus-visible {
  outline: 2px solid rgba(102, 126, 234, 0.5);
  outline-offset: 2px;
  border-radius: radius(sm);
}
