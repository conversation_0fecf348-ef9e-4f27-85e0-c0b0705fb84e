// 统一设计系统变量 - 基于参考项目的精确复制

// 颜色系统 - 与参考项目完全一致
$primary-color: #667eea;
$primary-color-hover: #5a6fd8;
$secondary-color: #764ba2;
$primary-gradient: linear-gradient(135deg, $primary-color 0%, $secondary-color 100%);
$primary-gradient-hover: linear-gradient(135deg, $primary-color-hover 0%, #6a4190 100%);

// 背景色系统 - 精确匹配参考项目
$page-background: rgba(255, 255, 255, 0.95);
$page-background-blur: rgba(255, 255, 255, 0.95);
$card-background: rgba(255, 255, 255, 0.8);
$card-background-hover: rgba(255, 255, 255, 0.9);
$header-background: rgba(255, 255, 255, 0.95);
$input-background: rgba(255, 255, 255, 0.9);

// 全局背景渐变
$global-background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);

// 边框和阴影
$border-color: rgba(226, 232, 240, 0.6);
$border-color-light: rgba(226, 232, 240, 0.4);

// 圆角系统
$border-radius: (
  sm: 8px,
  md: 12px,
  lg: 16px,
  xl: 20px
);

// 阴影系统
$shadow: (
  sm: 0 2px 8px rgba(0, 0, 0, 0.06),
  md: 0 4px 20px rgba(0, 0, 0, 0.08),
  lg: 0 8px 32px rgba(0, 0, 0, 0.08),
  primary: 0 8px 24px rgba(102, 126, 234, 0.3)
);

// 文字颜色
$text-colors: (
  primary: #1a1a1a,
  secondary: #666,
  tertiary: #999,
  white: #fff
);
  
// 间距系统
$spacing: (
  xs: 4px,
  sm: 8px,
  md: 12px,
  lg: 16px,
  xl: 20px,
  2xl: 24px,
  3xl: 32px,
  4xl: 48px
);

// 字体系统
$font-size: (
  xs: 11px,
  sm: 12px,
  base: 14px,
  md: 15px,
  lg: 16px,
  xl: 18px,
  2xl: 20px,
  3xl: 24px
);

$font-weight: (
  normal: 400,
  medium: 500,
  semibold: 600,
  bold: 700
);

$line-height: (
  tight: 1.2,
  normal: 1.4,
  relaxed: 1.6
);

// 布局系统
$layout: (
  page-height: calc(100vh - 96px),
  header-height: 80px,
  page-padding: map-get($spacing, 2xl),
  card-padding: map-get($spacing, xl),
  input-height: 48px
);

// 动画系统
$transition: (
  fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1),
  normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1),
  slow: 0.4s cubic-bezier(0.4, 0, 0.2, 1)
);

// 状态颜色
$status-colors: (
  success: #52c41a,
  warning: #faad14,
  error: #ff4d4f,
  info: #1890ff
);

// 状态颜色变量 - 直接使用
$success-color: #52c41a;
$warning-color: #faad14;
$error-color: #ff4d4f;
$info-color: #1890ff;

// 背景模糊效果
$backdrop-filter: (
  normal: blur(20px),
  light: blur(10px)
);

// 背景模糊效果变量 - 直接使用
$backdrop-filter-normal: blur(20px);
$backdrop-filter-light: blur(10px);

// 函数：获取颜色值
@function color($key) {
  @return map-get($text-colors, $key);
}

// 函数：获取间距值
@function spacing($key) {
  @return map-get($spacing, $key);
}

// 函数：获取圆角值
@function radius($key) {
  @return map-get($border-radius, $key);
}

// 函数：获取阴影值
@function shadow($key) {
  @return map-get($shadow, $key);
}

/* 选择文本样式 */
::selection {
  background: rgba(102, 126, 234, 0.1);
  color: $primary-color;
}

::-moz-selection {
  background: rgba(102, 126, 234, 0.1);
  color: $primary-color;
}

/* 主内容区域全局样式 - 防止被导航栏遮挡 */
.elegant-content {
  padding-top: 64px !important; /* 桌面端导航栏高度 */

  @media (max-width: 768px) {
    padding-top: 56px !important; /* 移动端导航栏高度 */
  }
}
