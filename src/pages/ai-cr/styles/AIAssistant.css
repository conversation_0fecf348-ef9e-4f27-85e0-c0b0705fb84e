/* 企业级AI助手聊天界面样式 */

/* 主容器 - 现在使用统一的page-container */
.ai-assistant-container {
  height: 100%;
  background: transparent;
  overflow: hidden;
}

/* 聊天区域 */
.chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  margin: 0;
  position: relative;
}

.chat-area::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(118, 75, 162, 0.02) 100%);
  pointer-events: none;
}

/* 聊天头部 */
.chat-header {
  padding: var(--spacing-md) var(--spacing-2xl) var(--spacing-sm);
  border-bottom: 1px solid var(--border-color-light);
  background: var(--header-background);
  backdrop-filter: var(--backdrop-filter-light);
  position: relative;
  z-index: 1;
  flex-shrink: 0;
}

.chat-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.chat-header-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  display: flex;
  align-items: center;
}

.chat-header-subtitle {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-top: 2px;
}

/* 消息容器 */
.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-lg) var(--spacing-2xl);
  position: relative;
  z-index: 1;
}

/* 消息样式 */
.message-wrapper {
  display: flex;
  margin-bottom: var(--spacing-lg);
  align-items: flex-start;
  gap: var(--spacing-md);
}

.message-wrapper.user {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(102, 126, 234, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-size: 14px;
}

.message-bubble {
  max-width: 70%;
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--border-radius-lg);
  position: relative;
  word-wrap: break-word;
  line-height: 1.5;
}

.message-bubble.ai {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(226, 232, 240, 0.8);
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-sm);
}

.message-bubble.user {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: var(--shadow-md);
}

.message-bubble.agent {
  background: rgba(82, 196, 26, 0.1);
  border: 1px solid rgba(82, 196, 26, 0.3);
  backdrop-filter: blur(10px);
}

.message-content {
  font-size: var(--font-size-base);
  line-height: 1.6;
}

.message-files {
  margin-top: var(--spacing-sm);
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.message-time {
  font-size: var(--font-size-xs);
  opacity: 0.7;
  margin-top: var(--spacing-xs);
}

/* 打字指示器 */
.message-bubble.typing {
  padding: var(--spacing-md);
}

.typing-indicator {
  display: flex;
  gap: 4px;
  align-items: center;
}

.typing-indicator span {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #667eea;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 输入区域 */
.chat-input-container {
  padding: var(--spacing-lg) var(--spacing-2xl);
  background: var(--header-background);
  border-top: 1px solid var(--border-color-light);
  backdrop-filter: var(--backdrop-filter-light);
  position: relative;
  z-index: 1;
  flex-shrink: 0;
}

.chat-input-wrapper {
  display: flex;
  align-items: flex-end;
  gap: var(--spacing-md);
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid rgba(226, 232, 240, 0.8);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-sm);
  backdrop-filter: blur(10px);
  transition: all var(--transition-normal);
}

.chat-input-wrapper:focus-within {
  border-color: #667eea;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
}

/* 输入框样式 */
.chat-input {
  width: 100%;
  min-height: 48px;
  max-height: 120px;
  padding: 12px 20px;
  border: 2px solid rgba(226, 232, 240, 0.8);
  border-radius: 16px;
  font-size: 15px;
  line-height: 1.5;
  resize: none;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-family: inherit;
  box-sizing: border-box;
}

.chat-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
  background: rgba(255, 255, 255, 1);
}

.chat-input::placeholder {
  color: #94a3b8;
  font-weight: 400;
}

.chat-input-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.send-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: var(--border-radius-md);
  height: 40px;
  padding: 0 var(--spacing-lg);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-sm);
}

.send-button:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.send-button:disabled {
  opacity: 0.5;
  transform: none;
  box-shadow: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-area {
    margin: 16px;
    border-radius: 20px;
  }
  
  .chat-header {
    padding: 20px 24px;
  }
  
  .chat-header-title {
    font-size: 20px;
  }
  
  .messages-container {
    padding: 20px 24px;
  }
  
  .message-bubble {
    padding: 14px 18px;
    font-size: 14px;
  }
  
  .message-bubble.user {
    margin-left: 24px;
  }
  
  .message-bubble.ai {
    margin-right: 24px;
  }
  
  .chat-input-container {
    padding: 20px 24px;
  }
  
  .chat-input {
    font-size: 16px; /* 防止iOS缩放 */
  }
}
