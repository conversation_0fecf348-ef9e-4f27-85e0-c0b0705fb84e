/* MTD组件强制样式覆盖 - 全局生效 */
/* 使用最高优先级确保覆盖所有MTD组件样式 */

/* 重置所有MTD输入框的默认样式 */
.mtd-input,
input[class*="mtd"],
[class*="mtd-input"],
.mtd-input-wrapper input,
.mtd-input-inner,
.mtd-input-content input,
.mtd-input-affix-wrapper input {
  border: none !important;
  box-shadow: none !important;
  background: transparent !important;
  outline: none !important;
}

.mtd-input:focus,
input[class*="mtd"]:focus,
[class*="mtd-input"]:focus,
.mtd-input-wrapper input:focus,
.mtd-input-inner:focus,
.mtd-input-content input:focus,
.mtd-input-affix-wrapper input:focus {
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
}

/* 重置MTD输入框容器样式 */
.mtd-input-wrapper,
[class*="mtd-input-wrapper"],
.mtd-input-inner,
[class*="mtd-input-inner"],
.mtd-input-affix-wrapper,
[class*="mtd-input-affix-wrapper"] {
  border: none !important;
  box-shadow: none !important;
  background: transparent !important;
}

/* Settings页面专用的增强输入框样式 */
.settings-page .enhanced-input,
.settings-page input.enhanced-input,
.enhanced-input {
  width: 100% !important;
  height: 48px !important;
  padding: 0 16px !important;
  border: 2px solid rgba(226, 232, 240, 0.4) !important;
  border-radius: 16px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  background: rgba(255, 255, 255, 0.95) !important;
  transition: all 0.2s ease !important;
  outline: none !important;
  box-shadow: none !important;
  line-height: normal !important;
  min-height: 48px !important;
  max-height: 48px !important;
  color: #1a1a1a !important;
  position: relative !important;
  z-index: 1 !important;
}

.settings-page .enhanced-input:focus,
.settings-page input.enhanced-input:focus,
.enhanced-input:focus {
  border-color: #667eea !important;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1) !important;
  background: rgba(255, 255, 255, 1) !important;
}

.settings-page .enhanced-input:hover,
.settings-page input.enhanced-input:hover,
.enhanced-input:hover {
  border-color: #667eea !important;
}

.settings-page .enhanced-input::placeholder,
.settings-page input.enhanced-input::placeholder,
.enhanced-input::placeholder {
  color: #64748b !important;
  font-weight: 400 !important;
}

/* 确保MTD组件内部的input元素被正确覆盖 */
.settings-page .mtd-input-wrapper .enhanced-input,
.settings-page .mtd-input-affix-wrapper .enhanced-input,
.settings-page .mtd-input-content .enhanced-input {
  width: 100% !important;
  height: 48px !important;
  padding: 0 16px !important;
  border: 2px solid rgba(226, 232, 240, 0.4) !important;
  border-radius: 16px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  background: rgba(255, 255, 255, 0.95) !important;
  transition: all 0.2s ease !important;
  outline: none !important;
  box-shadow: none !important;
}

/* 特殊情况：如果MTD组件使用了内联样式，强制覆盖 */
.settings-page [style*="border"],
.settings-page [style*="box-shadow"] {
  border: 2px solid rgba(226, 232, 240, 0.4) !important;
  box-shadow: none !important;
}

.settings-page [style*="border"]:focus,
.settings-page [style*="box-shadow"]:focus {
  border-color: #667eea !important;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1) !important;
}

/* 确保在任何嵌套情况下都能生效 */
.settings-page * input.enhanced-input,
.settings-page * .enhanced-input,
.settings-page div input.enhanced-input,
.settings-page div .enhanced-input {
  width: 100% !important;
  height: 48px !important;
  padding: 0 16px !important;
  border: 2px solid rgba(226, 232, 240, 0.4) !important;
  border-radius: 16px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  background: rgba(255, 255, 255, 0.95) !important;
  transition: all 0.2s ease !important;
  outline: none !important;
  box-shadow: none !important;
}

/* 移除所有可能的MTD组件边框和阴影 */
.settings-page .mtd-input,
.settings-page .mtd-input-wrapper,
.settings-page .mtd-input-affix-wrapper,
.settings-page .mtd-input-content,
.settings-page .mtd-input-inner,
.settings-page [class*="mtd-input"] {
  border: none !important;
  box-shadow: none !important;
  background: transparent !important;
}

.settings-page .mtd-input:focus,
.settings-page .mtd-input-wrapper:focus,
.settings-page .mtd-input-affix-wrapper:focus,
.settings-page .mtd-input-content:focus,
.settings-page .mtd-input-inner:focus,
.settings-page [class*="mtd-input"]:focus {
  border: none !important;
  box-shadow: none !important;
}

/* 确保Select组件也使用统一样式 */
.settings-page .enhanced-select .mtd-select-selection {
  height: 48px !important;
  border: 2px solid rgba(226, 232, 240, 0.4) !important;
  border-radius: 12px !important;
  background: rgba(255, 255, 255, 0.95) !important;
  transition: all 0.2s ease !important;
  box-shadow: none !important;
  min-width: 140px !important;
}

.settings-page .enhanced-select .mtd-select-selection:hover {
  border-color: #667eea !important;
}

.settings-page .enhanced-select .mtd-select-selection.mtd-select-selection-focused {
  border-color: #667eea !important;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1) !important;
}

/* 调试用：添加红色边框来识别未被覆盖的MTD组件 */
/*
.mtd-input:not(.enhanced-input) {
  border: 2px solid red !important;
}
*/
