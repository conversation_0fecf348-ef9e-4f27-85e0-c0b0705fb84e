/**
 * 路由配置文件
 * 统一管理应用的路由信息
 */

export interface RouteConfig {
  path: string
  icon: string
  label: string
  component?: string
  exact?: boolean
  hidden?: boolean
  description?: string
}

/**
 * 主要路由配置
 * 首页默认为 AI 助手聊天界面
 */
export const routes: RouteConfig[] = [
  {
    path: '/',
    icon: '🤖',
    label: 'AI助手',
    component: 'AIAssistant',
    exact: true,
    description: '智能对话驱动的 Agent 系统，支持代码审查、需求分析等功能'
  },
  {
    path: '/dashboard',
    icon: '📊',
    label: '仪表板',
    component: 'Dashboard',
    exact: true,
    description: '项目概览和数据统计面板'
  },
  {
    path: '/code-review',
    icon: '🔍',
    label: '代码审查',
    component: 'CodeReviewPage',
    exact: true,
    description: '代码质量检测和审查管理'
  },
  {
    path: '/rule-config',
    icon: '⚙️',
    label: '规则配置',
    component: 'RuleConfig',
    exact: true,
    description: '代码审查规则和检测配置'
  },
  {
    path: '/settings',
    icon: '🛠️',
    label: '设置',
    component: 'Settings',
    exact: true,
    description: '系统设置和个人偏好配置'
  },
  {
    path: '/test-review',
    icon: '🧪',
    label: '测试审查',
    component: 'TestCodeReview',
    exact: true,
    hidden: false,
    description: '代码审查结果展示测试页面'
  }
]

/**
 * 获取导航菜单项
 * 过滤掉隐藏的路由
 */
export const getNavigationItems = (): RouteConfig[] => {
  return routes.filter(route => !route.hidden)
}

/**
 * 根据路径获取路由配置
 */
export const getRouteByPath = (path: string): RouteConfig | undefined => {
  return routes.find(route => route.path === path)
}

/**
 * 获取首页路由
 */
export const getHomeRoute = (): RouteConfig => {
  return routes[0] // 第一个路由作为首页
}

/**
 * 检查路径是否为首页
 */
export const isHomePage = (path: string): boolean => {
  return path === '/' || path === ''
}

/**
 * 获取面包屑导航
 */
export const getBreadcrumbs = (currentPath: string): RouteConfig[] => {
  const breadcrumbs: RouteConfig[] = []
  
  // 如果不是首页，添加首页到面包屑
  if (!isHomePage(currentPath)) {
    breadcrumbs.push(getHomeRoute())
  }
  
  // 添加当前页面
  const currentRoute = getRouteByPath(currentPath)
  if (currentRoute) {
    breadcrumbs.push(currentRoute)
  }
  
  return breadcrumbs
}

/**
 * 路由元信息
 */
export const routeMeta = {
  title: '闪购 AI 平台',
  description: '企业级 AI 代码审查和工作流管理平台',
  keywords: ['AI', '代码审查', '工作流', '企业级'],
  author: '闪购技术团队'
}

/**
 * 获取页面标题
 */
export const getPageTitle = (path: string): string => {
  const route = getRouteByPath(path)
  if (route) {
    return `${route.label} - ${routeMeta.title}`
  }
  return routeMeta.title
}

/**
 * 路由守卫配置
 */
export const routeGuards = {
  // 需要认证的路由
  authRequired: ['/settings'],
  
  // 需要特殊权限的路由
  permissionRequired: ['/rule-config'],
  
  // 公开访问的路由
  publicRoutes: ['/', '/dashboard', '/code-review', '/test-review']
}

/**
 * 检查路由是否需要认证
 */
export const requiresAuth = (path: string): boolean => {
  return routeGuards.authRequired.includes(path)
}

/**
 * 检查路由是否需要特殊权限
 */
export const requiresPermission = (path: string): boolean => {
  return routeGuards.permissionRequired.includes(path)
}

/**
 * 检查路由是否为公开路由
 */
export const isPublicRoute = (path: string): boolean => {
  return routeGuards.publicRoutes.includes(path)
}
