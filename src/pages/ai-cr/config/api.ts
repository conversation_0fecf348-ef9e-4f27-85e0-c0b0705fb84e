/**
 * API配置文件
 * 管理不同环境下的API地址和配置
 */

// 环境类型
type Environment = 'development' | 'staging' | 'production'

// API配置接口
interface APIConfig {
  baseURL: string
  timeout: number
  headers: Record<string, string>
}

// 获取当前环境
const getEnvironment = (): Environment => {
  if (typeof window !== 'undefined') {
    const hostname = window.location.hostname
    if (hostname === 'localhost' || hostname === '127.0.0.1') {
      return 'development'
    } else if (hostname.includes('test') || hostname.includes('staging')) {
      return 'staging'
    }
  }
  return process.env.NODE_ENV as Environment || 'development'
}

// 不同环境的API配置
const apiConfigs: Record<Environment, APIConfig> = {
  development: {
    baseURL: '/yunzhuan/api/v1', // 开发环境使用代理
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
    }
  },
  staging: {
    baseURL: 'https://staging-api.example.com/yunzhuan/api/v1', // 测试环境API地址
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
    }
  },
  production: {
    baseURL: 'https://api.example.com/yunzhuan/api/v1', // 生产环境API地址
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
    }
  }
}

// 获取当前环境的API配置
export const getAPIConfig = (): APIConfig => {
  const env = getEnvironment()
  return apiConfigs[env]
}

// API端点配置
export const API_ENDPOINTS = {
  // 代码审查相关
  CODE_REVIEW: '/main/cr_lc',
  REVIEW_HISTORY: '/main/cr_history',
  REVIEW_DETAIL: '/main/cr_detail',
  REVIEW_STATUS: '/main/cr_status',

  // 健康检查
  HEALTH: '/health',

  // 用户相关
  USER_INFO: '/user/info',
  USER_SETTINGS: '/user/settings',

  // 规则配置
  RULES_LIST: '/rules/list',
  RULES_UPDATE: '/rules/update',

  // 统计数据
  STATS_OVERVIEW: '/stats/overview',
  STATS_TRENDS: '/stats/trends',
}

// 导出当前配置
export const currentAPIConfig = getAPIConfig()

// 构建完整的API URL
export const buildAPIURL = (endpoint: string): string => {
  const config = getAPIConfig()
  return `${config.baseURL}${endpoint}`
}

// API响应类型
export interface APIResponse<T = any> {
  code: number
  message: string
  data: T
  success: boolean
}

// 分页响应类型
export interface PaginatedResponse<T = any> {
  list: T[]
  total: number
  page: number
  pageSize: number
  hasMore: boolean
}

// 代码审查结果类型
export interface CodeReviewResult {
  reviewId: string
  status: 'pending' | 'completed' | 'failed'
  issues: CodeIssue[]
  summary: {
    totalIssues: number
    criticalIssues: number
    warningIssues: number
    infoIssues: number
    score: number
  }
  createdAt: string
  completedAt?: string
}

// 代码问题类型
export interface CodeIssue {
  id: string
  type: 'security' | 'quality' | 'performance' | 'maintainability' | 'complexity'
  severity: 'critical' | 'major' | 'minor' | 'info'
  title: string
  description: string
  file: string
  line: number
  column?: number
  rule: string
  suggestion?: string
}

export default {
  getAPIConfig,
  API_ENDPOINTS,
  buildAPIURL,
  currentAPIConfig
}
