/**
 * 代码审查相关API接口
 */
import axios from '@/lib/axios'
import { API_ENDPOINTS, buildAPIURL, type APIResponse, type CodeReviewResult, type PaginatedResponse } from '../config/api'

// 代码审查接口
export const codeReviewAPI = {
  /**
   * 执行代码审查
   * @param params 代码审查参数
   */
  async reviewCode(params: {
    code?: string
    repoUrl?: string
    branch?: string
    pullRequestId?: string
    language?: string
    rules?: string[]
  }): Promise<APIResponse<CodeReviewResult>> {
    try {
      const response = await axios.post(buildAPIURL(API_ENDPOINTS.CODE_REVIEW), params, {
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 30000, // 30秒超时
      })
      return response.data
    } catch (error) {
      console.error('代码审查API调用失败:', error)
      throw error
    }
  },

  /**
   * 获取代码审查历史
   */
  async getReviewHistory(params?: {
    page?: number
    pageSize?: number
    status?: string
  }): Promise<APIResponse<PaginatedResponse<CodeReviewResult>>> {
    try {
      const response = await axios.get(buildAPIURL(API_ENDPOINTS.REVIEW_HISTORY), {
        params,
      })
      return response.data
    } catch (error) {
      console.error('获取审查历史失败:', error)
      throw error
    }
  },

  /**
   * 获取审查详情
   * @param reviewId 审查ID
   */
  async getReviewDetail(reviewId: string): Promise<APIResponse<CodeReviewResult>> {
    try {
      const response = await axios.get(buildAPIURL(`${API_ENDPOINTS.REVIEW_DETAIL}/${reviewId}`))
      return response.data
    } catch (error) {
      console.error('获取审查详情失败:', error)
      throw error
    }
  },

  /**
   * 更新审查状态
   * @param reviewId 审查ID
   * @param status 新状态
   */
  async updateReviewStatus(reviewId: string, status: string): Promise<APIResponse<any>> {
    try {
      const response = await axios.put(buildAPIURL(`${API_ENDPOINTS.REVIEW_STATUS}/${reviewId}`), {
        status,
      })
      return response.data
    } catch (error) {
      console.error('更新审查状态失败:', error)
      throw error
    }
  },

  /**
   * 测试API连接
   */
  async testConnection(): Promise<APIResponse<any>> {
    try {
      const response = await axios.get(buildAPIURL(API_ENDPOINTS.HEALTH))
      return response.data
    } catch (error) {
      console.error('API连接测试失败:', error)
      throw error
    }
  }
}

// 导出默认API实例
export default codeReviewAPI
