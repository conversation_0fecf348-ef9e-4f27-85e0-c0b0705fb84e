// 用户信息接口
export interface User {
  id: string;
  email: string;
  nickname: string;
  login_channel: string;
  avatar?: string;
  created_at?: string;
  last_login?: string;
}

// 通用API响应接口
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

// 登录状态检查响应
export interface CheckLoginResponse {
  is_logged_in: boolean;
  user: User | null;
}

// SSO配置响应
export interface SSOConfigResponse {
  client_id: string;
  env: {
    prod: {
      api_host: string;
      client_id: string;
      secret: string;
      sso_host: string;
    };
    st: {
      api_host: string;
      client_id: string;
      secret: string;
      sso_host: string;
    };
  };
}

// 认证状态接口
export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  isLoading: boolean;
  error: string | null;
  rememberMe: boolean;
}
