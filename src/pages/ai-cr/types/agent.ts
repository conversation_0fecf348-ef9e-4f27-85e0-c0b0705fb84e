// Agent系统的类型定义
export type AgentType = 
  | 'requirements' 
  | 'code-review' 
  | 'prd' 
  | 'ui-prototype' 
  | 'tech-doc' 
  | 'api-doc';

export interface AgentProps {
  visible: boolean;
  onClose: () => void;
  initialData?: any;
  onComplete?: (result: any) => void;
}

export interface ActiveAgent {
  type: AgentType;
  data?: any;
  displayMode: 'modal' | 'drawer' | 'inline' | 'sidebar';
}

export interface AgentConfig {
  type: AgentType;
  title: string;
  description: string;
  keywords: string[];
  component: React.ComponentType<AgentProps>;
}

// 聊天消息接口
export interface ChatMessage {
  id: string;
  type: 'user' | 'ai' | 'agent' | 'agent-info';
  content: string;
  timestamp: Date;
  files?: string[];
  agentType?: AgentType;
  agentResult?: any;
  pendingAgent?: AgentType; // 用于agent-info类型消息
}

// 代码审查相关类型
export interface CodeIssue {
  id: string;
  line: number;
  type: 'error' | 'warning' | 'info';
  message: string;
  suggestion: string;
  code: string;
}

export interface CodeReviewTask {
  id: number;
  prId: string;
  title: string;
  project: string;
  repo: string;
  author: string;
  status: 'completed' | 'pending' | 'reviewing';
  priority: 'high' | 'medium' | 'low';
  createdAt: string;
  fromBranch: string;
  toBranch: string;
  issuesCount: number;
  reviewDetails?: {
    summary: string;
    totalFiles: number;
    checkedFiles: number;
    issues: Array<{
      id: number;
      file: string;
      line: number;
      type: string;
      level: string;
      title: string;
      description: string;
      problemCode: string;
      suggestion: string;
      fixedCode?: string;
    }>;
  };
}
