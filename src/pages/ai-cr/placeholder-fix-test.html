<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Placeholder文本对齐修复测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            margin: 0;
            padding: 40px;
        }

        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.9);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
        }

        .test-title {
            font-size: 24px;
            font-weight: 700;
            color: #1a1a1a;
            margin-bottom: 30px;
            text-align: center;
        }

        .test-section {
            margin-bottom: 40px;
        }

        .test-subtitle {
            font-size: 18px;
            font-weight: 600;
            color: #475569;
            margin: 30px 0 15px 0;
        }

        .form-group {
            margin-bottom: 24px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: 600;
            color: #1a1a1a;
        }

        /* 模拟MTD组件的默认样式 */
        .mtd-input {
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            padding: 8px 12px;
            font-size: 14px;
            background: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            height: 32px;
            line-height: 1.5;
        }

        /* 修复后的增强输入框样式 */
        .enhanced-input {
            width: 100% !important;
            height: 48px !important;
            padding: 0 16px !important;
            border: 2px solid rgba(226, 232, 240, 0.4) !important;
            border-radius: 16px !important;
            font-size: 14px !important;
            font-weight: 500 !important;
            background: rgba(255, 255, 255, 0.95) !important;
            transition: all 0.2s ease !important;
            outline: none !important;
            box-shadow: none !important;
            line-height: 48px !important;
            min-height: 48px !important;
            max-height: 48px !important;
            color: #1a1a1a !important;
            vertical-align: top !important;
            box-sizing: border-box !important;
            margin: 0 !important;
        }

        .enhanced-input:focus {
            border-color: #667eea !important;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1) !important;
            background: rgba(255, 255, 255, 1) !important;
        }

        .enhanced-input:hover {
            border-color: #667eea !important;
        }

        .enhanced-input::placeholder {
            color: #64748b !important;
            font-weight: 400 !important;
            line-height: 48px !important;
            vertical-align: top !important;
        }

        /* 对比区域 */
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 20px;
        }

        .comparison-item {
            padding: 20px;
            border-radius: 12px;
            border: 1px solid rgba(226, 232, 240, 0.4);
        }

        .comparison-item h4 {
            margin: 0 0 15px 0;
            font-size: 16px;
            font-weight: 600;
        }

        .old-style {
            background: #fef2f2;
            border-color: #fecaca;
        }

        .new-style {
            background: #f0fdf4;
            border-color: #bbf7d0;
        }

        .fix-info {
            background: rgba(102, 126, 234, 0.1);
            border: 1px solid rgba(102, 126, 234, 0.2);
            border-radius: 12px;
            padding: 20px;
            margin-top: 30px;
        }

        .fix-info h3 {
            margin: 0 0 10px 0;
            color: #667eea;
            font-size: 16px;
        }

        .fix-info ul {
            margin: 0;
            padding-left: 20px;
            color: #475569;
        }

        .test-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }

        @media (max-width: 768px) {
            .comparison,
            .test-grid {
                grid-template-columns: 1fr;
            }
        }

        /* 特殊测试：不同长度的placeholder */
        .placeholder-test {
            margin-top: 30px;
        }

        .placeholder-short::placeholder {
            content: "短";
        }

        .placeholder-long::placeholder {
            content: "这是一个很长的placeholder文本用来测试对齐";
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">Placeholder文本对齐修复测试</h1>
        
        <div class="test-section">
            <div class="test-subtitle">修复前后对比</div>
            <div class="comparison">
                <div class="comparison-item old-style">
                    <h4>修复前（文本偏移）</h4>
                    <input type="text" class="mtd-input" placeholder="placeholder文本可能偏移" value="">
                    <br><br>
                    <input type="text" class="mtd-input" placeholder="输入一些文本看看对齐" value="实际输入的文本">
                </div>
                <div class="comparison-item new-style">
                    <h4>修复后（完美对齐）</h4>
                    <input type="text" class="enhanced-input" placeholder="placeholder文本完美对齐" value="">
                    <br><br>
                    <input type="text" class="enhanced-input" placeholder="输入一些文本看看对齐" value="实际输入的文本">
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-subtitle">不同长度placeholder测试</div>
            <div class="test-grid">
                <div class="form-group">
                    <label class="form-label">短placeholder</label>
                    <input type="text" class="enhanced-input" placeholder="短文本" value="">
                </div>
                <div class="form-group">
                    <label class="form-label">长placeholder</label>
                    <input type="text" class="enhanced-input" placeholder="这是一个很长的placeholder文本用来测试文本对齐是否正常" value="">
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-subtitle">不同输入类型测试</div>
            <div class="form-group">
                <label class="form-label">文本输入</label>
                <input type="text" class="enhanced-input" placeholder="请输入您的姓名" value="">
            </div>
            <div class="form-group">
                <label class="form-label">邮箱输入</label>
                <input type="email" class="enhanced-input" placeholder="请输入您的邮箱地址" value="">
            </div>
            <div class="form-group">
                <label class="form-label">密码输入</label>
                <input type="password" class="enhanced-input" placeholder="请输入您的密码" value="">
            </div>
            <div class="form-group">
                <label class="form-label">URL输入</label>
                <input type="url" class="enhanced-input" placeholder="https://example.com" value="">
            </div>
        </div>

        <div class="fix-info">
            <h3>🔧 Placeholder对齐修复要点</h3>
            <ul>
                <li><strong>line-height: 48px</strong> - 与输入框高度一致</li>
                <li><strong>vertical-align: top</strong> - 确保垂直对齐</li>
                <li><strong>统一字体样式</strong> - placeholder与输入文本使用相同字体</li>
                <li><strong>内联样式保护</strong> - 防止被其他样式覆盖</li>
                <li><strong>CSS和内联双重保险</strong> - 确保在任何环境下都正常</li>
            </ul>
        </div>
    </div>
</body>
</html>
