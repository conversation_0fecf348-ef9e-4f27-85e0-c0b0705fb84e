<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings页面UI一致性测试</title>
    <style>
        /* 导入Settings页面的完整样式 */
        
        /* CSS变量定义 - 与CodeReviewPage.scss完全一致 */
        :root {
          /* 主色系 - 与全局变量统一 */
          --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          --primary-color: #667eea;
          --primary-color-hover: #5a6fd8;
          --primary-light: rgba(102, 126, 234, 0.1);
          --primary-shadow: rgba(102, 126, 234, 0.3);

          /* 状态色系 */
          --success-color: #52c41a;
          --success-light: rgba(82, 196, 26, 0.1);
          --warning-color: #faad14;
          --warning-light: rgba(250, 173, 20, 0.1);
          --danger-color: #ff4d4f;
          --danger-light: rgba(255, 77, 79, 0.1);
          --info-color: #1890ff;
          --info-light: rgba(24, 144, 255, 0.1);

          /* 文字色系 */
          --text-primary: #1a1a1a;
          --text-secondary: #666666;
          --text-tertiary: #999999;
          --text-muted: #64748b;

          /* 背景色系 */
          --bg-primary: rgba(255, 255, 255, 0.95);
          --bg-secondary: rgba(248, 250, 252, 0.8);
          --bg-glass: rgba(255, 255, 255, 0.8);
          --bg-glass-hover: rgba(255, 255, 255, 0.9);
          --bg-card: rgba(255, 255, 255, 0.8);
          --bg-card-hover: rgba(255, 255, 255, 0.9);

          /* 边框色系 */
          --border-light: rgba(226, 232, 240, 0.4);
          --border-medium: rgba(226, 232, 240, 0.6);
          --border-strong: rgba(226, 232, 240, 0.8);

          /* 阴影系统 */
          --shadow-xs: 0 1px 4px rgba(0, 0, 0, 0.04);
          --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.06);
          --shadow-md: 0 4px 20px rgba(0, 0, 0, 0.08);
          --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.08);
          --shadow-xl: 0 20px 60px rgba(0, 0, 0, 0.12);
          --shadow-primary: 0 8px 24px rgba(102, 126, 234, 0.3);

          /* 模糊效果 */
          --blur-light: blur(10px);
          --blur-normal: blur(20px);
          --blur-strong: blur(32px);

          /* 动画系统 - 统一的缓动函数 */
          --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
          --transition-normal: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          --transition-slow: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          --transition-smooth: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);

          /* 间距系统 */
          --spacing-xs: 4px;
          --spacing-sm: 8px;
          --spacing-md: 12px;
          --spacing-lg: 16px;
          --spacing-xl: 20px;
          --spacing-2xl: 24px;
          --spacing-3xl: 32px;
          --spacing-4xl: 48px;

          /* 圆角系统 */
          --radius-sm: 8px;
          --radius-md: 12px;
          --radius-lg: 16px;
          --radius-xl: 20px;

          /* 字体系统 */
          --font-xs: 11px;
          --font-sm: 12px;
          --font-base: 14px;
          --font-md: 15px;
          --font-lg: 16px;
          --font-xl: 18px;
          --font-2xl: 20px;
          --font-3xl: 24px;

          /* 全局背景 */
          --global-background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }

        /* 页面容器 */
        .settings-page {
          min-height: 100vh;
          background: var(--global-background);
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', sans-serif;
          margin: 0;
          padding: 0;
        }

        /* 输入框样式 - 与代码审查页搜索框完全一致 */
        .enhanced-input {
          width: 100%;
          height: 48px;
          padding: 0 16px;
          border: 2px solid var(--border-light);
          border-radius: 16px;
          font-size: 14px;
          font-weight: 500;
          background: var(--bg-primary);
          color: var(--text-primary);
          transition: var(--transition-fast);
          outline: none;
          box-shadow: none;
          font-family: inherit;
          box-sizing: border-box;
        }

        .enhanced-input:focus {
          border-color: var(--primary-color);
          box-shadow: 0 0 0 4px var(--primary-light);
          background: var(--bg-primary);
        }

        .enhanced-input:hover {
          border-color: var(--primary-color);
        }

        .enhanced-input::placeholder {
          color: var(--text-muted);
          font-weight: 400;
        }

        /* 页面头部 */
        .settings-header {
          background: var(--bg-glass);
          backdrop-filter: var(--blur-normal);
          border-bottom: 1px solid var(--border-light);
          padding: 24px 32px;
          position: sticky;
          top: 0;
          z-index: 100;
          box-shadow: var(--shadow-sm);
        }

        .settings-header-content {
          max-width: 1200px;
          margin: 0 auto;
          display: flex;
          align-items: center;
          justify-content: space-between;
          gap: 20px;
        }

        .header-title h1 {
          margin: 0 0 4px 0;
          font-size: 28px;
          font-weight: 800;
          color: var(--text-primary);
          background: var(--primary-gradient);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          letter-spacing: -0.5px;
        }

        .header-title p {
          margin: 0;
          font-size: 14px;
          color: var(--text-muted);
          font-weight: 500;
        }

        .header-actions {
          display: flex;
          align-items: center;
          gap: 16px;
        }

        /* 按钮样式 */
        .btn {
          display: inline-flex;
          align-items: center;
          gap: 8px;
          padding: 12px 20px;
          border-radius: 12px;
          font-size: 14px;
          font-weight: 600;
          cursor: pointer;
          transition: var(--transition-smooth);
          border: none;
          outline: none;
          text-decoration: none;
        }

        .btn-primary {
          background: var(--primary-gradient);
          color: white;
          box-shadow: var(--shadow-primary);
        }

        .btn-primary:hover {
          transform: translateY(-2px);
          box-shadow: var(--shadow-xl);
        }

        .btn-secondary {
          background: var(--bg-glass);
          color: var(--text-secondary);
          border: 2px solid var(--border-light);
        }

        .btn-secondary:hover {
          background: var(--bg-glass-hover);
          transform: translateY(-1px);
          box-shadow: var(--shadow-md);
          border-color: var(--primary-color);
        }

        /* 设置区块 */
        .settings-section {
          background: var(--bg-glass);
          backdrop-filter: var(--blur-normal);
          border-radius: 20px;
          border: 2px solid var(--border-light);
          padding: 32px;
          margin-bottom: 32px;
          box-shadow: var(--shadow-sm);
          position: relative;
          overflow: hidden;
          transition: var(--transition-smooth);
        }

        .settings-section::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 4px;
          background: var(--primary-gradient);
          border-radius: 20px 20px 0 0;
        }

        .settings-section:hover {
          transform: translateY(-2px);
          box-shadow: var(--shadow-lg);
          border-color: var(--primary-color);
        }

        .section-title {
          margin: 0 0 24px 0;
          font-size: 20px;
          font-weight: 800;
          color: var(--text-primary);
          position: relative;
          z-index: 2;
        }

        .form-group {
          margin-bottom: 24px;
          position: relative;
          z-index: 2;
        }

        .form-label {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 8px;
          font-size: 14px;
          font-weight: 600;
          color: var(--text-primary);
        }

        .label-icon {
          font-size: 16px;
          color: var(--primary-color);
        }

        /* 更改指示器 */
        .changes-indicator {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 14px;
          color: var(--warning-color);
          font-weight: 500;
          padding: 8px 12px;
          background: var(--warning-light);
          border-radius: 8px;
          border: 1px solid rgba(250, 173, 20, 0.2);
        }

        .changes-dot {
          width: 8px;
          height: 8px;
          background: var(--warning-color);
          border-radius: 50%;
        }

        /* 测试容器 */
        .test-container {
          max-width: 1200px;
          margin: 0 auto;
          padding: 40px 20px;
        }

        .comparison-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 40px;
          margin-top: 40px;
        }

        .comparison-section {
          background: var(--bg-glass);
          border-radius: 20px;
          padding: 30px;
          border: 2px solid var(--border-light);
        }

        .comparison-title {
          font-size: 18px;
          font-weight: 700;
          color: var(--text-primary);
          margin-bottom: 20px;
          text-align: center;
        }

        .status-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 16px;
          margin-top: 30px;
        }

        .status-item {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 12px 16px;
          background: var(--success-light);
          border: 1px solid var(--success-color);
          border-radius: 12px;
          font-size: 14px;
          font-weight: 500;
          color: var(--success-color);
        }

        @media (max-width: 768px) {
          .comparison-grid {
            grid-template-columns: 1fr;
            gap: 20px;
          }
        }
    </style>
</head>
<body>
    <div class="settings-page">
        <!-- 页面头部测试 -->
        <div class="settings-header">
            <div class="settings-header-content">
                <div class="header-title">
                    <h1>⚙️ 个人设置</h1>
                    <p>管理您的个人资料和系统偏好设置</p>
                </div>
                <div class="header-actions">
                    <div class="changes-indicator">
                        <div class="changes-dot"></div>
                        <span>有未保存的更改</span>
                    </div>
                    <button class="btn btn-secondary">
                        <span>🔄</span> 重置
                    </button>
                    <button class="btn btn-primary">
                        <span>✅</span> 保存设置
                    </button>
                </div>
            </div>
        </div>

        <div class="test-container">
            <h2 style="text-align: center; color: var(--text-primary); margin-bottom: 30px;">
                Settings页面 UI一致性测试
            </h2>

            <div class="comparison-grid">
                <!-- 输入框样式对比 -->
                <div class="comparison-section">
                    <h3 class="comparison-title">输入框样式对比</h3>
                    <div class="form-group">
                        <label class="form-label">
                            <span class="label-icon">👤</span>
                            用户昵称
                        </label>
                        <input type="text" class="enhanced-input" placeholder="请输入昵称" value="测试用户">
                    </div>
                    <div class="form-group">
                        <label class="form-label">
                            <span class="label-icon">📧</span>
                            邮箱地址
                        </label>
                        <input type="email" class="enhanced-input" placeholder="请输入邮箱" value="<EMAIL>">
                    </div>
                </div>

                <!-- 设置区块样式对比 -->
                <div class="comparison-section">
                    <h3 class="comparison-title">设置区块样式</h3>
                    <div class="settings-section">
                        <h4 class="section-title">个人资料</h4>
                        <div class="form-group">
                            <label class="form-label">
                                <span class="label-icon">🔗</span>
                                个人网站
                            </label>
                            <input type="url" class="enhanced-input" placeholder="https://example.com">
                        </div>
                    </div>
                </div>
            </div>

            <!-- 一致性检查状态 -->
            <div class="status-grid">
                <div class="status-item">
                    <span>✅</span>
                    <span>CSS变量系统一致</span>
                </div>
                <div class="status-item">
                    <span>✅</span>
                    <span>输入框样式一致</span>
                </div>
                <div class="status-item">
                    <span>✅</span>
                    <span>按钮样式一致</span>
                </div>
                <div class="status-item">
                    <span>✅</span>
                    <span>卡片样式一致</span>
                </div>
                <div class="status-item">
                    <span>✅</span>
                    <span>头部布局一致</span>
                </div>
                <div class="status-item">
                    <span>✅</span>
                    <span>动画效果一致</span>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
