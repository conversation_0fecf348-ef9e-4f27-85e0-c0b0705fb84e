/**
 * Rome Stone 依赖管理工具
 * 按照 Rome Stone 规范，提供依赖版本信息和使用指南
 * 避免幽灵依赖风险，确保版本一致性
 */

/**
 * Stone 依赖使用指南
 *
 * 正确的引入方式：
 *
 * // React 相关 (版本锁定)
 * import React from '@rome/stone/react'
 * import ReactDOM from '@rome/stone/react-dom'
 *
 * // React Router 相关 (版本锁定)
 * import { BrowserRouter, Switch, Route } from '@rome/stone/react-router-dom'
 * import { useHistory, useLocation } from '@rome/stone/react-router-dom'
 *
 * // MobX 状态管理
 * import { makeAutoObservable } from '@rome/stone/mobx'
 * import { observer } from '@rome/stone/mobx-react'
 *
 * // 请求库
 * import axios from '@rome/stone/axios'
 */

// 工具函数：检查依赖版本信息
export const getDependencyVersions = () => {
  return {
    react: '17.0.2', // Stone 锁定版本
    'react-dom': '17.0.2', // Stone 锁定版本
    'react-router': '5.3.4', // Stone 锁定版本
    'react-router-dom': '5.3.4', // Stone 锁定版本
    mobx: '6.12.3', // Stone 管控版本
    'mobx-react': '9.1.1', // Stone 管控版本
    axios: '0.21.1' // Stone 管控版本
  }
}

// 工具函数：验证是否使用了 Stone 依赖
export const validateStoneDependencies = () => {
  const versions = getDependencyVersions()
  console.group('🏛️ Rome Stone 依赖版本信息')
  Object.entries(versions).forEach(([name, version]) => {
    console.log(`📦 ${name}: ${version}`)
  })
  console.groupEnd()
  
  return versions
}

// 开发环境下自动验证依赖版本
if (process.env.NODE_ENV === 'development') {
  validateStoneDependencies()
}
