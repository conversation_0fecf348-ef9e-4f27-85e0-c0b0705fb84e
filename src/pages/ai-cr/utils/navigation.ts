/**
 * 导航工具函数
 * 提供路由跳转和导航相关的工具方法
 */

import { getRouteByPath, isHomePage } from '../config/routes'

/**
 * 路由跳转工具类
 */
export class NavigationHelper {
  private history: any

  constructor(history: any) {
    this.history = history
  }

  /**
   * 跳转到指定路径
   */
  navigateTo(path: string, state?: any) {
    this.history.push(path, state)
  }

  /**
   * 替换当前路径
   */
  replaceTo(path: string, state?: any) {
    this.history.replace(path, state)
  }

  /**
   * 返回上一页
   */
  goBack() {
    this.history.goBack()
  }

  /**
   * 前进到下一页
   */
  goForward() {
    this.history.goForward()
  }

  /**
   * 跳转到首页
   */
  goHome() {
    this.navigateTo('/')
  }

  /**
   * 跳转到仪表板
   */
  goToDashboard() {
    this.navigateTo('/dashboard')
  }

  /**
   * 跳转到代码审查页面
   */
  goToCodeReview() {
    this.navigateTo('/code-review')
  }

  /**
   * 跳转到规则配置页面
   */
  goToRuleConfig() {
    this.navigateTo('/rule-config')
  }

  /**
   * 跳转到设置页面
   */
  goToSettings() {
    this.navigateTo('/settings')
  }

  /**
   * 检查当前是否在首页
   */
  isCurrentlyHome(currentPath: string): boolean {
    return isHomePage(currentPath)
  }

  /**
   * 获取当前路由信息
   */
  getCurrentRouteInfo(currentPath: string) {
    return getRouteByPath(currentPath)
  }
}

/**
 * 创建导航助手实例
 */
export const createNavigationHelper = (history: any) => {
  return new NavigationHelper(history)
}

/**
 * 路由变化监听器
 */
export interface RouteChangeListener {
  (currentPath: string, previousPath?: string): void
}

/**
 * 路由变化管理器
 */
export class RouteChangeManager {
  private listeners: RouteChangeListener[] = []
  private currentPath: string = ''

  /**
   * 添加路由变化监听器
   */
  addListener(listener: RouteChangeListener) {
    this.listeners.push(listener)
  }

  /**
   * 移除路由变化监听器
   */
  removeListener(listener: RouteChangeListener) {
    const index = this.listeners.indexOf(listener)
    if (index > -1) {
      this.listeners.splice(index, 1)
    }
  }

  /**
   * 通知路由变化
   */
  notifyRouteChange(newPath: string) {
    const previousPath = this.currentPath
    this.currentPath = newPath

    this.listeners.forEach(listener => {
      listener(newPath, previousPath)
    })
  }

  /**
   * 获取当前路径
   */
  getCurrentPath(): string {
    return this.currentPath
  }
}

/**
 * 全局路由变化管理器实例
 */
export const routeChangeManager = new RouteChangeManager()

/**
 * 路由工具函数
 */
export const routeUtils = {
  /**
   * 检查路径是否匹配
   */
  isPathMatch(currentPath: string, targetPath: string, exact: boolean = true): boolean {
    if (exact) {
      return currentPath === targetPath
    }
    return currentPath.startsWith(targetPath)
  },

  /**
   * 获取路径参数
   */
  getPathParams(path: string): Record<string, string> {
    const params: Record<string, string> = {}
    const urlParams = new URLSearchParams(path.split('?')[1])
    
    urlParams.forEach((value, key) => {
      params[key] = value
    })
    
    return params
  },

  /**
   * 构建带参数的路径
   */
  buildPathWithParams(basePath: string, params: Record<string, string>): string {
    const searchParams = new URLSearchParams(params)
    const queryString = searchParams.toString()
    
    return queryString ? `${basePath}?${queryString}` : basePath
  },

  /**
   * 清理路径（移除多余的斜杠等）
   */
  cleanPath(path: string): string {
    return path.replace(/\/+/g, '/').replace(/\/$/, '') || '/'
  }
}

/**
 * 面包屑导航工具
 */
export const breadcrumbUtils = {
  /**
   * 生成面包屑路径
   */
  generateBreadcrumbs(currentPath: string) {
    const pathSegments = currentPath.split('/').filter(Boolean)
    const breadcrumbs = []

    // 添加首页
    breadcrumbs.push({
      path: '/',
      label: 'AI助手',
      icon: '🤖'
    })

    // 添加路径段
    let accumulatedPath = ''
    pathSegments.forEach(segment => {
      accumulatedPath += `/${segment}`
      const route = getRouteByPath(accumulatedPath)
      
      if (route) {
        breadcrumbs.push({
          path: route.path,
          label: route.label,
          icon: route.icon
        })
      }
    })

    return breadcrumbs
  }
}
