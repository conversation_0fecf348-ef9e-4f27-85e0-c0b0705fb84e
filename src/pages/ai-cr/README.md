# 闪购 AI 代码审查系统

基于 `sg-aicr-fe-new` 参考项目实现的企业级AI代码审查系统，完全适配现有的 Rome 框架和 MobX 状态管理模式。

## 🚀 功能特性

### 核心功能
- **AI 工作助手**: 智能聊天界面，支持自然语言交互
- **代码审查 Agent**: 基于分支对比的智能代码质量检测
- **需求分析 Agent**: 智能分析产品需求，生成用户故事和功能规格
- **PRD 生成 Agent**: 基于需求分析生成产品需求文档
- **UI 原型 Agent**: 智能生成界面原型和设计稿
- **技术文档 Agent**: 将PRD转换为技术实现方案
- **API 文档 Agent**: 自动生成API接口文档

### 页面功能
- **AI助手页面**: 聊天驱动的Agent系统，支持分栏展示结果
- **工作台**: 审查统计、最近审查、团队效率展示
- **代码审查页面**: PR任务管理、详细审查结果展示
- **规则配置**: 审查规则管理、全局设置、模板管理
- **系统设置**: 个人信息、通知设置、安全配置

## 📁 项目结构

```
src/pages/ai-cr/
├── app.tsx                 # 主应用组件
├── main.tsx               # 应用入口
├── routes/                # 路由配置
│   └── index.ts
├── pages/                 # 页面组件
│   ├── AIAssistant.tsx    # AI助手页面
│   ├── Dashboard.tsx      # 工作台
│   ├── CodeReviewPage.tsx # 代码审查页面
│   ├── Settings.tsx       # 设置页面
│   └── RuleConfig.tsx     # 规则配置页面
├── components/            # 通用组件
│   ├── Layout.tsx         # 布局组件
│   ├── ResizableSplitter.tsx # 可调整分割器
│   ├── ErrorBoundary.tsx  # 错误边界
│   ├── AgentManager.tsx   # Agent管理器
│   ├── AgentInfoCard.tsx  # Agent信息卡片
│   ├── AgentSplitView.tsx # Agent分栏视图
│   ├── auth/              # 认证组件
│   │   └── ProtectedRoute.tsx
│   ├── agents/            # Agent组件
│   │   ├── CodeReviewAgent.tsx
│   │   ├── RequirementsAgent.tsx
│   │   ├── PRDAgent.tsx
│   │   ├── UIPrototypeAgent.tsx
│   │   ├── TechDocAgent.tsx
│   │   └── APIDocAgent.tsx
│   └── results/           # 结果展示组件
│       ├── CodeReviewResultView.tsx
│       ├── RequirementsResultView.tsx
│       ├── PRDResultView.tsx
│       └── DefaultResultView.tsx
├── store/                 # 状态管理
│   ├── index.ts
│   └── authStore.ts       # 认证状态
├── types/                 # 类型定义
│   ├── agent.ts           # Agent相关类型
│   └── auth.ts            # 认证相关类型
└── styles/                # 样式文件
    ├── global.css         # 全局样式
    ├── variables.css      # CSS变量
    └── AIAssistant.css    # AI助手样式
```

## 🛠 技术栈

- **框架**: React + Rome Framework
- **状态管理**: MobX + @nibfe/mobx-loading
- **UI组件**: @ss/mtd-react (内部组件库)
- **路由**: React Router (Rome集成版本)
- **样式**: CSS + CSS Variables
- **类型**: TypeScript

## 🎨 设计系统

### 颜色系统
- **主色调**: 渐变色 `linear-gradient(135deg, #667eea 0%, #764ba2 100%)`
- **背景色**: 渐变背景 `linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)`
- **卡片背景**: 半透明白色 `rgba(255, 255, 255, 0.9)`

### 布局系统
- **页面高度**: `calc(100vh - 96px)`
- **头部高度**: `80px`
- **统一圆角**: `8px`, `12px`, `16px`
- **统一阴影**: 多层次阴影系统

### 交互设计
- **分栏布局**: 可调整的左右分栏，支持拖拽调整
- **聊天界面**: 现代化聊天UI，支持打字指示器
- **Agent系统**: 信息补充卡片 → 分栏展示结果
- **响应式**: 适配不同屏幕尺寸

## 🔧 开发指南

### 启动开发
```bash
# 进入项目目录
cd sg-aicr-fe

# 安装依赖（如果还没有安装）
pnpm install

# 启动开发服务器
pnpm serve

# 访问 AI 代码审查系统测试页面
# http://localhost:3000/ai-cr.html
```

### 快速验证
1. 启动开发服务器后，访问 `http://localhost:3000/ai-cr.html`
2. 你应该看到一个漂亮的测试页面，包含：
   - 系统标题和描述
   - 功能模块展示卡片
   - "测试代码审查 Agent" 按钮
3. 点击测试按钮，会弹出 Agent 信息卡片
4. 填写表单（PR编号、项目名称等）并点击"启动Agent"
5. 查看控制台输出和弹窗确认数据处理成功

### 构建部署
```bash
# 构建生产版本
pnpm build

# 检查代码质量
pnpm lint

# 项目健康检查
pnpm check
```

### 开发规范

1. **组件开发**
   - 使用 TypeScript 严格模式
   - 遵循 React Hooks 最佳实践
   - 使用 MobX 进行状态管理

2. **样式开发**
   - 使用 CSS Variables 保持一致性
   - 遵循 BEM 命名规范
   - 优先使用统一的设计系统

3. **Agent 开发**
   - 实现 `AgentProps` 接口
   - 支持初始数据和完成回调
   - 提供加载状态和错误处理

## 🔌 集成说明

### 与现有项目集成
1. 该系统作为独立模块集成到现有 `sg-aicr-fe` 项目中
2. 使用现有的 Rome 框架和构建配置
3. 复用现有的认证和权限系统
4. 保持与现有代码风格的一致性

### 路由配置
- 主页面: `/` (AI助手)
- 工作台: `/dashboard`
- 代码审查: `/code-review`
- 规则配置: `/rule-config`
- 系统设置: `/settings`

### API 集成
- 认证状态通过 `authStore` 管理
- 支持 SSO 集成（当前已禁用用于开发）
- 预留 API 接口用于后端集成

## 📝 使用说明

### AI助手使用
1. 在聊天框中描述需求
2. 系统自动检测意图并推荐相应Agent
3. 填写Agent所需信息
4. 查看分栏展示的结果

### 代码审查流程
1. 在AI助手中说"帮我审查代码"
2. 填写PR信息（编号、项目、分支等）
3. 选择审查模式（快速/深度/安全）
4. 查看详细的审查结果和修复建议

### 规则配置
1. 访问规则配置页面
2. 添加/编辑审查规则
3. 设置规则类型、语言、严重级别
4. 配置全局审查参数

## 🚧 开发状态

- ✅ 核心框架和布局
- ✅ 基础组件系统（修复了所有 TypeScript 和 JSX 错误）
- ✅ Agent 信息卡片组件
- ✅ 测试页面和基本功能验证
- ✅ 适配 Rome 框架和 @ss/mtd-react 组件库
- 🚧 完整的 AI助手聊天界面
- 🚧 Agent系统架构
- 🚧 代码审查Agent
- 🚧 需求分析Agent
- 🚧 工作台和代码审查页面
- 🚧 规则配置和设置页面
- 🚧 其他Agent功能（UI原型、技术文档、API文档）
- 🚧 后端API集成
- 🚧 实际代码分析功能

## 🔧 当前可用功能

### 测试页面
- 访问 `http://localhost:3000/ai-cr.html` 查看测试页面
- 点击"测试代码审查 Agent"按钮体验 Agent 信息卡片
- 填写表单并提交查看数据处理流程

### 已修复的问题
1. **TypeScript 配置问题**: 修复了 JSX 编译错误
2. **组件库适配**: 适配了 @ss/mtd-react 组件库
3. **React 导入**: 使用正确的 @rome/stone/react 导入方式
4. **图标问题**: 使用 Emoji 图标替代 @ant-design/icons
5. **表单处理**: 使用原生表单元素和 React.createElement

### 技术实现
- 使用 `React.createElement` 替代 JSX 语法避免编译问题
- 使用 Emoji 图标 (📋🐛📖🎨⚙️🔌) 替代图标库
- 适配现有的 Rome 框架和 MobX 状态管理
- 保持企业级设计风格和交互体验

## 📄 许可证

内部项目，遵循公司开发规范和许可证要求。
