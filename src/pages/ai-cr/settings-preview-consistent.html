<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设置页面预览 - 与代码审查页保持一致的设计</title>
    <style>
        /* 基础重置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
            color: #1a1a1a;
        }

        /* CSS变量定义 - 与代码审查页保持一致 */
        :root {
            /* 主色系 */
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --primary-color: #667eea;
            --primary-light: rgba(102, 126, 234, 0.1);

            /* 状态色系 */
            --success-color: #52c41a;
            --warning-color: #faad14;
            --danger-color: #ff4d4f;
            --info-color: #1890ff;

            /* 文字色系 */
            --text-primary: #1a1a1a;
            --text-secondary: #666666;
            --text-muted: #64748b;

            /* 背景色系 */
            --bg-glass: rgba(255, 255, 255, 0.8);
            --bg-card: rgba(255, 255, 255, 0.8);

            /* 边框色系 */
            --border-light: rgba(226, 232, 240, 0.4);
            --border-medium: rgba(226, 232, 240, 0.6);

            /* 阴影系统 */
            --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 20px rgba(0, 0, 0, 0.08);
            --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.08);
            --shadow-primary: 0 8px 24px rgba(102, 126, 234, 0.3);

            /* 模糊效果 */
            --blur-md: blur(20px);

            /* 动画系统 */
            --transition-normal: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-smooth: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
        }

        /* 页面容器 */
        .settings-page {
            min-height: 100vh;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }

        /* 页面头部 - 与代码审查页保持一致 */
        .settings-header {
            background: var(--bg-glass);
            backdrop-filter: var(--blur-md);
            border-bottom: 1px solid var(--border-light);
            padding: 24px 32px;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: var(--shadow-sm);
        }

        .settings-header-content {
            max-width: 2400px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 20px;
        }

        .header-title {
            flex: 1;
            min-width: 0;
        }

        .header-title h1 {
            margin: 0 0 4px 0;
            font-size: 28px;
            font-weight: 800;
            color: var(--text-primary);
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            letter-spacing: -0.5px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .header-title p {
            margin: 0;
            font-size: 14px;
            color: var(--text-muted);
            font-weight: 500;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 16px;
            flex-shrink: 0;
        }

        .action-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 20px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-normal);
            border: none;
            outline: none;
            white-space: nowrap;
            flex-shrink: 0;
        }

        .action-btn.secondary {
            background: transparent;
            color: var(--text-secondary);
            border: 1px solid var(--border-medium);
        }

        .action-btn.secondary:hover {
            background: var(--primary-light);
            color: var(--primary-color);
            border-color: var(--primary-color);
            transform: translateY(-1px);
        }

        .action-btn.primary {
            background: var(--primary-gradient);
            color: white;
            box-shadow: var(--shadow-sm);
        }

        .action-btn.primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-primary);
        }

        /* 页面内容 */
        .settings-content {
            max-width: 2400px;
            margin: 0 auto;
            padding: 32px;
            display: grid;
            grid-template-columns: 280px 1fr;
            gap: 32px;
            min-height: calc(100vh - 200px);
        }

        /* 设置导航 */
        .settings-nav {
            background: var(--bg-glass);
            backdrop-filter: var(--blur-md);
            border-radius: 20px;
            border: 1px solid var(--border-light);
            padding: 24px;
            position: sticky;
            top: 24px;
            height: fit-content;
            box-shadow: var(--shadow-sm);
        }

        .settings-nav-header {
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid var(--border-light);
        }

        .settings-nav-header h3 {
            margin: 0 0 8px 0;
            font-size: 18px;
            font-weight: 700;
            color: var(--text-primary);
        }

        .settings-nav-header p {
            margin: 0;
            font-size: 14px;
            color: var(--text-muted);
            line-height: 1.4;
        }

        .settings-nav-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        /* 导航项 */
        .settings-nav-item {
            position: relative;
            padding: 20px;
            border-radius: 16px;
            background: rgba(255, 255, 255, 0.5);
            border: 1px solid var(--border-light);
            cursor: pointer;
            transition: var(--transition-normal);
            overflow: hidden;
        }

        .settings-nav-item:hover {
            background: rgba(255, 255, 255, 0.8);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .settings-nav-item.active {
            background: var(--primary-light);
            border-color: var(--primary-color);
            box-shadow: var(--shadow-primary);
        }

        .nav-item-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
        }

        .nav-item-icon {
            width: 40px;
            height: 40px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            flex-shrink: 0;
        }

        .nav-item-title {
            font-size: 15px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .nav-item-stats {
            font-size: 11px;
            font-weight: 500;
            padding: 2px 6px;
            border-radius: 4px;
            display: inline-block;
        }

        .nav-item-description {
            font-size: 12px;
            color: var(--text-muted);
            line-height: 1.4;
        }

        /* 主内容区域 */
        .settings-main {
            background: var(--bg-glass);
            backdrop-filter: var(--blur-md);
            border-radius: 20px;
            border: 1px solid var(--border-light);
            padding: 32px;
            box-shadow: var(--shadow-sm);
            position: relative;
            overflow: hidden;
        }

        .settings-main::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--primary-gradient);
            opacity: 0.6;
        }

        .section-title {
            font-size: 20px;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 24px;
        }

        .demo-content {
            color: var(--text-muted);
            font-size: 16px;
            line-height: 1.6;
            text-align: center;
            padding: 60px 20px;
        }

        /* 强制样式优先级 */
        .settings-page * {
            box-sizing: border-box;
        }

        /* 确保样式生效的测试 */
        .settings-nav-item:hover {
            background: rgba(255, 255, 255, 0.8) !important;
            transform: translateY(-2px) !important;
            box-shadow: var(--shadow-md) !important;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .header-title h1 {
                font-size: 24px;
            }

            .action-btn {
                padding: 10px 16px;
                font-size: 13px;
            }
        }

        @media (max-width: 1024px) {
            .settings-content {
                grid-template-columns: 1fr;
                gap: 24px;
                padding: 24px;
            }

            .settings-nav {
                position: static;
            }
        }

        @media (max-width: 768px) {
            .settings-header {
                padding: 20px 24px;
            }

            .settings-header-content {
                flex-direction: column;
                align-items: flex-start;
                gap: 16px;
            }

            .header-actions {
                flex-direction: column;
                width: 100%;
                gap: 12px;
            }

            .action-btn {
                width: 100%;
                justify-content: center;
                padding: 12px 20px;
                font-size: 14px;
            }

            .settings-content {
                padding: 20px;
            }

            .header-title h1 {
                font-size: 22px;
                white-space: normal;
            }
        }
    </style>
</head>
<body>
    <div class="settings-page">
        <!-- 页面头部 -->
        <div class="settings-header">
            <div class="settings-header-content">
                <div class="header-title">
                    <h1>系统设置</h1>
                    <p>个人信息、通知偏好和系统配置</p>
                </div>
                <div class="header-actions">
                    <button class="action-btn secondary">🔄 重置</button>
                    <button class="action-btn primary">✓ 保存设置</button>
                </div>
            </div>
        </div>

        <!-- 页面内容 -->
        <div class="settings-content">
            <!-- 左侧导航 -->
            <div class="settings-nav">
                <div class="settings-nav-header">
                    <h3>设置分类</h3>
                    <p>选择要配置的设置类别</p>
                </div>

                <div class="settings-nav-list">
                    <div class="settings-nav-item active">
                        <div class="nav-item-header">
                            <div class="nav-item-icon" style="background: linear-gradient(135deg, rgba(102, 126, 234, 0.2), rgba(102, 126, 234, 0.1)); border: 1px solid rgba(102, 126, 234, 0.3);">👤</div>
                            <div>
                                <div class="nav-item-title">个人信息</div>
                                <div class="nav-item-stats" style="background: rgba(102, 126, 234, 0.15); color: #667eea;">2/3 已完成</div>
                            </div>
                        </div>
                        <div class="nav-item-description">管理个人资料和账户信息</div>
                    </div>

                    <div class="settings-nav-item">
                        <div class="nav-item-header">
                            <div class="nav-item-icon" style="background: linear-gradient(135deg, rgba(250, 173, 20, 0.2), rgba(250, 173, 20, 0.1)); border: 1px solid rgba(250, 173, 20, 0.3);">🔔</div>
                            <div>
                                <div class="nav-item-title">通知设置</div>
                                <div class="nav-item-stats" style="background: rgba(250, 173, 20, 0.15); color: #faad14;">3/4 已启用</div>
                            </div>
                        </div>
                        <div class="nav-item-description">配置通知偏好和提醒方式</div>
                    </div>

                    <div class="settings-nav-item">
                        <div class="nav-item-header">
                            <div class="nav-item-icon" style="background: linear-gradient(135deg, rgba(82, 196, 26, 0.2), rgba(82, 196, 26, 0.1)); border: 1px solid rgba(82, 196, 26, 0.3);">⚙️</div>
                            <div>
                                <div class="nav-item-title">偏好设置</div>
                                <div class="nav-item-stats" style="background: rgba(82, 196, 26, 0.15); color: #52c41a;">已配置</div>
                            </div>
                        </div>
                        <div class="nav-item-description">自定义系统行为和界面</div>
                    </div>

                    <div class="settings-nav-item">
                        <div class="nav-item-header">
                            <div class="nav-item-icon" style="background: linear-gradient(135deg, rgba(139, 92, 246, 0.2), rgba(139, 92, 246, 0.1)); border: 1px solid rgba(139, 92, 246, 0.3);">🔗</div>
                            <div>
                                <div class="nav-item-title">集成配置</div>
                                <div class="nav-item-stats" style="background: rgba(139, 92, 246, 0.15); color: #8b5cf6;">1/3 已连接</div>
                            </div>
                        </div>
                        <div class="nav-item-description">第三方服务集成配置</div>
                    </div>
                </div>
            </div>

            <!-- 右侧内容 -->
            <div class="settings-main">
                <h2 class="section-title">个人资料</h2>
                <div class="demo-content">
                    <p>🎨 与代码审查页保持一致的设计风格</p>
                    <p>统一的色彩系统、组件样式和交互效果</p>
                    <p>使用相同的React Icons图标库</p>
                    <p>保持一致的用户体验</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
