<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>全局样式修复验证</title>
    <style>
        /* 模拟全局样式加载 */
        /* CSS变量定义 - 与参考项目完全一致 */
        :root {
            /* 颜色系统 */
            --color-primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --color-primary-gradient-hover: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            --color-background-primary: #ffffff;
            --color-background-secondary: #f8fafc;
            --color-background-tertiary: #f1f5f9;
            --color-background-gradient: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            --color-text-primary: #1e293b;
            --color-text-secondary: #475569;
            --color-text-tertiary: #64748b;
            --color-border-light: #f1f5f9;
            --color-border-main: #e2e8f0;
        }

        /* 全局重置和基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            font-size: 16px;
            line-height: 1.5;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            color: var(--color-text-primary);
            background: var(--color-background-gradient);
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* 焦点样式 */
        :focus-visible {
            outline: 2px solid rgba(102, 126, 234, 0.5);
            outline-offset: 2px;
            border-radius: 6px;
        }

        /* 输入框焦点样式特殊处理 - 避免与自定义样式冲突 */
        input:focus-visible,
        textarea:focus-visible,
        select:focus-visible,
        .enhanced-input:focus-visible,
        [class*="enhanced-input"]:focus-visible {
            outline: none !important;
            outline-offset: 0 !important;
        }

        /* 全局增强输入框样式 - 统一设计系统 */
        .enhanced-input,
        input.enhanced-input {
            width: 100%;
            height: 48px;
            padding: 0 16px;
            border: 2px solid rgba(226, 232, 240, 0.4);
            border-radius: 16px;
            font-size: 14px;
            font-weight: 500;
            background: rgba(255, 255, 255, 0.95);
            transition: all 0.2s ease;
            outline: none;
            box-shadow: none;
            line-height: 44px;
            vertical-align: middle;
            display: block;
            box-sizing: border-box;
            margin: 0;
            color: var(--color-text-primary);
            font-family: inherit;
        }

        .enhanced-input:focus,
        input.enhanced-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
            background: rgba(255, 255, 255, 1);
            outline: none;
        }

        .enhanced-input:hover,
        input.enhanced-input:hover {
            border-color: #667eea;
        }

        .enhanced-input::placeholder,
        input.enhanced-input::placeholder {
            color: var(--color-text-tertiary);
            font-weight: 400;
            line-height: 44px;
            vertical-align: middle;
            opacity: 1;
        }

        /* 全局MTD组件样式重置 - 确保增强输入框正常工作 */
        .mtd-input.enhanced-input,
        .mtd-input-wrapper .enhanced-input,
        .mtd-input-affix-wrapper .enhanced-input,
        input[class*="mtd"].enhanced-input {
            border: 2px solid rgba(226, 232, 240, 0.4) !important;
            box-shadow: none !important;
            background: rgba(255, 255, 255, 0.95) !important;
            outline: none !important;
        }

        .mtd-input.enhanced-input:focus,
        .mtd-input-wrapper .enhanced-input:focus,
        .mtd-input-affix-wrapper .enhanced-input:focus,
        input[class*="mtd"].enhanced-input:focus {
            border-color: #667eea !important;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1) !important;
            background: rgba(255, 255, 255, 1) !important;
            outline: none !important;
        }

        /* 重置MTD组件容器样式 */
        .mtd-input-wrapper,
        .mtd-input-affix-wrapper,
        .mtd-input-content {
            border: none !important;
            box-shadow: none !important;
            background: transparent !important;
        }

        .mtd-input-wrapper:focus-within,
        .mtd-input-affix-wrapper:focus-within,
        .mtd-input-content:focus-within {
            border: none !important;
            box-shadow: none !important;
        }

        /* 模拟MTD组件的默认样式 */
        .mtd-input {
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            padding: 8px 12px;
            font-size: 14px;
            background: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            height: 32px;
        }

        .mtd-input:focus {
            border-color: #40a9ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        /* 页面布局 */
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 40px;
        }

        .test-title {
            font-size: 28px;
            font-weight: 700;
            color: var(--color-text-primary);
            margin-bottom: 30px;
            text-align: center;
        }

        .test-section {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            border: 1px solid rgba(226, 232, 240, 0.4);
            padding: 32px;
            margin-bottom: 32px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }

        .test-subtitle {
            font-size: 20px;
            font-weight: 600;
            color: var(--color-text-secondary);
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 24px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: 600;
            color: var(--color-text-primary);
        }

        .test-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 500;
        }

        .status-success {
            background: #f0fdf4;
            color: #166534;
            border: 1px solid #bbf7d0;
        }

        .status-warning {
            background: #fefce8;
            color: #a16207;
            border: 1px solid #fde047;
        }

        .info-box {
            background: rgba(102, 126, 234, 0.1);
            border: 1px solid rgba(102, 126, 234, 0.2);
            border-radius: 12px;
            padding: 20px;
            margin-top: 30px;
        }

        .info-box h3 {
            margin: 0 0 10px 0;
            color: #667eea;
            font-size: 16px;
        }

        .info-box ul {
            margin: 0;
            padding-left: 20px;
            color: var(--color-text-secondary);
        }

        @media (max-width: 768px) {
            .test-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">全局样式修复验证</h1>
        
        <div class="test-section">
            <h2 class="test-subtitle">✅ 全局增强输入框测试</h2>
            <div class="test-grid">
                <div class="form-group">
                    <label class="form-label">普通增强输入框</label>
                    <input type="text" class="enhanced-input" placeholder="全局样式生效测试" value="">
                </div>
                <div class="form-group">
                    <label class="form-label">模拟MTD + 增强样式</label>
                    <input type="text" class="enhanced-input mtd-input" placeholder="MTD组件样式覆盖测试" value="">
                </div>
            </div>
            
            <div class="form-group">
                <label class="form-label">长文本测试</label>
                <input type="text" class="enhanced-input" placeholder="这是一个很长的placeholder文本用来测试全局样式是否正确应用以及文本对齐是否正常" value="">
            </div>
        </div>

        <div class="test-section">
            <h2 class="test-subtitle">🔍 焦点样式测试</h2>
            <div class="test-grid">
                <div class="form-group">
                    <label class="form-label">Tab键焦点测试</label>
                    <input type="text" class="enhanced-input" placeholder="使用Tab键测试焦点样式" value="">
                </div>
                <div class="form-group">
                    <label class="form-label">鼠标点击焦点测试</label>
                    <input type="text" class="enhanced-input" placeholder="点击测试焦点样式" value="">
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2 class="test-subtitle">📊 修复状态检查</h2>
            <div style="display: flex; flex-wrap: wrap; gap: 16px; margin-bottom: 20px;">
                <span class="status-badge status-success">
                    ✓ 全局样式已加载
                </span>
                <span class="status-badge status-success">
                    ✓ 输入框样式统一
                </span>
                <span class="status-badge status-success">
                    ✓ MTD组件样式覆盖
                </span>
                <span class="status-badge status-success">
                    ✓ 焦点样式修复
                </span>
                <span class="status-badge status-success">
                    ✓ Placeholder对齐
                </span>
            </div>

            <div class="info-box">
                <h3>🎯 全局样式修复要点</h3>
                <ul>
                    <li><strong>优先级策略</strong>：全局样式 > 页面样式 > 组件样式</li>
                    <li><strong>导入顺序</strong>：在主样式文件中导入reference-global.scss</li>
                    <li><strong>焦点样式</strong>：全局覆盖:focus-visible，避免冲突</li>
                    <li><strong>MTD组件</strong>：全局重置MTD组件默认样式</li>
                    <li><strong>统一设计</strong>：所有页面使用相同的增强输入框样式</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
