import React from '@rome/stone/react'
import { <PERSON>, Button } from '@ss/mtd-react'

interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
  errorInfo?: React.ErrorInfo
}

interface ErrorBoundaryProps {
  children: React.ReactNode
}

class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo)
    this.setState({
      error,
      errorInfo
    })
  }

  handleReload = () => {
    window.location.reload()
  }

  handleReset = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined })
  }

  render() {
    if (this.state.hasError) {
      return (
        <div style={{
          padding: '40px 20px',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '400px',
          background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)'
        }}>
          <Card style={{
            maxWidth: '600px',
            width: '100%',
            padding: '32px',
            borderRadius: '12px',
            textAlign: 'center',
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)'
          }}>
            <div style={{
              fontSize: '48px',
              marginBottom: '16px'
            }}>
              😵
            </div>
            
            <h2 style={{
              color: '#1a1a1a',
              fontSize: '24px',
              fontWeight: 600,
              marginBottom: '12px'
            }}>
              页面出现错误
            </h2>
            
            <p style={{
              color: '#666',
              fontSize: '16px',
              marginBottom: '24px',
              lineHeight: 1.6
            }}>
              抱歉，页面遇到了一些问题。请尝试刷新页面或联系技术支持。
            </p>

            {process.env.NODE_ENV === 'development' && this.state.error && (
              <div style={{
                background: '#f5f5f5',
                padding: '16px',
                borderRadius: '8px',
                marginBottom: '24px',
                textAlign: 'left',
                fontSize: '14px',
                fontFamily: 'monospace',
                color: '#d32f2f',
                overflow: 'auto',
                maxHeight: '200px'
              }}>
                <strong>错误详情：</strong>
                <br />
                {this.state.error.message}
                {this.state.errorInfo && (
                  <>
                    <br />
                    <br />
                    <strong>组件堆栈：</strong>
                    <pre style={{ whiteSpace: 'pre-wrap', margin: 0 }}>
                      {this.state.errorInfo.componentStack}
                    </pre>
                  </>
                )}
              </div>
            )}

            <div style={{
              display: 'flex',
              gap: '12px',
              justifyContent: 'center'
            }}>
              <Button
                type="primary"
                onClick={this.handleReload}
                style={{
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  border: 'none',
                  borderRadius: '6px',
                  padding: '8px 24px'
                }}
              >
                刷新页面
              </Button>
              
              <Button
                onClick={this.handleReset}
                style={{
                  borderRadius: '6px',
                  padding: '8px 24px'
                }}
              >
                重试
              </Button>
            </div>
          </Card>
        </div>
      )
    }

    return this.props.children
  }
}

export default ErrorBoundary
