// Supercons图标组件 - 基于Supercons图标库
import React from '@rome/stone/react'

interface SuperconsIconProps {
  name: string
  size?: number
  color?: string
  className?: string
  style?: React.CSSProperties
}

// Supercons SVG图标映射
const SuperconsIcons: Record<string, string> = {
  // 主要功能图标
  'search': `<svg viewBox="0 0 24 24" fill="currentColor"><path d="M10 2a8 8 0 105.293 14.293l4.854 4.853a1 1 0 001.414-1.414l-4.853-4.854A8 8 0 0010 2zm0 2a6 6 0 110 12 6 6 0 010-12z"/></svg>`,
  'document': `<svg viewBox="0 0 24 24" fill="currentColor"><path d="M6 2a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8l-6-6H6zm0 2h7v5a1 1 0 001 1h5v10H6V4zm9 1.414L17.586 8H15V5.414z"/></svg>`,
  'palette': `<svg viewBox="0 0 24 24" fill="currentColor"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10c1.19 0 2-.81 2-2 0-.52-.2-.98-.52-1.32-.3-.32-.48-.76-.48-1.18 0-1.1.9-2 2-2h2.5c3.31 0 6-2.69 6-6 0-5.52-4.48-10-10-10zM7.5 13c-.83 0-1.5-.67-1.5-1.5S6.67 10 7.5 10s1.5.67 1.5 1.5S8.33 13 7.5 13zm3-4C9.67 9 9 8.33 9 7.5S9.67 6 10.5 6s1.5.67 1.5 1.5S11.33 9 10.5 9zm3 0c-.83 0-1.5-.67-1.5-1.5S12.67 6 13.5 6s1.5.67 1.5 1.5S14.33 9 13.5 9zm3 4c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5z"/></svg>`,
  'code': `<svg viewBox="0 0 24 24" fill="currentColor"><path d="M8.7 15.9L4.8 12l3.9-3.9a1 1 0 00-1.4-1.4l-4.6 4.6a1 1 0 000 1.4l4.6 4.6a1 1 0 001.4-1.4zM15.3 8.1L19.2 12l-3.9 3.9a1 1 0 001.4 1.4l4.6-4.6a1 1 0 000-1.4l-4.6-4.6a1 1 0 00-1.4 1.4z"/></svg>`,
  'link': `<svg viewBox="0 0 24 24" fill="currentColor"><path d="M10 13a5 5 0 007.54.54l3-3a5 5 0 00-7.07-7.07l-1.72 1.71a1 1 0 001.42 1.42l1.71-1.71a3 3 0 014.24 4.24l-3 3a3 3 0 01-4.24 0 1 1 0 00-1.42 1.42z"/><path d="M14 11a5 5 0 00-7.54-.54l-3 3a5 5 0 007.07 7.07l1.72-1.71a1 1 0 00-1.42-1.42l-1.71 1.71a3 3 0 01-4.24-4.24l3-3a3 3 0 014.24 0 1 1 0 001.42-1.42z"/></svg>`,
  'check': `<svg viewBox="0 0 24 24" fill="currentColor"><path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z"/></svg>`,
  
  // 界面元素图标
  'robot': `<svg viewBox="0 0 24 24" fill="currentColor"><path d="M12 2a2 2 0 012 2c0 .74-.4 1.39-1 1.73V7h1a4 4 0 014 4v7a2 2 0 01-2 2H8a2 2 0 01-2-2v-7a4 4 0 014-4h1V5.73c-.6-.34-1-.99-1-1.73a2 2 0 012-2zM8 9a2 2 0 00-2 2v7h12v-7a2 2 0 00-2-2H8zm2 3a1 1 0 110 2 1 1 0 010-2zm4 0a1 1 0 110 2 1 1 0 010-2z"/></svg>`,
  'user': `<svg viewBox="0 0 24 24" fill="currentColor"><path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/></svg>`,
  'upload': `<svg viewBox="0 0 24 24" fill="currentColor"><path d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8l-6-6zm4 18H6V4h7v5h5v11zm-3-7l-3-3-3 3h2v4h2v-4h2z"/></svg>`,
  'send': `<svg viewBox="0 0 24 24" fill="currentColor"><path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2 .01 7z"/></svg>`,
  'close': `<svg viewBox="0 0 24 24" fill="currentColor"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"/></svg>`,
  'more': `<svg viewBox="0 0 24 24" fill="currentColor"><path d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/></svg>`,
  
  // 分类标题图标
  'tools': `<svg viewBox="0 0 24 24" fill="currentColor"><path d="M22.7 19l-9.1-9.1c.9-2.3.4-5-1.5-6.9-2-2-5-2.4-7.4-1.3L9 6 6 9 1.6 4.7C.4 7.1.9 10.1 2.9 12.1c1.9 1.9 4.6 2.4 6.9 1.5l9.1 9.1c.4.4 1 .4 1.4 0l2.3-2.3c.5-.4.5-1.1.1-1.4z"/></svg>`,
  'lightning': `<svg viewBox="0 0 24 24" fill="currentColor"><path d="M7 2v11h3v9l7-12h-4l4-8z"/></svg>`,
  'clipboard': `<svg viewBox="0 0 24 24" fill="currentColor"><path d="M19 3h-4.18C14.4 1.84 13.3 1 12 1c-1.3 0-2.4.84-2.82 2H5a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2V5a2 2 0 00-2-2zm-7 0a1 1 0 011 1 1 1 0 01-1 1 1 1 0 01-1-1 1 1 0 011-1zm2 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/></svg>`,
  'rocket': `<svg viewBox="0 0 24 24" fill="currentColor"><path d="M12 2l3.09 6.26L22 9l-5 4.74L18.18 21 12 17.77 5.82 21 7 13.74 2 9l6.91-.74L12 2zm0 4.24L10.5 9.5 7 10l2.5 2.36L8.82 16 12 14.1 15.18 16 14.5 12.36 17 10l-3.5-.5L12 6.24z"/></svg>`,
  
  // 状态和操作图标
  'loading': `<svg viewBox="0 0 24 24" fill="currentColor"><path d="M12 6v3l4-4-4-4v3c-4.42 0-8 3.58-8 8 0 1.57.46 3.03 1.24 4.26L6.7 14.8c-.45-.83-.7-1.79-.7-2.8 0-3.31 2.69-6 6-6zm6.76 1.74L17.3 9.2c.44.84.7 1.79.7 2.8 0 3.31-2.69 6-6 6v-3l-4 4 4 4v-3c4.42 0 8-3.58 8-8 0-1.57-.46-3.03-1.24-4.26z"/></svg>`,
  'star': `<svg viewBox="0 0 24 24" fill="currentColor"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/></svg>`,
  'heart': `<svg viewBox="0 0 24 24" fill="currentColor"><path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/></svg>`,
}

const SuperconsIcon: React.FC<SuperconsIconProps> = ({ 
  name, 
  size = 16, 
  color = 'currentColor', 
  className = '',
  style = {} 
}) => {
  const iconSvg = SuperconsIcons[name]
  
  if (!iconSvg) {
    console.warn(`Supercons icon "${name}" not found`)
    return null
  }

  return (
    <span
      className={`supercons-icon ${className}`}
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        width: size,
        height: size,
        color,
        ...style
      }}
      dangerouslySetInnerHTML={{
        __html: iconSvg.replace('currentColor', color)
      }}
    />
  )
}

export default SuperconsIcon

// 导出常用图标组件
export const SearchIcon = (props: Omit<SuperconsIconProps, 'name'>) => <SuperconsIcon name="search" {...props} />
export const DocumentIcon = (props: Omit<SuperconsIconProps, 'name'>) => <SuperconsIcon name="document" {...props} />
export const PaletteIcon = (props: Omit<SuperconsIconProps, 'name'>) => <SuperconsIcon name="palette" {...props} />
export const CodeIcon = (props: Omit<SuperconsIconProps, 'name'>) => <SuperconsIcon name="code" {...props} />
export const LinkIcon = (props: Omit<SuperconsIconProps, 'name'>) => <SuperconsIcon name="link" {...props} />
export const CheckIcon = (props: Omit<SuperconsIconProps, 'name'>) => <SuperconsIcon name="check" {...props} />
export const RobotIcon = (props: Omit<SuperconsIconProps, 'name'>) => <SuperconsIcon name="robot" {...props} />
export const UserIcon = (props: Omit<SuperconsIconProps, 'name'>) => <SuperconsIcon name="user" {...props} />
export const UploadIcon = (props: Omit<SuperconsIconProps, 'name'>) => <SuperconsIcon name="upload" {...props} />
export const SendIcon = (props: Omit<SuperconsIconProps, 'name'>) => <SuperconsIcon name="send" {...props} />
export const CloseIcon = (props: Omit<SuperconsIconProps, 'name'>) => <SuperconsIcon name="close" {...props} />
export const MoreIcon = (props: Omit<SuperconsIconProps, 'name'>) => <SuperconsIcon name="more" {...props} />
export const ToolsIcon = (props: Omit<SuperconsIconProps, 'name'>) => <SuperconsIcon name="tools" {...props} />
export const LightningIcon = (props: Omit<SuperconsIconProps, 'name'>) => <SuperconsIcon name="lightning" {...props} />
export const ClipboardIcon = (props: Omit<SuperconsIconProps, 'name'>) => <SuperconsIcon name="clipboard" {...props} />
export const RocketIcon = (props: Omit<SuperconsIconProps, 'name'>) => <SuperconsIcon name="rocket" {...props} />