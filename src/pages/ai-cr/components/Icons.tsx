// 统一图标组件 - React Icons系统
import React from '@rome/stone/react'
import {
  HiOutlineCpuChip,
  HiOutlineDocument,
  HiOutlineDocumentText,
  HiOutlinePaintBrush,
  HiOutlineQrCode,
  HiOutlineLink,
  HiOutlineUser,
  HiOutlinePlay,
  HiOutlinePaperClip,
  HiOutlineAdjustmentsHorizontal,
  HiOutlineEllipsisHorizontal,
  HiOutlineSquares2X2,
  HiOutlineXMark,
  HiOutlineMagnifyingGlass,
  HiOutlineExclamationTriangle,
  HiOutlineInformationCircle,
  HiOutlineCheckCircle,
  HiOutlinePlusCircle,
  HiOutlinePencilSquare,
  HiOutlineTrash,
  HiOutlineClipboard,
  HiOutlineArrowDownTray,
  HiOutlineShare,
  HiOutlineArrowPath,
  HiOutlineChevronDown,
  HiOutlineArrowTrendingUp,
  HiOutlineArrowTrendingDown,
  HiOutlineMinus
} from 'react-icons/hi2'

interface IconProps {
  size?: number
  color?: string
  className?: string
  style?: React.CSSProperties
}

// Logo 组件
export const Logo: React.FC<IconProps> = ({ size = 32, style, className }) => {
  const IconComponent = HiOutlineCpuChip as any
  return (
    <div
      className={className}
      style={{
        width: size,
        height: size,
        borderRadius: '8px',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        boxShadow: '0 2px 8px rgba(102, 126, 234, 0.3)',
        ...style
      }}
    >
      <IconComponent style={{ color: '#fff', fontSize: size * 0.5 }} />
    </div>
  )
}

// 代码审查图标
export const CodeReviewIcon: React.FC<IconProps> = ({ size = 16, color = '#eb2f96', style, className }) => {
  const IconComponent = HiOutlineMagnifyingGlass as any
  return (
    <IconComponent
      className={className}
      style={{ fontSize: size, color, ...style }}
    />
  )
}

// 需求分析图标
export const RequirementsIcon: React.FC<IconProps> = ({ size = 16, color = '#1890ff', style, className }) => {
  const IconComponent = HiOutlineDocument as any
  return (
    <IconComponent
      className={className}
      style={{ fontSize: size, color, ...style }}
    />
  )
}

// PRD生成图标
export const PRDIcon: React.FC<IconProps> = ({ size = 16, color = '#52c41a', style, className }) => {
  const IconComponent = HiOutlineDocumentText as any
  return (
    <IconComponent
      className={className}
      style={{ fontSize: size, color, ...style }}
    />
  )
}

// UI原型图标
export const UIPrototypeIcon: React.FC<IconProps> = ({ size = 16, color = '#722ed1', style, className }) => {
  const IconComponent = HiOutlinePaintBrush as any
  return (
    <IconComponent
      className={className}
      style={{ fontSize: size, color, ...style }}
    />
  )
}

// 技术文档图标
export const TechDocIcon: React.FC<IconProps> = ({ size = 16, color = '#fa8c16', style, className }) => {
  const IconComponent = HiOutlineQrCode as any
  return (
    <IconComponent
      className={className}
      style={{ fontSize: size, color, ...style }}
    />
  )
}

// API文档图标
export const APIDocIcon: React.FC<IconProps> = ({ size = 16, color = '#13c2c2', style, className }) => {
  const IconComponent = HiOutlineLink as any
  return (
    <IconComponent
      className={className}
      style={{ fontSize: size, color, ...style }}
    />
  )
}

// 用户图标
export const UserIcon: React.FC<IconProps> = ({ size = 16, color = '#666', style, className }) => {
  const IconComponent = HiOutlineUser as any
  return (
    <IconComponent
      className={className}
      style={{ fontSize: size, color, ...style }}
    />
  )
}

// AI助手图标
export const AIIcon: React.FC<IconProps> = ({ size = 16, color = '#667eea', style, className }) => {
  const IconComponent = HiOutlineCpuChip as any
  return (
    <IconComponent
      className={className}
      style={{ fontSize: size, color, ...style }}
    />
  )
}

// 发送图标
export const SendIcon: React.FC<IconProps> = ({ size = 16, color = '#fff', style, className }) => {
  const IconComponent = HiOutlinePlay as any
  return (
    <IconComponent
      className={className}
      style={{ fontSize: size, color, ...style }}
    />
  )
}

// 上传图标
export const UploadIcon: React.FC<IconProps> = ({ size = 16, color = '#666', style, className }) => {
  const IconComponent = HiOutlinePaperClip as any
  return (
    <IconComponent
      className={className}
      style={{ fontSize: size, color, ...style }}
    />
  )
}

// 设置图标
export const SettingsIcon: React.FC<IconProps> = ({ size = 16, color = '#666', style, className }) => {
  const IconComponent = HiOutlineAdjustmentsHorizontal as any
  return (
    <IconComponent
      className={className}
      style={{ fontSize: size, color, ...style }}
    />
  )
}

// 更多操作图标
export const MoreIcon: React.FC<IconProps> = ({ size = 16, color = '#666', style, className }) => {
  const IconComponent = HiOutlineEllipsisHorizontal as any
  return (
    <IconComponent
      className={className}
      style={{ fontSize: size, color, ...style }}
    />
  )
}

// 状态图标
export const StatusIcons = {
  pending: ({ size = 16, color = '#faad14', style, className }: IconProps) => {
    const IconComponent = HiOutlineInformationCircle as any
    return (
      <IconComponent
        className={className}
        style={{ fontSize: size, color, ...style }}
      />
    )
  },
  reviewing: ({ size = 16, color = '#1890ff', style, className }: IconProps) => {
    const IconComponent = HiOutlineMagnifyingGlass as any
    return (
      <IconComponent
        className={className}
        style={{ fontSize: size, color, ...style }}
      />
    )
  },
  completed: ({ size = 16, color = '#52c41a', style, className }: IconProps) => {
    const IconComponent = HiOutlineCheckCircle as any
    return (
      <IconComponent
        className={className}
        style={{ fontSize: size, color, ...style }}
      />
    )
  },
  error: ({ size = 16, color = '#ff4d4f', style, className }: IconProps) => {
    const IconComponent = HiOutlineXMark as any
    return (
      <IconComponent
        className={className}
        style={{ fontSize: size, color, ...style }}
      />
    )
  },
  warning: ({ size = 16, color = '#faad14', style, className }: IconProps) => {
    const IconComponent = HiOutlineExclamationTriangle as any
    return (
      <IconComponent
        className={className}
        style={{ fontSize: size, color, ...style }}
      />
    )
  },
  info: ({ size = 16, color = '#1890ff', style, className }: IconProps) => {
    const IconComponent = HiOutlineInformationCircle as any
    return (
      <IconComponent
        className={className}
        style={{ fontSize: size, color, ...style }}
      />
    )
  },
  success: ({ size = 16, color = '#52c41a', style, className }: IconProps) => {
    const IconComponent = HiOutlineCheckCircle as any
    return (
      <IconComponent
        className={className}
        style={{ fontSize: size, color, ...style }}
      />
    )
  }
}

// 优先级图标
export const PriorityIcons = {
  high: ({ size = 16, color = '#ff4d4f', style, className }: IconProps) => {
    const IconComponent = HiOutlineExclamationTriangle as any
    return (
      <IconComponent
        className={className}
        style={{ fontSize: size, color, ...style }}
      />
    )
  },
  medium: ({ size = 16, color = '#faad14', style, className }: IconProps) => {
    const IconComponent = HiOutlineInformationCircle as any
    return (
      <IconComponent
        className={className}
        style={{ fontSize: size, color, ...style }}
      />
    )
  },
  low: ({ size = 16, color = '#52c41a', style, className }: IconProps) => {
    const IconComponent = HiOutlineCheckCircle as any
    return (
      <IconComponent
        className={className}
        style={{ fontSize: size, color, ...style }}
      />
    )
  }
}

// 文件类型图标
export const FileIcons = {
  document: ({ size = 16, color = '#666', style, className }: IconProps) => {
    const IconComponent = HiOutlineDocument as any
    return (
      <IconComponent
        className={className}
        style={{ fontSize: size, color, ...style }}
      />
    )
  },
  code: ({ size = 16, color = '#666', style, className }: IconProps) => {
    const IconComponent = HiOutlineQrCode as any
    return (
      <IconComponent
        className={className}
        style={{ fontSize: size, color, ...style }}
      />
    )
  },
  image: ({ size = 16, color = '#666', style, className }: IconProps) => {
    const IconComponent = HiOutlinePaintBrush as any
    return (
      <IconComponent
        className={className}
        style={{ fontSize: size, color, ...style }}
      />
    )
  },
  archive: ({ size = 16, color = '#666', style, className }: IconProps) => {
    const IconComponent = HiOutlineSquares2X2 as any
    return (
      <IconComponent
        className={className}
        style={{ fontSize: size, color, ...style }}
      />
    )
  }
}

// 操作图标
export const ActionIcons = {
  add: ({ size = 16, color = '#667eea', style, className }: IconProps) => {
    const IconComponent = HiOutlinePlusCircle as any
    return (
      <IconComponent
        className={className}
        style={{ fontSize: size, color, ...style }}
      />
    )
  },
  edit: ({ size = 16, color = '#667eea', style, className }: IconProps) => {
    const IconComponent = HiOutlinePencilSquare as any
    return (
      <IconComponent
        className={className}
        style={{ fontSize: size, color, ...style }}
      />
    )
  },
  delete: ({ size = 16, color = '#ff4d4f', style, className }: IconProps) => {
    const IconComponent = HiOutlineTrash as any
    return (
      <IconComponent
        className={className}
        style={{ fontSize: size, color, ...style }}
      />
    )
  },
  copy: ({ size = 16, color = '#667eea', style, className }: IconProps) => {
    const IconComponent = HiOutlineClipboard as any
    return (
      <IconComponent
        className={className}
        style={{ fontSize: size, color, ...style }}
      />
    )
  },
  download: ({ size = 16, color = '#667eea', style, className }: IconProps) => {
    const IconComponent = HiOutlineArrowDownTray as any
    return (
      <IconComponent
        className={className}
        style={{ fontSize: size, color, ...style }}
      />
    )
  },
  share: ({ size = 16, color = '#667eea', style, className }: IconProps) => {
    const IconComponent = HiOutlineShare as any
    return (
      <IconComponent
        className={className}
        style={{ fontSize: size, color, ...style }}
      />
    )
  },
  refresh: ({ size = 16, color = '#667eea', style, className }: IconProps) => {
    const IconComponent = HiOutlineArrowPath as any
    return (
      <IconComponent
        className={className}
        style={{ fontSize: size, color, ...style }}
      />
    )
  },
  search: ({ size = 16, color = '#667eea', style, className }: IconProps) => {
    const IconComponent = HiOutlineMagnifyingGlass as any
    return (
      <IconComponent
        className={className}
        style={{ fontSize: size, color, ...style }}
      />
    )
  },
  filter: ({ size = 16, color = '#667eea', style, className }: IconProps) => {
    const IconComponent = HiOutlineChevronDown as any
    return (
      <IconComponent
        className={className}
        style={{ fontSize: size, color, ...style }}
      />
    )
  },
  close: ({ size = 16, color = '#999', style, className }: IconProps) => {
    const IconComponent = HiOutlineXMark as any
    return (
      <IconComponent
        className={className}
        style={{ fontSize: size, color, ...style }}
      />
    )
  }
}

// 趋势图标
export const TrendIcons = {
  up: ({ size = 16, color = '#52c41a', style, className }: IconProps) => {
    const IconComponent = HiOutlineArrowTrendingUp as any
    return (
      <IconComponent
        className={className}
        style={{ fontSize: size, color, ...style }}
      />
    )
  },
  down: ({ size = 16, color = '#ff4d4f', style, className }: IconProps) => {
    const IconComponent = HiOutlineArrowTrendingDown as any
    return (
      <IconComponent
        className={className}
        style={{ fontSize: size, color, ...style }}
      />
    )
  },
  stable: ({ size = 16, color = '#666', style, className }: IconProps) => {
    const IconComponent = HiOutlineMinus as any
    return (
      <IconComponent
        className={className}
        style={{ fontSize: size, color, ...style }}
      />
    )
  }
}

// 导出所有图标
export const Icons = {
  Logo,
  CodeReviewIcon,
  RequirementsIcon,
  PRDIcon,
  UIPrototypeIcon,
  TechDocIcon,
  APIDocIcon,
  UserIcon,
  AIIcon,
  SendIcon,
  UploadIcon,
  SettingsIcon,
  MoreIcon,
  StatusIcons,
  PriorityIcons,
  FileIcons,
  ActionIcons,
  TrendIcons
}

export default Icons
