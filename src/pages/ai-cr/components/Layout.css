/* 现代化企业级Layout组件样式 - 奢华高级版本 */

/* CSS变量定义 */
:root {
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --primary-color: #667eea;
  --primary-light: rgba(102, 126, 234, 0.1);
  --primary-shadow: rgba(102, 126, 234, 0.3);
  --glass-bg: rgba(255, 255, 255, 0.90);
  --glass-bg-scrolled: rgba(255, 255, 255, 0.95);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-border-scrolled: rgba(255, 255, 255, 0.3);
  --text-primary: #1a1a1a;
  --text-secondary: #374151;
  --text-muted: #6b7280;
  --border-light: rgba(0, 0, 0, 0.06);
  --border-medium: rgba(0, 0, 0, 0.12);
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 20px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.12);
  --shadow-luxury: 0 20px 60px rgba(0, 0, 0, 0.12);
  --blur-sm: blur(10px);
  --blur-md: blur(20px);
  --blur-lg: blur(32px);
  --transition-smooth: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
  --transition-quick: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  --transition-fast: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

/* 主布局容器 */
.enterprise-layout {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', sans-serif;
}

/* 奢华导航栏 - 浮动玻璃态设计 */
.luxury-navbar {
  position: fixed;
  top: 0;
  left: 50%;
  transform: translateX(-50%) translateY(-100%);
  width: calc(100% - 48px);
  max-width: 2400px;
  height: 68px;
  z-index: 1000;
  margin: 16px 0;
  border-radius: 20px;
  background: var(--glass-bg);
  backdrop-filter: var(--blur-lg);
  -webkit-backdrop-filter: var(--blur-lg);
  border: 1px solid var(--glass-border);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.08),
    0 4px 20px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    inset 0 -1px 0 rgba(0, 0, 0, 0.03);
  transition: var(--transition-smooth);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  overflow: hidden;
}

/* 导航栏可见状态 */
.luxury-navbar.visible {
  transform: translateX(-50%) translateY(0);
}

/* 滚动状态下的导航栏 */
.luxury-navbar.scrolled {
  background: var(--glass-bg-scrolled);
  border-color: var(--glass-border-scrolled);
  box-shadow:
    var(--shadow-luxury),
    0 8px 32px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.4),
    inset 0 -1px 0 rgba(0, 0, 0, 0.05);
}

/* 奢华Logo区域 */
.luxury-logo {
  display: flex;
  align-items: center;
  gap: 16px;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 14px;
  transition: var(--transition-quick);
  position: relative;
  overflow: hidden;
}

.luxury-logo::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 14px;
}

.luxury-logo:hover::before {
  opacity: 1;
}

.luxury-logo:hover {
  transform: translateY(-1px) scale(1.02);
  box-shadow: 0 12px 32px rgba(102, 126, 234, 0.2);
}

/* Logo图标容器 */
.luxury-logo .logo-container {
  position: relative;
  padding: 4px;
  border-radius: 12px;
  background: var(--primary-gradient);
  box-shadow:
    0 8px 24px rgba(102, 126, 234, 0.4),
    0 4px 12px rgba(102, 126, 234, 0.2);
  transition: var(--transition-fast);
}

/* Logo文字区域 */
.luxury-logo .logo-text h4 {
  margin: 0;
  font-size: 20px;
  font-weight: 800;
  letter-spacing: -0.8px;
  background: linear-gradient(135deg, #1a1a1a 0%, #374151 50%, #667eea 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 1px 2px rgba(0,0,0,0.1);
  line-height: 1.2;
}

.luxury-logo .logo-text .subtitle {
  font-size: 11px;
  color: var(--text-muted);
  font-weight: 500;
  letter-spacing: 0.5px;
  margin-top: 2px;
  opacity: 0.8;
  line-height: 1;
}

/* 奢华导航菜单 */
.luxury-nav-menu {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 8px;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow:
    inset 0 1px 0 rgba(255, 255, 255, 0.4),
    0 2px 8px rgba(0, 0, 0, 0.04);
}

/* 奢华导航菜单项 */
.luxury-nav-item {
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border-radius: 12px;
  border: none;
  background: transparent;
  color: var(--text-secondary);
  font-size: 13px;
  font-weight: 600;
  letter-spacing: 0.3px;
  cursor: pointer;
  transition: var(--transition-quick);
  overflow: hidden;
  white-space: nowrap;
}

/* 导航菜单项悬停效果 */
.luxury-nav-item:hover {
  background: rgba(102, 126, 234, 0.1);
  color: var(--primary-color);
  transform: translateY(-0.5px) scale(1.01);
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.15);
}

/* 导航菜单项激活状态 */
.luxury-nav-item.active {
  background: var(--primary-gradient);
  color: white;
  font-weight: 700;
  transform: translateY(-1px) scale(1.02);
  box-shadow:
    0 8px 24px rgba(102, 126, 234, 0.4),
    0 4px 12px rgba(102, 126, 234, 0.2);
}

/* 激活状态的顶部光效 */
.luxury-nav-item.active::before {
  content: '';
  position: absolute;
  top: 0;
  left: 20%;
  right: 20%;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.6) 50%, transparent 100%);
}

/* 导航图标样式 */
.luxury-nav-item .nav-icon {
  font-size: 15px;
  filter: drop-shadow(0 1px 2px rgba(0,0,0,0.1));
  transition: transform 0.3s ease;
}

.luxury-nav-item:hover .nav-icon {
  transform: scale(1.1);
}

.luxury-nav-item.active .nav-label {
  text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

/* 奢华用户区域 */
.luxury-user-area {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 通知按钮 */
.luxury-user-area button {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  border: none;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-fast);
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
}

.luxury-user-area button:hover {
  background: var(--primary-light);
  transform: translateY(-1px) scale(1.05);
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.15);
}

/* 奢华用户资料 */
.luxury-user-profile {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 14px;
  border-radius: 14px;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: var(--transition-fast);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.luxury-user-profile:hover {
  background: var(--primary-light);
  border-color: rgba(102, 126, 234, 0.3);
  transform: translateY(-1px) scale(1.02);
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.2);
}

/* 用户头像 */
.luxury-user-profile .user-avatar {
  width: 32px;
  height: 32px;
  background: var(--primary-gradient);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 13px;
  font-weight: 700;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  position: relative;
}

/* 在线状态指示器 */
.luxury-user-profile .user-avatar::after {
  content: '';
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 10px;
  height: 10px;
  background: #10b981;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* 用户信息文字 */
.luxury-user-profile .user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.luxury-user-profile .user-name {
  font-size: 13px;
  font-weight: 600;
  color: var(--text-secondary);
  letter-spacing: 0.2px;
  line-height: 1;
}

.luxury-user-profile .user-status {
  font-size: 11px;
  color: var(--text-muted);
  font-weight: 500;
  letter-spacing: 0.5px;
  margin-top: 2px;
  opacity: 0.8;
  line-height: 1;
}

/* 优雅内容区域 */
.elegant-content {
  padding-top: 0; /* 移除顶部填充，让AI助手页面铺满屏幕 */
  min-height: 100vh;
  background: transparent;
}

/* 奢华内容区域 */
.luxury-content {
  margin-top: 100px;
  min-height: calc(100vh - 100px);
  padding: 0 24px;
  max-width: 3400px;
  margin-left: auto;
  margin-right: auto;
  position: relative;
}

/* 响应式设计 - 奢华版本 */
@media (max-width: 1400px) {
  .luxury-navbar {
    width: calc(100% - 32px);
    margin: 12px 0;
  }

  .luxury-content {
    padding: 0 20px;
  }
}

@media (max-width: 1200px) {
  .luxury-navbar {
    padding: 0 20px;
  }

  .luxury-logo {
    gap: 14px;
    padding: 6px 12px;
  }

  .luxury-logo .logo-text h4 {
    font-size: 18px;
  }

  .luxury-nav-item {
    padding: 8px 14px;
    font-size: 12px;
  }

  .elegant-navbar {
    padding: 0 24px;
  }

  .elegant-nav-item {
    padding: 8px 12px;
    font-size: 13px;
  }
}

@media (max-width: 1024px) {
  .luxury-navbar {
    width: calc(100% - 24px);
    padding: 0 16px;
    height: 64px;
  }

  .luxury-logo .logo-text h4 {
    font-size: 17px;
  }

  .luxury-nav-menu {
    gap: 2px;
    padding: 4px 6px;
  }

  .luxury-nav-item {
    padding: 8px 12px;
  }

  .luxury-user-area {
    gap: 8px;
  }

  .luxury-content {
    margin-top: 92px;
    padding: 0 16px;
  }
}

@media (max-width: 768px) {
  .elegant-navbar {
    padding: 0 16px;
    height: 56px;
  }

  .elegant-logo {
    gap: 10px;
    padding: 6px 8px;
  }

  .elegant-logo h4 {
    font-size: 16px;
  }

  .elegant-logo .logo-icon {
    width: 28px;
    height: 28px;
  }

  .elegant-nav-menu {
    display: none;
  }

  .elegant-user-profile {
    padding: 4px 8px;
    gap: 6px;
  }

  .elegant-user-profile .user-avatar {
    width: 24px;
    height: 24px;
    font-size: 11px;
  }

  .elegant-content {
    padding-top: 0; /* 保持与主样式一致，让AI助手页面铺满屏幕 */
  }
}

@media (max-width: 480px) {
  .luxury-navbar {
    width: calc(100% - 12px);
    padding: 0 10px;
  }

  .luxury-logo .logo-text h4 {
    font-size: 15px;
  }

  .luxury-user-profile .user-info {
    display: none;
  }

  .luxury-user-profile {
    padding: 6px;
  }
}

/* 高级动画效果 */
@keyframes luxuryFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-3px) rotate(1deg);
  }
}

@keyframes luxuryGlow {
  0%, 100% {
    box-shadow: 0 0 10px var(--primary-shadow);
  }
  50% {
    box-shadow: 0 0 30px var(--primary-shadow), 0 0 50px var(--primary-shadow);
  }
}

@keyframes luxuryShimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes luxuryPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

/* 特殊效果类 */
.luxury-floating {
  animation: luxuryFloat 4s ease-in-out infinite;
}

.luxury-glow {
  animation: luxuryGlow 3s ease-in-out infinite alternate;
}

.luxury-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent);
  background-size: 200% 100%;
  animation: luxuryShimmer 2.5s infinite;
}

.luxury-pulse {
  animation: luxuryPulse 2s ease-in-out infinite;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --glass-bg: rgba(20, 20, 20, 0.90);
    --glass-bg-scrolled: rgba(15, 15, 15, 0.95);
    --glass-border: rgba(255, 255, 255, 0.1);
    --glass-border-scrolled: rgba(255, 255, 255, 0.15);
    --text-primary: #ffffff;
    --text-secondary: #e2e8f0;
    --text-muted: #94a3b8;
    --border-light: rgba(255, 255, 255, 0.1);
    --border-medium: rgba(255, 255, 255, 0.2);
  }

  .enterprise-layout {
    background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 100%);
  }

  .luxury-nav-menu {
    background: rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.1);
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .luxury-nav-item {
    border: 2px solid var(--primary-color);
  }

  .luxury-logo {
    outline: 2px solid var(--primary-color);
  }

  .luxury-user-profile {
    border-width: 2px;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .luxury-navbar,
  .luxury-logo,
  .luxury-nav-item,
  .luxury-user-profile,
  .luxury-user-area button {
    transition: none;
    animation: none;
  }
}

/* 打印样式 */
@media print {
  .luxury-navbar {
    display: none;
  }

  .luxury-content {
    margin-top: 0;
    padding: 0;
  }
}
