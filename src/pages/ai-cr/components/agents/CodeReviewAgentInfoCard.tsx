// 代码审查信息补充卡片 - 严格遵循 sg-aicr-fe 设计规范
import React, { useState } from '@rome/stone/react'
import { observer } from '@rome/stone/mobx-react'
import { Card, Input, Select, Button } from '@ss/mtd-react'

interface CodeReviewInfo {
  pr_id: string
  project: string
  repo: string
  crMode: 'fast' | 'standard' | 'deep'
  fromBranch: string
  toBranch: string
}

interface CodeReviewAgentInfoCardProps {
  visible?: boolean
  initialData?: Partial<CodeReviewInfo>
  onComplete?: (data: CodeReviewInfo) => void
  onClose?: () => void
}

const { Option } = Select

const CodeReviewAgentInfoCard: React.FC<CodeReviewAgentInfoCardProps> = observer(({ initialData, onComplete, onClose }) => {
  const [form, setForm] = useState<CodeReviewInfo>({
    pr_id: initialData?.pr_id || '',
    project: initialData?.project || '',
    repo: initialData?.repo || '',
    crMode: initialData?.crMode || 'standard',
    fromBranch: initialData?.fromBranch || '',
    toBranch: initialData?.toBranch || ''
  })
  const [submitting, setSubmitting] = useState(false)
  const [submitCount, setSubmitCount] = useState(0)

  const handleChange = (field: keyof CodeReviewInfo, value: string) => {
    setForm(prev => ({ ...prev, [field]: value }))
  }

  const handleSelectChange = (value: any) => {
    // 确保只取值，不管传入的是字符串还是对象
    const actualValue = typeof value === 'object' && value?.value ? value.value : value
    setForm(prev => ({ ...prev, crMode: actualValue }))
  }

  const handleSubmit = () => {
    if (!form.pr_id || !form.project || !form.repo || !form.fromBranch || !form.toBranch) return

    setSubmitting(true)
    setSubmitCount(prev => prev + 1)

    // 模拟提交延迟，实际提交由父组件处理
    setTimeout(() => {
      setSubmitting(false)
      onComplete && onComplete(form)
    }, 600)
  }

  const isFormValid = form.pr_id && form.project && form.repo && form.fromBranch && form.toBranch

  return React.createElement(Card, {
    style: {
      borderRadius: '16px',
      border: '1px solid rgba(226, 232, 240, 0.6)',
      boxShadow: '0 8px 32px rgba(0, 0, 0, 0.08)',
      maxWidth: 480,
      margin: '0 auto',
      padding: '0 0 16px 0',
      background: 'rgba(255,255,255,0.98)'
    }
  },
    React.createElement('div', {
      style: {
        padding: '24px 24px 0 24px',
        borderBottom: '1px solid rgba(226, 232, 240, 0.6)',
        marginBottom: 16
      }
    },
      React.createElement('h4', {
        style: {
          margin: 0,
          fontSize: '18px',
          fontWeight: 600,
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text',
          display: 'flex',
          alignItems: 'center',
          gap: '12px'
        }
      }, '🐛 代码审查信息'),
      React.createElement('p', {
        style: {
          color: '#64748b',
          fontSize: '12px',
          margin: '4px 0 0 0',
          fontWeight: 400
        }
      }, submitCount > 0 ? '请检查信息是否正确，然后重新提交' : '请填写PR和分支信息，启动AI代码审查')
    ),
    React.createElement('div', { style: { padding: '0 24px' } },
      // 如果是重新提交，显示提示信息
      submitCount > 0 && React.createElement('div', {
        style: {
          background: 'rgba(250, 173, 20, 0.1)',
          border: '1px solid rgba(250, 173, 20, 0.3)',
          borderRadius: '8px',
          padding: '12px',
          marginBottom: '16px',
          display: 'flex',
          alignItems: 'center',
          gap: '8px'
        }
      },
        React.createElement('span', { style: { fontSize: '16px' } }, '⚠️'),
        React.createElement('div', {
          style: { fontSize: '13px', color: '#d97706' }
        }, `第${submitCount}次提交失败，请检查信息后重试`)
      ),

      React.createElement('div', { style: { marginBottom: 16 } },
        React.createElement('label', {
          style: {
            fontWeight: 600,
            fontSize: '14px',
            display: 'block',
            marginBottom: 6,
            color: !form.pr_id ? '#ef4444' : '#1a1a1a'
          }
        }, 'PR编号 *'),
        React.createElement(Input, {
          placeholder: '如：1234',
          value: form.pr_id,
          onChange: (e: any) => handleChange('pr_id', e.target.value),
          style: {
            borderColor: !form.pr_id ? '#fca5a5' : undefined
          }
        })
      ),
      React.createElement('div', { style: { marginBottom: 16 } },
        React.createElement('label', {
          style: {
            fontWeight: 600,
            fontSize: '14px',
            display: 'block',
            marginBottom: 6,
            color: !form.project ? '#ef4444' : '#1a1a1a'
          }
        }, '项目名称 *'),
        React.createElement(Input, {
          placeholder: '如：sg-aicr-fe',
          value: form.project,
          onChange: (e: any) => handleChange('project', e.target.value),
          style: {
            borderColor: !form.project ? '#fca5a5' : undefined
          }
        })
      ),
      React.createElement('div', { style: { marginBottom: 16 } },
        React.createElement('label', {
          style: {
            fontWeight: 600,
            fontSize: '14px',
            display: 'block',
            marginBottom: 6,
            color: !form.repo ? '#ef4444' : '#1a1a1a'
          }
        }, '仓库名 *'),
        React.createElement(Input, {
          placeholder: '如：ai-cr-repo',
          value: form.repo,
          onChange: (e: any) => handleChange('repo', e.target.value),
          style: {
            borderColor: !form.repo ? '#fca5a5' : undefined
          }
        })
      ),
      React.createElement('div', { style: { marginBottom: 16 } },
        React.createElement('label', { style: { fontWeight: 600, fontSize: '14px', display: 'block', marginBottom: 6 } }, '审查模式 *'),
        React.createElement(Select, {
          value: form.crMode,
          onChange: handleSelectChange,
          style: { width: '100%' }
        },
          React.createElement(Option, { value: 'fast' }, '🚀 快速审查 - 基础问题检测'),
          React.createElement(Option, { value: 'standard' }, '⚡ 标准审查 - 全面质量检查'),
          React.createElement(Option, { value: 'deep' }, '🔍 深度审查 - 详细安全分析')
        )
      ),
      React.createElement('div', { style: { marginBottom: 16 } },
        React.createElement('label', {
          style: {
            fontWeight: 600,
            fontSize: '14px',
            display: 'block',
            marginBottom: 6,
            color: !form.fromBranch ? '#ef4444' : '#1a1a1a'
          }
        }, '源分支 *'),
        React.createElement(Input, {
          placeholder: '如：feature/new-feature',
          value: form.fromBranch,
          onChange: (e: any) => handleChange('fromBranch', e.target.value),
          style: {
            borderColor: !form.fromBranch ? '#fca5a5' : undefined
          }
        })
      ),
      React.createElement('div', { style: { marginBottom: 24 } },
        React.createElement('label', {
          style: {
            fontWeight: 600,
            fontSize: '14px',
            display: 'block',
            marginBottom: 6,
            color: !form.toBranch ? '#ef4444' : '#1a1a1a'
          }
        }, '目标分支 *'),
        React.createElement(Input, {
          placeholder: '如：main 或 develop',
          value: form.toBranch,
          onChange: (e: any) => handleChange('toBranch', e.target.value),
          style: {
            borderColor: !form.toBranch ? '#fca5a5' : undefined
          }
        })
      ),

      // 表单验证提示
      !isFormValid && React.createElement('div', {
        style: {
          background: 'rgba(239, 68, 68, 0.1)',
          border: '1px solid rgba(239, 68, 68, 0.3)',
          borderRadius: '8px',
          padding: '12px',
          marginBottom: '16px',
          display: 'flex',
          alignItems: 'center',
          gap: '8px'
        }
      },
        React.createElement('span', { style: { fontSize: '16px' } }, '❌'),
        React.createElement('div', {
          style: { fontSize: '13px', color: '#dc2626' }
        }, '请填写所有必填字段')
      ),

      React.createElement('div', { style: { display: 'flex', gap: '12px', justifyContent: 'center' } },
        React.createElement(Button, {
          type: 'primary',
          onClick: handleSubmit,
          loading: submitting,
          disabled: !isFormValid || submitting,
          style: {
            background: isFormValid ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' : '#d1d5db',
            border: 'none',
            borderRadius: '8px',
            opacity: isFormValid ? 1 : 0.6
          }
        }, submitting ? '提交中...' : (submitCount > 0 ? '重新提交' : '启动审查')),
        React.createElement(Button, {
          onClick: onClose,
          disabled: submitting,
          style: {
            borderRadius: '8px',
            opacity: submitting ? 0.6 : 1
          }
        }, '取消')
      ),

      // 提交次数提示
      submitCount > 0 && React.createElement('div', {
        style: {
          textAlign: 'center',
          marginTop: '12px',
          fontSize: '12px',
          color: '#64748b'
        }
      }, `已尝试提交 ${submitCount} 次`)
    )
  )
})

export default CodeReviewAgentInfoCard
