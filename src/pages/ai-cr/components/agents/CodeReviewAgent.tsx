// 代码审查 Agent 组件 - 严格遵循 sg-aicr-fe 开发规范
import React, { useState } from '@rome/stone/react'
import { observer } from '@rome/stone/mobx-react'
import { Card, Button, Tag } from '@ss/mtd-react'
import type { AgentProps } from '../../types/agent'

interface CodeIssue {
  id: string
  line: number
  type: 'error' | 'warning' | 'info'
  message: string
  suggestion: string
  code: string
}

const CodeReviewAgent: React.FC<AgentProps> = observer(({
  visible,
  onClose,
  initialData,
  onComplete
}) => {
  const [analyzing, setAnalyzing] = useState(false)
  const [reviewResult, setReviewResult] = useState<CodeIssue[]>([])

  const handleAnalyze = async () => {
    setAnalyzing(true)

    // 模拟基于分支对比的AI代码分析过程
    setTimeout(() => {
      const mockIssues: CodeIssue[] = [
        {
          id: '1',
          line: 15,
          type: 'error',
          message: '变量未定义',
          suggestion: '请确保变量在使用前已经声明',
          code: 'const result = undefinedVariable + 1;'
        },
        {
          id: '2',
          line: 23,
          type: 'warning',
          message: '函数复杂度过高',
          suggestion: '建议将此函数拆分为多个小函数',
          code: 'function complexFunction() { ... }'
        },
        {
          id: '3',
          line: 8,
          type: 'info',
          message: '建议使用const替代let',
          suggestion: '此变量未被重新赋值，建议使用const',
          code: 'let name = "example";'
        },
        {
          id: '4',
          line: 31,
          type: 'warning',
          message: '缺少错误处理',
          suggestion: '建议添加try-catch块处理可能的异常',
          code: 'const data = JSON.parse(response);'
        },
        {
          id: '5',
          line: 42,
          type: 'info',
          message: '代码风格建议',
          suggestion: '建议使用箭头函数简化代码',
          code: 'function handler() { return value; }'
        }
      ]

      setReviewResult(mockIssues)
      setAnalyzing(false)
    }, 3000)
  }

  const handleComplete = () => {
    if (onComplete) {
      onComplete({
        ...initialData,
        issues: reviewResult,
        summary: {
          total: reviewResult.length,
          errors: reviewResult.filter(issue => issue.type === 'error').length,
          warnings: reviewResult.filter(issue => issue.type === 'warning').length,
          infos: reviewResult.filter(issue => issue.type === 'info').length
        }
      })
    }
    if (onClose) {
      onClose()
    }
  }

  const getIssueIcon = (type: string) => {
    switch (type) {
      case 'error': return '❌'
      case 'warning': return '⚠️'
      case 'info': return 'ℹ️'
      default: return 'ℹ️'
    }
  }

  const getIssueColor = (type: string) => {
    switch (type) {
      case 'error': return '#ff4d4f'
      case 'warning': return '#faad14'
      case 'info': return '#1890ff'
      default: return '#d9d9d9'
    }
  }

  return React.createElement('div', {
    style: {
      height: 'calc(100vh - 96px)',
      background: 'rgba(255, 255, 255, 0.95)',
      backdropFilter: 'blur(20px)',
      borderRadius: '16px',
      border: '1px solid rgba(226, 232, 240, 0.6)',
      boxShadow: '0 8px 32px rgba(0, 0, 0, 0.08)',
      overflow: 'hidden',
      display: 'flex',
      flexDirection: 'column',
      margin: '16px'
    }
  },
    // 头部
    React.createElement('div', {
      style: {
        background: 'rgba(255, 255, 255, 0.95)',
        backdropFilter: 'blur(20px)',
        padding: '16px 24px',
        borderBottom: '1px solid rgba(226, 232, 240, 0.6)',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }
    },
      React.createElement('div', null,
        React.createElement('h4', {
          style: {
            margin: 0,
            fontSize: '18px',
            fontWeight: 600,
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text',
            display: 'flex',
            alignItems: 'center',
            gap: '12px'
          }
        }, '🐛 代码审查Agent'),
        React.createElement('p', {
          style: {
            color: '#64748b',
            fontSize: '12px',
            margin: '4px 0 0 0',
            fontWeight: 400
          }
        }, '智能代码质量检测和优化建议')
      ),
      React.createElement(Button, {
        size: 'small',
        onClick: onClose,
        style: { color: '#666', background: 'transparent', border: 'none' }
      }, '✕')
    ),

    // 主体内容
    React.createElement('div', {
      style: {
        flex: 1,
        padding: '24px',
        overflow: 'auto'
      }
    },
      // 审查参数卡片
      React.createElement(Card, {
        title: '分支对比审查',
        style: { 
          marginBottom: '16px', 
          borderRadius: '12px',
          border: '1px solid rgba(226, 232, 240, 0.6)',
          boxShadow: '0 4px 16px rgba(0, 0, 0, 0.04)'
        }
      },
        React.createElement('div', {
          style: {
            background: 'rgba(248, 250, 252, 0.8)',
            padding: '16px',
            borderRadius: '8px',
            marginBottom: '16px'
          }
        },
          React.createElement('div', {
            style: { 
              display: 'grid', 
              gridTemplateColumns: '1fr 1fr', 
              gap: '12px', 
              fontSize: '14px' 
            }
          },
            React.createElement('div', null,
              React.createElement('span', { style: { fontWeight: 600 } }, 'PR编号: '),
              React.createElement('span', null, `#${initialData?.pr_id || 'N/A'}`)
            ),
            React.createElement('div', null,
              React.createElement('span', { style: { fontWeight: 600 } }, '项目: '),
              React.createElement('span', null, initialData?.project || 'N/A')
            ),
            React.createElement('div', null,
              React.createElement('span', { style: { fontWeight: 600 } }, '仓库: '),
              React.createElement('span', null, initialData?.repo || 'N/A')
            ),
            React.createElement('div', null,
              React.createElement('span', { style: { fontWeight: 600 } }, '审查模式: '),
              React.createElement('span', null, 
                initialData?.crMode === 'deep' ? '深度审查' :
                initialData?.crMode === 'standard' ? '标准审查' :
                initialData?.crMode === 'fast' ? '快速审查' : 'N/A'
              )
            )
          ),
          React.createElement('div', {
            style: { marginTop: '12px', fontSize: '14px' }
          },
            React.createElement('span', { style: { fontWeight: 600 } }, '分支对比: '),
            React.createElement('code', {
              style: {
                fontSize: '12px',
                background: 'rgba(102, 126, 234, 0.1)',
                padding: '2px 6px',
                borderRadius: '4px',
                color: '#667eea'
              }
            }, `${initialData?.fromBranch || 'N/A'} → ${initialData?.toBranch || 'N/A'}`)
          )
        ),

        React.createElement('div', { style: { textAlign: 'center' } },
          React.createElement('div', { style: { display: 'flex', gap: '12px', justifyContent: 'center' } },
            React.createElement(Button, {
              onClick: onClose,
              style: { borderRadius: '8px' }
            }, '取消'),
            React.createElement(Button, {
              type: 'primary',
              onClick: handleAnalyze,
              disabled: analyzing,
              loading: analyzing,
              style: {
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                border: 'none',
                borderRadius: '8px'
              }
            }, analyzing ? 'AI分析中...' : '开始分支对比审查')
          )
        )
      ),

      // 分析进度
      analyzing && React.createElement(Card, {
        style: { 
          borderRadius: '12px', 
          textAlign: 'center',
          border: '1px solid rgba(226, 232, 240, 0.6)',
          boxShadow: '0 4px 16px rgba(0, 0, 0, 0.04)'
        }
      },
        React.createElement('div', { style: { padding: '32px 0' } },
          React.createElement('div', {
            style: {
              fontSize: '48px',
              marginBottom: '16px'
            }
          }, '🐛'),
          React.createElement('h5', {
            style: {
              fontSize: '16px',
              fontWeight: 600,
              color: '#1a1a1a',
              margin: '0 0 16px 0'
            }
          }, 'AI正在审查你的代码...'),
          React.createElement('div', {
            style: {
              width: '300px',
              height: '8px',
              background: 'rgba(0, 0, 0, 0.1)',
              borderRadius: '4px',
              margin: '0 auto 16px',
              overflow: 'hidden'
            }
          },
            React.createElement('div', {
              style: {
                width: '66%',
                height: '100%',
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                borderRadius: '4px',
                animation: 'progress 2s ease-in-out infinite'
              }
            })
          ),
          React.createElement('div', {
            style: {
              textAlign: 'center',
              padding: '20px',
              color: '#64748b',
              fontSize: '14px'
            }
          }, '正在检测语法错误、性能问题、安全漏洞... 66%')
        )
      ),

      // 审查结果
      reviewResult.length > 0 && !analyzing && React.createElement('div', null,
        // 结果摘要
        React.createElement('div', {
          style: { 
            background: reviewResult.some(i => i.type === 'error') ?
              'linear-gradient(135deg, #fee2e2 0%, #fecaca 100%)' :
              'linear-gradient(135deg, #fef3c7 0%, #fde68a 100%)',
            border: `1px solid ${reviewResult.some(i => i.type === 'error') ? '#fca5a5' : '#f59e0b'}`,
            borderRadius: '8px',
            padding: '16px',
            marginBottom: '16px',
            display: 'flex',
            alignItems: 'center',
            gap: '12px'
          }
        },
          React.createElement('span', {
            style: { fontSize: '20px' }
          }, reviewResult.some(i => i.type === 'error') ? '⚠️' : 'ℹ️'),
          React.createElement('div', null,
            React.createElement('div', {
              style: { fontWeight: 600, fontSize: '14px', color: '#1a1a1a' }
            }, `代码审查完成，发现 ${reviewResult.length} 个问题`),
            React.createElement('div', {
              style: { fontSize: '12px', color: '#64748b', marginTop: '4px' }
            }, `错误: ${reviewResult.filter(i => i.type === 'error').length}，警告: ${reviewResult.filter(i => i.type === 'warning').length}，建议: ${reviewResult.filter(i => i.type === 'info').length}`)
        )
        ),

        React.createElement(Card, {
          title: '问题详情',
          style: { 
            borderRadius: '12px',
            border: '1px solid rgba(226, 232, 240, 0.6)',
            boxShadow: '0 4px 16px rgba(0, 0, 0, 0.04)'
          }
        },
          // 手动渲染列表项
          React.createElement('div', null,
            reviewResult.map((issue, index) =>
              React.createElement('div', {
                key: issue.id,
                style: {
                  padding: '16px 0',
                  borderBottom: index < reviewResult.length - 1 ? '1px solid rgba(226, 232, 240, 0.6)' : 'none'
                }
              },
                React.createElement('div', { style: { width: '100%' } },
                  React.createElement('div', {
                    style: { 
                      display: 'flex',
                      alignItems: 'center',
                      marginBottom: '12px'
                    }
                  },
                    React.createElement('span', {
                      style: { fontSize: '16px', marginRight: '8px' }
                    }, getIssueIcon(issue.type)),
                    React.createElement('span', {
                      style: {
                        fontWeight: 600,
                        fontSize: '14px',
                        color: '#1a1a1a',
                        flex: 1
                      }
                    }, `第 ${issue.line} 行: ${issue.message}`),
                    React.createElement(Tag, {
                      color: getIssueColor(issue.type),
                      style: { fontSize: '10px' }
                    }, issue.type.toUpperCase())
                  ),

                  React.createElement('div', {
                    style: {
                      background: 'rgba(248, 250, 252, 0.8)',
                      padding: '12px',
                      borderRadius: '6px',
                      marginBottom: '12px',
                      fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
                      fontSize: '12px',
                      border: '1px solid rgba(226, 232, 240, 0.6)',
                      color: '#1a1a1a'
                    }
                  }, issue.code),
                
                  React.createElement('p', {
                    style: {
                      color: '#64748b',
                      fontSize: '13px',
                      margin: 0
                    }
                  }, `💡 建议: ${issue.suggestion}`)
    )
  )
            )
          )
        ),

        React.createElement('div', { 
          style: { textAlign: 'center', marginTop: '24px' } 
        },
          React.createElement('div', { style: { display: 'flex', gap: '12px', justifyContent: 'center' } },
            React.createElement(Button, {
              onClick: () => {
                setReviewResult([])
              },
              style: { borderRadius: '8px' }
            }, '重新审查'),
            React.createElement(Button, {
              type: 'primary',
              onClick: handleComplete,
              style: {
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                border: 'none',
                borderRadius: '8px'
              }
            }, '✅ 完成审查')
          )
        )
      )
    )
  )
})

export default CodeReviewAgent