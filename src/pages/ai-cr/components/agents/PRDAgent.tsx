// PRD生成 Agent 组件 - 严格遵循 sg-aicr-fe 开发规范
import React, { useState } from '@rome/stone/react'
import { observer } from '@rome/stone/mobx-react'
import { Card, Button } from '@ss/mtd-react'
import type { AgentProps } from '../../types/agent'

interface PRDSection {
  title: string
  content: string
}

interface PRDResult {
  title: string
  version: string
  sections: PRDSection[]
}

const PRDAgent: React.FC<AgentProps> = observer(({ 
  visible, 
  onClose, 
  initialData, 
  onComplete 
}) => {
  const [generating, setGenerating] = useState(false)
  const [prdResult, setPrdResult] = useState<PRDResult | null>(null)

  const handleGenerate = () => {
    setGenerating(true)
    
    setTimeout(() => {
      const mockPRD: PRDResult = {
        title: `${initialData?.projectName || '产品'} PRD文档`,
        version: '1.0',
        sections: [
          {
            title: '1. 产品概述',
            content: '这是一个创新的产品解决方案，旨在为用户提供高效、便捷的服务体验。产品将通过先进的技术手段，解决用户在日常工作和生活中遇到的痛点问题。'
          },
          {
            title: '2. 用户需求分析',
            content: '基于前期需求分析，目标用户主要包括企业用户和个人用户。他们需要一个稳定、易用、功能完善的平台来提升工作效率和用户体验。'
          },
          {
            title: '3. 功能规格说明',
            content: '产品将包含用户管理、数据分析、权限控制等核心功能模块。每个模块都将提供完整的CRUD操作和丰富的交互体验。'
          },
          {
            title: '4. 技术架构',
            content: '采用现代化的前后端分离架构，前端使用React技术栈，后端采用Node.js，数据库使用MySQL，确保系统的稳定性和可扩展性。'
          },
          {
            title: '5. 项目里程碑',
            content: '项目分为三个阶段：第一阶段完成核心功能开发，第二阶段进行功能完善和优化，第三阶段进行测试和上线部署。'
          }
        ]
      }
      
      setPrdResult(mockPRD)
      setGenerating(false)
    }, 2000)
  }

  const handleComplete = () => {
    if (onComplete && prdResult) {
      onComplete(prdResult)
    }
    if (onClose) {
      onClose()
    }
  }

  return React.createElement('div', {
    style: {
      height: 'calc(100vh - 96px)',
      background: 'rgba(255, 255, 255, 0.95)',
      backdropFilter: 'blur(20px)',
      borderRadius: '16px',
      border: '1px solid rgba(226, 232, 240, 0.6)',
      boxShadow: '0 8px 32px rgba(0, 0, 0, 0.08)',
      overflow: 'hidden',
      display: 'flex',
      flexDirection: 'column',
      margin: '16px'
    }
  },
    // 头部
    React.createElement('div', {
      style: {
        background: 'rgba(255, 255, 255, 0.95)',
        backdropFilter: 'blur(20px)',
        padding: '16px 24px',
        borderBottom: '1px solid rgba(226, 232, 240, 0.6)',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }
    },
      React.createElement('div', null,
        React.createElement('h4', {
          style: {
            margin: 0,
            fontSize: '18px',
            fontWeight: 600,
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text',
            display: 'flex',
            alignItems: 'center',
            gap: '12px'
          }
        }, '📖 PRD生成Agent'),
        React.createElement('p', {
          style: {
            color: '#64748b',
            fontSize: '12px',
            margin: '4px 0 0 0',
            fontWeight: 400
          }
        }, '基于需求分析生成产品需求文档')
      ),
      React.createElement(Button, {
        size: 'small',
        onClick: onClose,
        style: { color: '#666', background: 'transparent', border: 'none' }
      }, '✕')
    ),

    // 主体内容
    React.createElement('div', {
      style: {
        flex: 1,
        padding: '24px',
        overflow: 'auto'
      }
    },
      // 初始状态
      !prdResult && !generating && React.createElement(Card, {
        style: { 
          borderRadius: '12px', 
          textAlign: 'center',
          border: '1px solid rgba(226, 232, 240, 0.6)',
          boxShadow: '0 4px 16px rgba(0, 0, 0, 0.04)'
        }
      },
        React.createElement('div', { style: { padding: '48px 0' } },
          React.createElement('div', {
            style: { fontSize: '64px', marginBottom: '24px' }
          }, '📖'),
          React.createElement('h4', {
            style: {
              fontSize: '18px',
              fontWeight: 600,
              color: '#1a1a1a',
              margin: '0 0 16px 0'
            }
          }, '准备生成PRD文档'),
          React.createElement('p', {
            style: {
              color: '#64748b',
              fontSize: '14px',
              margin: '0 0 32px 0',
              lineHeight: '1.6'
            }
          }, '将基于之前的需求分析结果生成详细的产品需求文档'),
          React.createElement('div', { style: { display: 'flex', gap: '12px', justifyContent: 'center' } },
            React.createElement(Button, {
              onClick: onClose,
              style: { borderRadius: '8px' }
            }, '取消'),
            React.createElement(Button, {
              type: 'primary',
              onClick: handleGenerate,
              style: {
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                border: 'none',
                borderRadius: '8px'
              }
            }, '开始生成')
          )
        )
      ),

      // 生成中状态
      generating && React.createElement(Card, {
        style: { 
          borderRadius: '12px', 
          textAlign: 'center',
          border: '1px solid rgba(226, 232, 240, 0.6)',
          boxShadow: '0 4px 16px rgba(0, 0, 0, 0.04)'
        }
      },
        React.createElement('div', { style: { padding: '48px 0' } },
          React.createElement('div', {
            style: { fontSize: '64px', marginBottom: '24px' }
          }, '📖'),
          React.createElement('h4', {
            style: {
              fontSize: '18px',
              fontWeight: 600,
              color: '#1a1a1a',
              margin: '0 0 24px 0'
            }
          }, 'AI正在生成PRD文档...'),
          React.createElement('div', {
            style: {
              width: '400px',
              height: '8px',
              background: '#f1f5f9',
              borderRadius: '4px',
              margin: '0 auto 24px',
              overflow: 'hidden'
            }
          },
            React.createElement('div', {
              style: {
                width: '75%',
                height: '100%',
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                borderRadius: '4px',
                animation: 'progress 2s ease-in-out infinite'
              }
            })
          ),
          React.createElement('p', {
            style: {
              color: '#64748b',
              fontSize: '14px',
              margin: 0
            }
          }, '正在整理需求信息，生成产品规格...')
        )
      ),

      // 生成结果
      prdResult && React.createElement('div', null,
        // 成功提示
        React.createElement('div', {
          style: {
            background: 'linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%)',
            border: '1px solid #6ee7b7',
            borderRadius: '8px',
            padding: '16px',
            marginBottom: '24px',
            display: 'flex',
            alignItems: 'center',
            gap: '12px'
          }
        },
          React.createElement('span', { style: { fontSize: '20px' } }, '✅'),
          React.createElement('div', null,
            React.createElement('div', {
              style: { fontWeight: 600, fontSize: '14px', color: '#1a1a1a' }
            }, 'PRD文档生成完成'),
            React.createElement('div', {
              style: { fontSize: '12px', color: '#64748b', marginTop: '4px' }
            }, 'AI已完成PRD文档生成，请查看内容')
          )
        ),

        // PRD文档内容
        React.createElement(Card, {
          title: prdResult.title,
          style: { 
            borderRadius: '12px',
            border: '1px solid rgba(226, 232, 240, 0.6)',
            boxShadow: '0 4px 16px rgba(0, 0, 0, 0.04)',
            marginBottom: '24px'
          }
        },
          React.createElement('div', {
            style: {
              background: 'rgba(248, 250, 252, 0.8)',
              padding: '12px 16px',
              borderRadius: '8px',
              marginBottom: '20px',
              border: '1px solid rgba(226, 232, 240, 0.6)'
            }
          },
            React.createElement('div', {
              style: { display: 'flex', justifyContent: 'space-between', alignItems: 'center' }
            },
              React.createElement('span', {
                style: { fontWeight: 600, fontSize: '14px' }
              }, `版本: ${prdResult.version}`),
              React.createElement('span', {
                style: { fontSize: '12px', color: '#64748b' }
              }, `生成时间: ${new Date().toLocaleString()}`)
            )
          ),

          React.createElement('div', { style: { maxHeight: '400px', overflowY: 'auto' } },
            prdResult.sections.map((section, index) => 
              React.createElement('div', {
                key: index,
                style: {
                  marginBottom: '24px',
                  paddingBottom: '20px',
                  borderBottom: index < prdResult.sections.length - 1 ? '1px solid rgba(226, 232, 240, 0.6)' : 'none'
                }
              },
                React.createElement('h5', {
                  style: {
                    fontSize: '16px',
                    fontWeight: 600,
                    color: '#1a1a1a',
                    margin: '0 0 12px 0'
                  }
                }, section.title),
                React.createElement('p', {
                  style: {
                    fontSize: '14px',
                    color: '#64748b',
                    lineHeight: '1.6',
                    margin: 0
                  }
                }, section.content)
              )
            )
          )
        ),

        // 操作按钮
        React.createElement('div', { 
          style: { textAlign: 'center' } 
        },
          React.createElement('div', { style: { display: 'flex', gap: '12px', justifyContent: 'center' } },
            React.createElement(Button, {
              onClick: () => setPrdResult(null),
              style: { borderRadius: '8px' }
            }, '重新生成'),
            React.createElement(Button, {
              type: 'primary',
              onClick: handleComplete,
              style: {
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                border: 'none',
                borderRadius: '8px'
              }
            }, '✅ 完成生成')
          )
        )
      )
    )
  )
})

export default PRDAgent