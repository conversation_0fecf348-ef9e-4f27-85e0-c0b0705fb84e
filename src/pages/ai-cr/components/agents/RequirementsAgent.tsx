// 需求分析 Agent 组件 - 严格遵循 sg-aicr-fe 开发规范
import React, { useState } from '@rome/stone/react'
import { observer } from '@rome/stone/mobx-react'
import { Card, Input, Button, Select, Tag } from '@ss/mtd-react'
import type { AgentProps } from '../../types/agent'

const { Option } = Select

interface UserStory {
  id: number
  role: string
  action: string
  goal: string
  priority: 'high' | 'medium' | 'low'
}

interface AnalysisResult {
  projectName: string
  projectType: string
  description: string
  userStories: UserStory[]
  functionalRequirements: string[]
  nonFunctionalRequirements: string[]
  technicalConstraints: string[]
}

const RequirementsAgent: React.FC<AgentProps> = observer(({ 
  visible, 
  onClose, 
  initialData, 
  onComplete 
}) => {
  const [currentStep, setCurrentStep] = useState(0)
  const [analyzing, setAnalyzing] = useState(false)
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null)
  const [formData, setFormData] = useState({
    projectName: '',
    projectType: '',
    description: '',
    targetUsers: ''
  })

  const steps = [
    { title: '需求输入', description: '描述你的产品需求' },
    { title: '智能分析', description: 'AI分析需求并生成结构化文档' },
    { title: '需求确认', description: '确认和完善需求分析结果' }
  ]

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleAnalyze = async () => {
    if (!formData.projectName || !formData.projectType || !formData.description) {
      return
    }

    setAnalyzing(true)
    setCurrentStep(1)
    
    // 模拟AI分析过程
    setTimeout(() => {
      const result: AnalysisResult = {
        projectName: formData.projectName,
        projectType: formData.projectType,
        description: formData.description,
        userStories: [
          {
            id: 1,
            role: '用户',
            action: '注册账号',
            goal: '使用平台服务',
            priority: 'high'
          },
          {
            id: 2,
            role: '用户',
            action: '登录系统',
            goal: '访问个人数据',
            priority: 'high'
          },
          {
            id: 3,
            role: '管理员',
            action: '管理用户',
            goal: '维护系统秩序',
            priority: 'medium'
          }
        ],
        functionalRequirements: [
          '用户注册与登录功能',
          '用户个人信息管理',
          '数据展示与分析',
          '权限管理系统'
        ],
        nonFunctionalRequirements: [
          '系统响应时间 < 2秒',
          '支持1000并发用户',
          '99.9%系统可用性',
          '数据安全与隐私保护'
        ],
        technicalConstraints: [
          '使用React前端框架',
          'Node.js后端服务',
          'MySQL数据库',
          '部署在云服务器'
        ]
      }
      
      setAnalysisResult(result)
      setAnalyzing(false)
      setCurrentStep(2)
    }, 3000)
  }

  const handleComplete = () => {
    if (onComplete && analysisResult) {
      onComplete(analysisResult)
    }
    if (onClose) {
      onClose()
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return '#ff4d4f'
      case 'medium': return '#faad14'
      case 'low': return '#52c41a'
      default: return '#d9d9d9'
    }
  }

  return React.createElement('div', {
    style: {
      height: 'calc(100vh - 96px)',
      background: 'rgba(255, 255, 255, 0.95)',
      backdropFilter: 'blur(20px)',
      borderRadius: '16px',
      border: '1px solid rgba(226, 232, 240, 0.6)',
      boxShadow: '0 8px 32px rgba(0, 0, 0, 0.08)',
      overflow: 'hidden',
      display: 'flex',
      flexDirection: 'column',
      margin: '16px'
    }
  },
    // 头部
    React.createElement('div', {
      style: {
        background: 'rgba(255, 255, 255, 0.95)',
        backdropFilter: 'blur(20px)',
        padding: '16px 24px',
        borderBottom: '1px solid rgba(226, 232, 240, 0.6)',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }
    },
      React.createElement('div', null,
        React.createElement('h4', {
          style: {
            margin: 0,
            fontSize: '18px',
            fontWeight: 600,
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text',
            display: 'flex',
            alignItems: 'center',
            gap: '12px'
          }
        }, '📋 需求分析Agent'),
        React.createElement('p', {
          style: {
            color: '#64748b',
            fontSize: '12px',
            margin: '4px 0 0 0',
            fontWeight: 400
          }
        }, '通过AI智能分析，将你的想法转化为结构化的产品需求文档')
      ),
      React.createElement(Button, {
        size: 'small',
        onClick: onClose,
        style: { color: '#666', background: 'transparent', border: 'none' }
      }, '✕')
    ),

    // 主体内容
    React.createElement('div', {
      style: {
        flex: 1,
        padding: '24px',
        overflow: 'auto'
      }
    },
      // 步骤指示器
      React.createElement('div', {
        style: {
          display: 'flex',
          justifyContent: 'center',
          marginBottom: '32px'
        }
      },
        steps.map((step, index) =>
          React.createElement('div', {
            key: index,
            style: {
              display: 'flex',
              alignItems: 'center',
              opacity: index <= currentStep ? 1 : 0.4
            }
          },
            React.createElement('div', {
              style: {
                width: '32px',
                height: '32px',
                borderRadius: '50%',
                background: index <= currentStep ?
                  'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' :
                  '#e2e8f0',
                color: 'white',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '14px',
                fontWeight: 600,
                marginRight: index < steps.length - 1 ? '8px' : '0'
              }
            }, index + 1),
            index < steps.length - 1 && React.createElement('div', {
              style: {
                width: '60px',
                height: '2px',
                background: index < currentStep ?
                  'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' :
                  '#e2e8f0',
                marginRight: '8px'
              }
            })
          )
        )
      ),

      // 步骤1：需求输入
      currentStep === 0 && React.createElement(Card, {
        title: '项目需求输入',
        style: { 
          borderRadius: '12px',
          border: '1px solid rgba(226, 232, 240, 0.6)',
          boxShadow: '0 4px 16px rgba(0, 0, 0, 0.04)'
        }
      },
        React.createElement('div', {
          style: { display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px', marginBottom: '16px' }
        },
          React.createElement('div', null,
            React.createElement('label', {
              style: { display: 'block', marginBottom: '8px', fontWeight: 600, fontSize: '14px' }
            }, '项目名称 *'),
            React.createElement(Input, {
              placeholder: '例如：在线教育平台',
              value: formData.projectName,
              onChange: (e: any) => handleInputChange('projectName', e.target.value)
            })
          ),
          React.createElement('div', null,
            React.createElement('label', {
              style: { display: 'block', marginBottom: '8px', fontWeight: 600, fontSize: '14px' }
            }, '项目类型 *'),
            React.createElement(Select, {
              placeholder: '选择项目类型',
              value: formData.projectType,
              onChange: (value: string) => handleInputChange('projectType', value),
              style: { width: '100%' }
            },
              React.createElement(Option, { value: 'web' }, 'Web应用'),
              React.createElement(Option, { value: 'mobile' }, '移动应用'),
              React.createElement(Option, { value: 'desktop' }, '桌面应用'),
              React.createElement(Option, { value: 'api' }, 'API服务')
            )
          )
        ),

        React.createElement('div', { style: { marginBottom: '16px' } },
          React.createElement('label', {
            style: { display: 'block', marginBottom: '8px', fontWeight: 600, fontSize: '14px' }
          }, '需求描述 *'),
          React.createElement('textarea', {
            placeholder: '详细描述你的产品需求，包括目标用户、主要功能、预期效果等...',
            value: formData.description,
            onChange: (e: any) => handleInputChange('description', e.target.value),
            style: {
              width: '100%',
              minHeight: '100px',
              padding: '12px',
              border: '1px solid rgba(226, 232, 240, 0.6)',
              borderRadius: '8px',
              fontSize: '14px',
              fontFamily: 'inherit',
              resize: 'vertical'
            }
          })
        ),

        React.createElement('div', { style: { marginBottom: '24px' } },
          React.createElement('label', {
            style: { display: 'block', marginBottom: '8px', fontWeight: 600, fontSize: '14px' }
          }, '目标用户'),
          React.createElement(Input, {
            placeholder: '例如：学生、教师、企业员工',
            value: formData.targetUsers,
            onChange: (e: any) => handleInputChange('targetUsers', e.target.value)
          })
        ),

        React.createElement('div', { style: { display: 'flex', gap: '12px', justifyContent: 'center' } },
          React.createElement(Button, {
            type: 'primary',
            onClick: handleAnalyze,
            disabled: !formData.projectName || !formData.projectType || !formData.description,
            style: {
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              border: 'none',
              borderRadius: '8px'
            }
          }, '开始AI分析'),
          React.createElement(Button, {
            onClick: onClose,
            style: { borderRadius: '8px' }
          }, '取消')
        )
      ),

      // 步骤2：分析中
      currentStep === 1 && React.createElement(Card, {
        style: { 
          borderRadius: '12px', 
          textAlign: 'center',
          border: '1px solid rgba(226, 232, 240, 0.6)',
          boxShadow: '0 4px 16px rgba(0, 0, 0, 0.04)'
        }
      },
        React.createElement('div', { style: { padding: '48px 0' } },
          React.createElement('div', {
            style: { fontSize: '64px', marginBottom: '24px' }
          }, '💡'),
          React.createElement('h4', {
            style: {
              fontSize: '18px',
              fontWeight: 600,
              color: '#1a1a1a',
              margin: '0 0 24px 0'
            }
          }, 'AI正在分析你的需求...'),
          React.createElement('div', {
            style: {
              width: '400px',
              height: '8px',
              background: '#f1f5f9',
              borderRadius: '4px',
              margin: '0 auto 24px',
              overflow: 'hidden'
            }
          },
            React.createElement('div', {
              style: {
                width: analyzing ? '66%' : '100%',
                height: '100%',
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                borderRadius: '4px',
                transition: 'width 0.3s ease'
              }
            })
          ),
          React.createElement('p', {
            style: {
              color: '#64748b',
              fontSize: '14px',
              margin: 0
            }
          }, '正在解析需求内容，生成用户故事，识别功能模块...')
        )
      ),

      // 步骤3：分析结果
      currentStep === 2 && analysisResult && React.createElement('div', null,
        // 成功提示
        React.createElement('div', {
          style: {
            background: 'linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%)',
            border: '1px solid #6ee7b7',
            borderRadius: '8px',
            padding: '16px',
            marginBottom: '24px',
            display: 'flex',
            alignItems: 'center',
            gap: '12px'
          }
        },
          React.createElement('span', { style: { fontSize: '20px' } }, '✅'),
          React.createElement('div', null,
            React.createElement('div', {
              style: { fontWeight: 600, fontSize: '14px', color: '#1a1a1a' }
            }, '需求分析完成'),
            React.createElement('div', {
              style: { fontSize: '12px', color: '#64748b', marginTop: '4px' }
            }, 'AI已完成需求分析，请查看并确认分析结果')
          )
        ),

        // 分析结果展示
        React.createElement('div', {
          style: { display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px', marginBottom: '24px' }
        },
          // 左侧：用户故事和技术约束
          React.createElement('div', null,
            React.createElement(Card, {
              title: '👤 用户故事',
              style: { 
                marginBottom: '16px', 
                borderRadius: '12px',
                border: '1px solid rgba(226, 232, 240, 0.6)',
                boxShadow: '0 4px 16px rgba(0, 0, 0, 0.04)'
              }
            },
              React.createElement('div', { style: { maxHeight: '200px', overflowY: 'auto' } },
                analysisResult.userStories.map((story) => 
                  React.createElement('div', {
                    key: story.id,
                    style: {
                      background: 'rgba(248, 250, 252, 0.8)',
                      padding: '12px',
                      borderRadius: '8px',
                      marginBottom: '8px',
                      border: '1px solid rgba(226, 232, 240, 0.6)'
                    }
                  },
                    React.createElement('div', {
                      style: { display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }
                    },
                      React.createElement('div', { style: { flex: 1 } },
                        React.createElement('div', {
                          style: { fontWeight: 600, fontSize: '12px', marginBottom: '4px' }
                        }, `作为 ${story.role}`),
                        React.createElement('div', {
                          style: { fontSize: '12px', marginBottom: '4px' }
                        }, `我想要 ${story.action}`),
                        React.createElement('div', {
                          style: { fontSize: '12px', color: '#64748b' }
                        }, `以便 ${story.goal}`)
                      ),
                      React.createElement(Tag, {
                        color: getPriorityColor(story.priority),
                        style: { fontSize: '10px' }
                      }, story.priority)
                    )
                  )
                )
              )
            ),

            React.createElement(Card, {
              title: '⚙️ 技术约束',
              style: { 
                borderRadius: '12px',
                border: '1px solid rgba(226, 232, 240, 0.6)',
                boxShadow: '0 4px 16px rgba(0, 0, 0, 0.04)'
              }
            },
              React.createElement('div', {
                style: { display: 'flex', flexWrap: 'wrap', gap: '8px' }
              },
                analysisResult.technicalConstraints.map((constraint, index) => 
                  React.createElement(Tag, {
                    key: index,
                    style: { borderRadius: '4px', fontSize: '11px' }
                  }, constraint)
                )
              )
            )
          ),

          // 右侧：功能需求和非功能需求
          React.createElement('div', null,
            React.createElement(Card, {
              title: '✅ 功能需求',
              style: { 
                marginBottom: '16px', 
                borderRadius: '12px',
                border: '1px solid rgba(226, 232, 240, 0.6)',
                boxShadow: '0 4px 16px rgba(0, 0, 0, 0.04)'
              }
            },
              React.createElement('div', null,
                analysisResult.functionalRequirements.map((req, index) => 
                  React.createElement('div', {
                    key: index,
                    style: {
                      display: 'flex',
                      alignItems: 'center',
                      padding: '8px 0',
                      borderBottom: index < analysisResult.functionalRequirements.length - 1 ? '1px solid rgba(226, 232, 240, 0.6)' : 'none'
                    }
                  },
                    React.createElement('span', {
                      style: { color: '#52c41a', marginRight: '8px', fontSize: '12px' }
                    }, '✅'),
                    React.createElement('span', {
                      style: { fontSize: '12px' }
                    }, req)
                  )
                )
              )
            ),

            React.createElement(Card, {
              title: '⏱️ 非功能需求',
              style: { 
                borderRadius: '12px',
                border: '1px solid rgba(226, 232, 240, 0.6)',
                boxShadow: '0 4px 16px rgba(0, 0, 0, 0.04)'
              }
            },
              React.createElement('div', null,
                analysisResult.nonFunctionalRequirements.map((req, index) => 
                  React.createElement('div', {
                    key: index,
                    style: {
                      display: 'flex',
                      alignItems: 'center',
                      padding: '8px 0',
                      borderBottom: index < analysisResult.nonFunctionalRequirements.length - 1 ? '1px solid rgba(226, 232, 240, 0.6)' : 'none'
                    }
                  },
                    React.createElement('span', {
                      style: { color: '#faad14', marginRight: '8px', fontSize: '12px' }
                    }, '⏱️'),
                    React.createElement('span', {
                      style: { fontSize: '12px' }
                    }, req)
                  )
                )
              )
            )
          )
        ),

        // 操作按钮
        React.createElement('div', { 
          style: { textAlign: 'center' } 
        },
          React.createElement('div', { style: { display: 'flex', gap: '12px', justifyContent: 'center' } },
            React.createElement(Button, {
              onClick: () => setCurrentStep(0),
              style: { borderRadius: '8px' }
            }, '重新分析'),
            React.createElement(Button, {
              type: 'primary',
              onClick: handleComplete,
              style: {
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                border: 'none',
                borderRadius: '8px'
              }
            }, '▶️ 完成分析')
          )
        )
      )
    )
  )
})

export default RequirementsAgent