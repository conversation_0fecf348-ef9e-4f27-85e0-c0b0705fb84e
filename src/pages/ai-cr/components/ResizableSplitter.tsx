// 可调整大小的分割器组件 - 适配Rome框架
import React, { useState, useCallback, useRef, useEffect } from '@rome/stone/react'

interface ResizableSplitterProps {
  leftContent: React.ReactNode
  rightContent: React.ReactNode
  defaultLeftWidth?: number
  minLeftWidth?: number
  maxLeftWidth?: number
  className?: string
  style?: React.CSSProperties
}

const ResizableSplitter: React.FC<ResizableSplitterProps> = ({
  leftContent,
  rightContent,
  defaultLeftWidth = 50, // 默认左侧占50%
  minLeftWidth = 30, // 最小30%
  maxLeftWidth = 70, // 最大70%
  className,
  style
}) => {
  const [leftWidth, setLeftWidth] = useState(defaultLeftWidth)
  const [isDragging, setIsDragging] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }, [])

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging || !containerRef.current) return

    const containerRect = containerRef.current.getBoundingClientRect()
    const newLeftWidth = ((e.clientX - containerRect.left) / containerRect.width) * 100
    
    // 限制在最小和最大宽度之间
    const clampedWidth = Math.max(minLeftWidth, Math.min(maxLeftWidth, newLeftWidth))
    setLeftWidth(clampedWidth)
  }, [isDragging, minLeftWidth, maxLeftWidth])

  const handleMouseUp = useCallback(() => {
    setIsDragging(false)
  }, [])

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      document.body.style.cursor = 'col-resize'
      document.body.style.userSelect = 'none'
    } else {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
      document.body.style.cursor = ''
      document.body.style.userSelect = ''
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
      document.body.style.cursor = ''
      document.body.style.userSelect = ''
    }
  }, [isDragging, handleMouseMove, handleMouseUp])

  return React.createElement('div', {
    ref: containerRef,
    className,
    style: {
      display: 'flex',
      height: '100%',
      width: '100%',
      ...style
    }
  },
    // 左侧面板
    React.createElement('div', {
      style: {
        width: `${leftWidth}%`,
        height: '100%',
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column'
      }
    }, leftContent),

    // 可拖动的分割线
    React.createElement('div', {
      onMouseDown: handleMouseDown,
      style: {
        width: '6px',
        height: '100%',
        background: isDragging ? 'rgba(102, 126, 234, 0.2)' : 'transparent',
        cursor: 'col-resize',
        position: 'relative',
        flexShrink: 0,
        transition: isDragging ? 'none' : 'all 0.2s ease',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      },
      onMouseEnter: (e: React.MouseEvent<HTMLDivElement>) => {
        if (!isDragging) {
          (e.currentTarget as HTMLDivElement).style.background = 'rgba(102, 126, 234, 0.1)'
        }
      },
      onMouseLeave: (e: React.MouseEvent<HTMLDivElement>) => {
        if (!isDragging) {
          (e.currentTarget as HTMLDivElement).style.background = 'transparent'
        }
      }
    },
      // 分割线可视化指示器
      React.createElement('div', {
        style: {
          width: '2px',
          height: '60px',
          background: isDragging ? '#667eea' : '#d9d9d9',
          borderRadius: '1px',
          transition: isDragging ? 'none' : 'background 0.2s ease',
          opacity: isDragging ? 1 : 0.6
        }
      }),

      // 三个点指示器
      React.createElement('div', {
        style: {
          position: 'absolute',
          display: 'flex',
          flexDirection: 'column',
          gap: '2px',
          alignItems: 'center'
        }
      },
        [1, 2, 3].map(i =>
          React.createElement('div', {
            key: i,
            style: {
              width: '3px',
              height: '3px',
              borderRadius: '50%',
              background: isDragging ? '#667eea' : '#bbb',
              transition: 'background 0.2s ease'
            }
          })
        )
      )
    ),

    // 右侧面板
    React.createElement('div', {
      style: {
        width: `${100 - leftWidth}%`,
        height: '100%',
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column'
      }
    }, rightContent)
  )
}

export default ResizableSplitter