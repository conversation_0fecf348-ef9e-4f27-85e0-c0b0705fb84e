// 默认结果视图组件 - 适配Rome框架
import React from '@rome/stone/react'
import type { AgentType } from '../../types/agent'

interface DefaultResultViewProps {
  result: any
  agentType: AgentType
}

const DefaultResultView: React.FC<DefaultResultViewProps> = ({
  result,
  agentType
}) => {
  const getAgentTitle = (type: AgentType) => {
    const titles = {
      'ui-prototype': 'UI原型设计',
      'tech-doc': '技术文档',
      'api-doc': 'API文档',
      'requirements': '需求分析',
      'code-review': '代码审查',
      'prd': 'PRD文档'
    }
    return titles[type]
  }

  const getAgentDescription = (type: AgentType) => {
    const descriptions = {
      'ui-prototype': '界面原型设计和交互流程定义',
      'tech-doc': '技术架构设计和实现方案文档',
      'api-doc': 'API接口规范和使用说明文档',
      'requirements': '产品需求分析和用户故事梳理',
      'code-review': '代码质量检测和优化建议',
      'prd': '产品需求文档和功能规格说明'
    }
    return descriptions[type]
  }

  const getAgentIcon = (type: AgentType) => {
    const icons = {
      'ui-prototype': '🎨',
      'tech-doc': '⚙️',
      'api-doc': '🔌',
      'requirements': '📋',
      'code-review': '🔍',
      'prd': '📖'
    }
    return icons[type]
  }

  return React.createElement('div', {
    style: {
      display: 'flex',
      height: '100%',
      width: '100%'
    }
  },
    // 左侧：主要内容
    React.createElement('div', {
      style: {
        width: '50%',
        height: '100%',
        overflow: 'auto',
        padding: '20px',
        background: '#ffffff'
      }
    },
      React.createElement('h4', {
        style: { 
          marginBottom: '16px', 
          fontSize: '16px', 
          fontWeight: 600,
          display: 'flex',
          alignItems: 'center',
          gap: '8px'
        }
      },
        React.createElement('span', { 
          style: { color: '#1890ff', fontSize: '18px' } 
        }, getAgentIcon(agentType)),
        `${getAgentTitle(agentType)}结果`
      ),
      
      React.createElement('div', {
        style: {
          marginBottom: '16px',
          borderRadius: '8px',
          border: '1px solid #e8e8e8',
          background: '#ffffff',
          padding: '16px'
        }
      },
        React.createElement('p', {
          style: { 
            fontSize: '14px', 
            lineHeight: '1.6', 
            marginBottom: '16px',
            color: '#333'
          }
        }, `${getAgentDescription(agentType)}已完成。以下是生成的结果内容：`),

        // 显示原始结果数据
        React.createElement('div', {
          style: {
            background: '#f8f9fa',
            padding: '16px',
            borderRadius: '6px',
            border: '1px solid #e8e8e8'
          }
        },
          React.createElement('pre', {
            style: {
              margin: 0,
              fontSize: '12px',
              lineHeight: '1.4',
              color: '#333',
              whiteSpace: 'pre-wrap',
              wordBreak: 'break-word',
              fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace'
            }
          }, JSON.stringify(result, null, 2))
        )
      ),

      // 功能说明
      React.createElement('div', {
        style: {
          borderRadius: '8px',
          border: '1px solid #e8e8e8',
          background: '#ffffff'
        }
      },
        React.createElement('div', {
          style: {
            padding: '12px 16px',
            borderBottom: '1px solid #e8e8e8',
            background: '#fafafa',
            fontWeight: 600,
            fontSize: '14px'
          }
        }, '功能说明'),
        React.createElement('div', {
          style: { padding: '16px' }
        },
          React.createElement('div', {
            style: { marginBottom: '16px' }
          },
            React.createElement('div', {
              style: { 
                fontSize: '13px', 
                fontWeight: 600, 
                marginBottom: '4px' 
              }
            }, '数据格式'),
            React.createElement('div', {
              style: { fontSize: '12px', color: '#666' }
            }, `结果以JSON格式存储，包含了${getAgentTitle(agentType)}的所有相关信息`)
          ),
          
          React.createElement('div', {
            style: { marginBottom: '16px' }
          },
            React.createElement('div', {
              style: { 
                fontSize: '13px', 
                fontWeight: 600, 
                marginBottom: '4px' 
              }
            }, '使用建议'),
            React.createElement('div', {
              style: { fontSize: '12px', color: '#666' }
            }, '可以使用右上角的复制或下载功能保存结果，也可以进行进一步的编辑和处理')
          ),
          
          React.createElement('div', {},
            React.createElement('div', {
              style: { 
                fontSize: '13px', 
                fontWeight: 600, 
                marginBottom: '4px' 
              }
            }, '后续操作'),
            React.createElement('div', {
              style: { fontSize: '12px', color: '#666' }
            }, '基于此结果，您可以启动其他相关的Agent来完成完整的工作流程')
          )
        )
      )
    ),

    // 右侧：元信息和操作
    React.createElement('div', {
      style: {
        width: '50%',
        height: '100%',
        overflow: 'auto',
        padding: '20px',
        background: '#fafafa',
        borderLeft: '1px solid #e8e8e8'
      }
    },
      // 基本信息
      React.createElement('div', {
        style: {
          marginBottom: '16px',
          borderRadius: '8px',
          border: '1px solid #e8e8e8',
          background: '#ffffff'
        }
      },
        React.createElement('div', {
          style: {
            padding: '12px 16px',
            borderBottom: '1px solid #e8e8e8',
            background: '#fafafa',
            fontWeight: 600,
            fontSize: '14px',
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }
        },
          React.createElement('span', { style: { fontSize: '16px' } }, 'ℹ️'),
          '基本信息'
        ),
        React.createElement('div', {
          style: { padding: '12px' }
        },
          React.createElement('div', {
            style: { marginBottom: '8px', fontSize: '12px' }
          },
            React.createElement('span', { 
              style: { fontWeight: 600 } 
            }, 'Agent类型：'),
            getAgentTitle(agentType)
          ),
          React.createElement('div', {
            style: { marginBottom: '8px', fontSize: '12px' }
          },
            React.createElement('span', { 
              style: { fontWeight: 600 } 
            }, '完成时间：'),
            new Date().toLocaleString()
          ),
          React.createElement('div', {
            style: { marginBottom: '8px', fontSize: '12px' }
          },
            React.createElement('span', { 
              style: { fontWeight: 600 } 
            }, '数据大小：'),
            `${new Blob([JSON.stringify(result)]).size} 字节`
          ),
          React.createElement('div', {
            style: { fontSize: '12px' }
          },
            React.createElement('span', { 
              style: { fontWeight: 600 } 
            }, '状态：'),
            React.createElement('span', {
              style: { color: '#52c41a' }
            }, '已完成')
          )
        )
      ),

      // 数据结构
      React.createElement('div', {
        style: {
          borderRadius: '8px',
          border: '1px solid #e8e8e8',
          background: '#ffffff'
        }
      },
        React.createElement('div', {
          style: {
            padding: '12px 16px',
            borderBottom: '1px solid #e8e8e8',
            background: '#fafafa',
            fontWeight: 600,
            fontSize: '14px',
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }
        },
          React.createElement('span', { style: { fontSize: '16px' } }, '📄'),
          '数据结构'
        ),
        React.createElement('div', {
          style: { padding: '12px', fontSize: '12px', color: '#666' }
        },
          React.createElement('div', {
            style: { marginBottom: '8px' }
          },
            React.createElement('span', { 
              style: { fontWeight: 600 } 
            }, '字段数量：'),
            Object.keys(result || {}).length
          ),
          
          React.createElement('div', {
            style: { marginBottom: '8px', fontWeight: 600 }
          }, '主要字段：'),
          
          React.createElement('div', {
            style: { paddingLeft: '12px' }
          },
            Object.keys(result || {}).slice(0, 5).map((key, index) =>
              React.createElement('div', {
                key: index,
                style: { marginBottom: '4px' }
              },
                React.createElement('code', {
                  style: { 
                    fontSize: '11px',
                    background: '#f5f5f5',
                    padding: '2px 4px',
                    borderRadius: '2px'
                  }
                }, key),
                React.createElement('span', {
                  style: { 
                    fontSize: '11px', 
                    color: '#999', 
                    marginLeft: '8px' 
                  }
                }, typeof result[key])
              )
            ),
            Object.keys(result || {}).length > 5 && React.createElement('div', {
              style: { fontSize: '11px', color: '#999' }
            }, `...还有 ${Object.keys(result || {}).length - 5} 个字段`)
          )
        )
      )
    )
  )
}

export default DefaultResultView