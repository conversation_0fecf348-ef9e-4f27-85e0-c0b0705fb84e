// 代码审查结果展示组件 - 严格遵循 sg-aicr-fe 开发规范
import React, { useState, useEffect } from '@rome/stone/react';
import { observer } from '@rome/stone/mobx-react';
import { Card, Button, Tag } from '@ss/mtd-react';
import {
  HiOutlineMagnifyingGlass,
  HiOutlineCheckCircle,
  HiOutlineXCircle,
  HiOutlineRocketLaunch,
  HiOutlineClipboardDocumentList,
  HiOutlineFolderOpen,
  HiOutlineLightBulb,
  HiOutlineCpuChip,
  HiOutlineChartBarSquare
} from 'react-icons/hi2';

interface CodeReviewResultProps {
  reviewData?: any;
  isLoading?: boolean;
  onComplete?: () => void;
  onClose?: () => void;
}

interface Problem {
  level: string;
  problem: string;
  suggestion: string;
  targetCode: string;
  codePosition: number[];
}

interface TaskDetail {
  taskName: string;
  taskType: string;
  executionStatus: string;
  problemsFound: number;
  taskSummary: string;
}

// 添加 CSS 动画样式
const animationStyles = `
  @keyframes pulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(1.05); }
  }

  @keyframes fadeInScale {
    0% { opacity: 0; transform: scale(0.8); }
    100% { opacity: 1; transform: scale(1); }
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  @keyframes checkmark {
    0% { opacity: 0; transform: scale(0.5); }
    50% { opacity: 1; transform: scale(1.2); }
    100% { opacity: 1; transform: scale(1); }
  }

  @keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
  }

  @keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
    20%, 40%, 60%, 80% { transform: translateX(2px); }
  }

  @keyframes rocketLaunch {
    0% { transform: translateY(0) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(5deg); }
    100% { transform: translateY(0) rotate(0deg); }
  }

  @keyframes slideInUp {
    0% { opacity: 0; transform: translateY(20px); }
    100% { opacity: 1; transform: translateY(0); }
  }

  @keyframes fadeIn {
    0% { opacity: 0; }
    100% { opacity: 1; }
  }
`;

const CodeReviewResultView: React.FC<CodeReviewResultProps> = observer(({
  reviewData,
  isLoading = false,
  onComplete,
  onClose
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [showFinalResult, setShowFinalResult] = useState(false);
  const [showAllProblems, setShowAllProblems] = useState(false);

  // 注入 CSS 动画样式
  useEffect(() => {
    const styleElement = document.createElement('style');
    styleElement.textContent = animationStyles;
    document.head.appendChild(styleElement);

    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);

  const steps = [
    { name: '安全风险检查', status: 'running' },
    { name: '代码质量检查', status: 'pending' },
    { name: '性能优化检查', status: 'pending' },
    { name: '可维护性检查', status: 'pending' }
  ];

  useEffect(() => {
    if (isLoading) {
      setShowFinalResult(false);
      const interval = setInterval(() => {
        setCurrentStep(prev => {
          if (prev < steps.length - 1) {
            return prev + 1;
          } else {
            clearInterval(interval);
            return prev;
          }
        });
      }, 2000);

      return () => clearInterval(interval);
    } else if (reviewData) {
      setShowFinalResult(true);
    }
    return undefined;
  }, [isLoading, reviewData, steps.length]);

  const getPriorityColor = (level: string) => {
    switch (level) {
      case 'P0': return '#ff4d4f';
      case 'P1': return '#faad14';
      case 'P2': return '#1890ff';
      default: return '#52c41a';
    }
  };

  const getPriorityLabel = (level: string) => {
    switch (level) {
      case 'P0': return '严重';
      case 'P1': return '重要';
      case 'P2': return '一般';
      default: return '轻微';
    }
  };

  const getGradeColor = (grade: string) => {
    switch (grade) {
      case 'A': return '#52c41a';
      case 'B': return '#1890ff';
      case 'C': return '#faad14';
      case 'D': return '#ff7a45';
      case 'F': return '#ff4d4f';
      default: return '#d9d9d9';
    }
  };

  // 如果正在加载或者没有数据且不是明确的完成状态，显示加载页面
  if (isLoading || (!reviewData && !showFinalResult)) {
    return (
      <div style={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        background: 'rgba(255, 255, 255, 0.95)',
        backdropFilter: 'blur(20px)',
        borderRadius: '16px',
        border: '1px solid rgba(226, 232, 240, 0.6)',
        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.08)',
        overflow: 'hidden'
      }}>
        <div style={{
          padding: '20px 24px',
          borderBottom: '1px solid rgba(226, 232, 240, 0.6)',
          background: 'rgba(255, 255, 255, 0.95)'
        }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <div>
              <h3 style={{
                margin: 0,
                fontSize: '18px',
                fontWeight: 600,
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text'
              }}>
                <span style={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  marginRight: '8px',
                  animation: 'pulse 2s infinite',
                  fontSize: '18px'
                }}>
                  {(HiOutlineMagnifyingGlass as any)({})}
                </span>
                代码审查进行中
              </h3>
              <p style={{ color: '#64748b', fontSize: '12px', margin: '4px 0 0 0' }}>
                AI正在分析你的代码，请稍候...
              </p>
            </div>
            <Button
              size="small"
              onClick={onClose}
              style={{
                color: '#666',
                background: 'transparent',
                border: 'none',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              ✕
            </Button>
          </div>
        </div>
        <div style={{ flex: 1, padding: '24px', overflow: 'auto' }}>
          <div style={{ marginBottom: '32px' }}>
            <h4 style={{ fontSize: '16px', fontWeight: 600, marginBottom: '16px' }}>
              任务执行进度
            </h4>
            {steps.map((step, index) => (
              <div key={index} style={{
                display: 'flex',
                alignItems: 'center',
                padding: '12px 0',
                borderBottom: index < steps.length - 1 ? '1px solid rgba(226, 232, 240, 0.6)' : 'none'
              }}>
                <div style={{
                  width: '24px',
                  height: '24px',
                  borderRadius: '50%',
                  background: index <= currentStep ?
                    'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' :
                    '#e2e8f0',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginRight: '12px',
                  color: 'white',
                  fontSize: '12px'
                }}>
                  {index < currentStep ? (
                    <span style={{ animation: 'checkmark 0.5s ease-out' }}>✓</span>
                  ) : index === currentStep ? (
                    <span style={{ animation: 'spin 1s linear infinite' }}>⟳</span>
                  ) : (
                    index + 1
                  )}
                </div>
                <div style={{ flex: 1 }}>
                  <div style={{ fontWeight: 500, fontSize: '14px' }}>{step.name}</div>
                  <div style={{ fontSize: '12px', color: '#64748b', marginTop: '2px' }}>
                    {index < currentStep ? '已完成' : index === currentStep ? '执行中...' : '等待中'}
                  </div>
                </div>
              </div>
            ))}
          </div>
          <div style={{
            background: 'rgba(102, 126, 234, 0.05)',
            border: '1px solid rgba(102, 126, 234, 0.2)',
            borderRadius: '8px',
            padding: '16px',
            textAlign: 'center'
          }}>
            <div style={{
              fontSize: '32px',
              marginBottom: '12px',
              animation: 'pulse 2s infinite',
              display: 'flex',
              justifyContent: 'center'
            }}>
              {(HiOutlineCpuChip as any)({})}
            </div>
            <p style={{ margin: 0, color: '#667eea', fontWeight: 500 }}>
              AI正在深度分析你的代码质量、安全性和性能...
            </p>
          </div>
        </div>
      </div>
    );
  }

  // 如果没有数据且已经完成加载，显示无结果页面
  if (!reviewData && showFinalResult) {
    return (
      <div style={{
        height: '100%',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        padding: '24px',
        textAlign: 'center'
      }}>
        <div style={{
          background: 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(20px)',
          borderRadius: '16px',
          border: '1px solid rgba(226, 232, 240, 0.6)',
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.08)',
          padding: '48px',
          fontSize: '16px',
          color: '#64748b'
        }}>
          <div style={{
            fontSize: '48px',
            marginBottom: '16px',
            display: 'flex',
            justifyContent: 'center'
          }}>
            {(HiOutlineClipboardDocumentList as any)({})}
          </div>
          <div style={{ fontSize: '18px', fontWeight: 600, marginBottom: '8px' }}>暂无审查结果</div>
          <div style={{ fontSize: '14px', color: '#94a3b8' }}>请稍后重试或检查网络连接</div>
        </div>
      </div>
    );
  }

  const { summary, scoring, problems, statistics, taskDetails, qualityGates, originalReviewResults } = reviewData;

  const processedStatistics = statistics || {
    criticalCount: scoring?.scoreBreakdown?.criticalIssues?.score === 30 ? 0 : (problems?.filter((p: Problem) => p.level === 'P0')?.length || 0),
    warningCount: scoring?.scoreBreakdown?.warningIssues?.score === 25 ? 0 : (problems?.filter((p: Problem) => p.level === 'P1')?.length || 0),
    moderateCount: scoring?.scoreBreakdown?.moderateIssues?.score === 0 ? (problems?.filter((p: Problem) => p.level === 'P2')?.length || 0) : 0,
    minorCount: scoring?.scoreBreakdown?.minorIssues?.score === 20 ? 0 : (problems?.filter((p: Problem) => p.level === 'P3')?.length || 0),
    totalCount: problems?.length || summary?.totalProblems || 0,
    problemDistribution: statistics?.problemDistribution || {
      P0: problems?.filter((p: Problem) => p.level === 'P0')?.length || 0,
      P1: problems?.filter((p: Problem) => p.level === 'P1')?.length || 0,
      P2: problems?.filter((p: Problem) => p.level === 'P2')?.length || 0,
      'P3+': problems?.filter((p: Problem) => p.level === 'P3')?.length || 0
    }
  };

  return (
    <div style={{
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      background: 'rgba(255, 255, 255, 0.9)',
      backdropFilter: 'blur(20px)',
      borderRadius: '20px',
      border: '1px solid rgba(255, 255, 255, 0.2)',
      boxShadow: '0 20px 40px rgba(0, 0, 0, 0.15)',
      overflow: 'hidden',
      position: 'relative'
    }}>
      {/* 添加光效背景 */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: 'radial-gradient(circle at 25% 25%, rgba(102, 126, 234, 0.03) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(118, 75, 162, 0.03) 0%, transparent 50%)',
        pointerEvents: 'none',
        zIndex: 0
      }} />

      <div style={{
        padding: '24px 32px',
        borderBottom: '1px solid rgba(226, 232, 240, 0.3)',
        background: 'rgba(255, 255, 255, 0.8)',
        position: 'relative',
        zIndex: 1
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <h3 style={{
              margin: 0,
              fontSize: '18px',
              fontWeight: 600,
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text'
            }}>
              <span style={{
                display: 'inline-flex',
                alignItems: 'center',
                marginRight: '8px',
                animation: 'fadeInScale 0.6s ease-out',
                fontSize: '18px'
              }}>
                {(HiOutlineChartBarSquare as any)({})}
              </span>
              代码审查报告
            </h3>
            <p style={{ color: '#64748b', fontSize: '12px', margin: '4px 0 0 0' }}>
              审查时间: {summary?.reviewTime || '未知'} | 审查分支: {summary?.checkBranch || '未知'}
            </p>
            <div style={{
              marginTop: '8px',
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}>
              <span style={{
                background: summary?.overallResult === '不通过' ? '#ff4d4f' : '#52c41a',
                color: 'white',
                padding: '2px 8px',
                borderRadius: '16px',
                fontSize: '11px',
                fontWeight: 600
              }}>
                {summary?.overallResult || '未知'}
              </span>
              <span style={{ fontSize: '12px', color: '#64748b' }}>
                {summary?.resultDescription || ''}
              </span>
            </div>
          </div>
          <Button
            size="small"
            onClick={onClose}
            style={{
              color: '#666',
              background: 'transparent',
              border: 'none',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            ✕
          </Button>
        </div>
      </div>

      <div style={{ flex: 1, padding: '24px', overflow: 'auto' }}>
        <div style={{
          display: 'grid',
          gridTemplateColumns: '1fr 1fr',
          gap: '16px',
          marginBottom: '24px',
          animation: 'slideInUp 0.6s ease-out'
        }}>
          <Card style={{
            background: 'rgba(255, 255, 255, 0.9)',
            backdropFilter: 'blur(20px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            borderRadius: '16px',
            padding: '24px',
            position: 'relative',
            overflow: 'hidden',
            transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
            cursor: 'pointer',
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            textAlign: 'center'
          }}>
            <div style={{
              fontSize: '48px',
              fontWeight: 'bold',
              color: getGradeColor(scoring?.qualityGrade),
              marginBottom: '8px'
            }}>
              {scoring?.qualityGrade || 'N/A'}
            </div>
            <div style={{ fontSize: '14px', color: '#64748b', marginBottom: '12px' }}>
              代码质量等级
            </div>
            <div style={{ fontSize: '24px', fontWeight: 600, color: '#1a1a1a' }}>
              {scoring?.overallScore || 0}/{scoring?.maxScore || 100}
            </div>
          </Card>

          <Card style={{
            background: 'rgba(255, 255, 255, 0.9)',
            backdropFilter: 'blur(20px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            borderRadius: '16px',
            padding: '24px',
            position: 'relative',
            overflow: 'hidden',
            transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
            cursor: 'pointer',
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)'
          }}>
            <h4 style={{
              fontSize: '16px',
              fontWeight: 600,
              marginBottom: '20px',
              color: '#1e293b',
              fontFamily: 'SF Pro Display, -apple-system, BlinkMacSystemFont, Segoe UI, sans-serif'
            }}>
              问题统计
            </h4>
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
              <div style={{
                textAlign: 'center',
                padding: '16px 12px',
                borderRadius: '12px',
                background: 'rgba(239, 68, 68, 0.05)',
                border: '1px solid rgba(239, 68, 68, 0.1)',
                transition: 'all 0.3s ease'
              }}>
                <div style={{
                  fontSize: '28px',
                  fontWeight: 700,
                  color: '#ef4444',
                  marginBottom: '4px',
                  fontFamily: 'SF Pro Display, -apple-system, BlinkMacSystemFont, Segoe UI, sans-serif',
                  letterSpacing: '-1px'
                }}>
                  {processedStatistics?.criticalCount || 0}
                </div>
                <div style={{ fontSize: '12px', color: '#64748b', fontWeight: 500 }}>严重问题</div>
              </div>
              <div style={{
                textAlign: 'center',
                padding: '16px 12px',
                borderRadius: '12px',
                background: 'rgba(245, 158, 11, 0.05)',
                border: '1px solid rgba(245, 158, 11, 0.1)',
                transition: 'all 0.3s ease'
              }}>
                <div style={{
                  fontSize: '28px',
                  fontWeight: 700,
                  color: '#f59e0b',
                  marginBottom: '4px',
                  fontFamily: 'SF Pro Display, -apple-system, BlinkMacSystemFont, Segoe UI, sans-serif',
                  letterSpacing: '-1px'
                }}>
                  {processedStatistics?.warningCount || 0}
                </div>
                <div style={{ fontSize: '12px', color: '#64748b', fontWeight: 500 }}>重要问题</div>
              </div>
              <div style={{
                textAlign: 'center',
                padding: '16px 12px',
                borderRadius: '12px',
                background: 'rgba(59, 130, 246, 0.05)',
                border: '1px solid rgba(59, 130, 246, 0.1)',
                transition: 'all 0.3s ease'
              }}>
                <div style={{
                  fontSize: '28px',
                  fontWeight: 700,
                  color: '#3b82f6',
                  marginBottom: '4px',
                  fontFamily: 'SF Pro Display, -apple-system, BlinkMacSystemFont, Segoe UI, sans-serif',
                  letterSpacing: '-1px'
                }}>
                  {processedStatistics?.moderateCount || 0}
                </div>
                <div style={{ fontSize: '12px', color: '#64748b', fontWeight: 500 }}>一般问题</div>
              </div>
              <div style={{
                textAlign: 'center',
                padding: '16px 12px',
                borderRadius: '12px',
                background: 'rgba(16, 185, 129, 0.05)',
                border: '1px solid rgba(16, 185, 129, 0.1)',
                transition: 'all 0.3s ease'
              }}>
                <div style={{
                  fontSize: '28px',
                  fontWeight: 700,
                  color: '#10b981',
                  marginBottom: '4px',
                  fontFamily: 'SF Pro Display, -apple-system, BlinkMacSystemFont, Segoe UI, sans-serif',
                  letterSpacing: '-1px'
                }}>
                  {processedStatistics?.minorCount || 0}
                </div>
                <div style={{ fontSize: '12px', color: '#64748b', fontWeight: 500 }}>轻微问题</div>
              </div>
            </div>
          </Card>
        </div>

        {qualityGates && (
          <Card
            title="质量门禁"
            style={{
              background: 'rgba(255, 255, 255, 0.9)',
              backdropFilter: 'blur(20px)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              borderRadius: '16px',
              boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
              marginBottom: '24px',
              transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)'
            }}
          >
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(3, 1fr)',
              gap: '16px',
              alignItems: 'center'
            }}>
              <div style={{ textAlign: 'center', padding: '16px' }}>
                <div style={{
                  fontSize: '32px',
                  fontWeight: 'bold',
                  color: qualityGates.overallStatus === 'PASSED' ? '#52c41a' : '#ff4d4f',
                  marginBottom: '8px'
                }}>
                  <span style={{
                    animation: qualityGates.overallStatus === 'PASSED' ? 'bounce 0.6s ease-out' : 'shake 0.5s ease-out',
                    display: 'inline-flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    {qualityGates.overallStatus === 'PASSED' ?
                      (HiOutlineCheckCircle as any)({}) :
                      (HiOutlineXCircle as any)({})
                    }
                  </span>
                </div>
                <div style={{ fontSize: '14px', color: '#64748b', marginBottom: '4px' }}>
                  门禁状态
                </div>
                <div style={{
                  fontSize: '16px',
                  fontWeight: 600,
                  color: qualityGates.overallStatus === 'PASSED' ? '#52c41a' : '#ff4d4f'
                }}>
                  {qualityGates.overallStatus === 'PASSED' ? '通过' : '未通过'}
                </div>
              </div>

              <div style={{ textAlign: 'center', padding: '16px' }}>
                <div style={{
                  fontSize: '32px',
                  fontWeight: 'bold',
                  color: qualityGates.passRate >= 80 ? '#52c41a' : qualityGates.passRate >= 60 ? '#faad14' : '#ff4d4f',
                  marginBottom: '8px'
                }}>
                  {qualityGates.passRate}%
                </div>
                <div style={{ fontSize: '14px', color: '#64748b', marginBottom: '4px' }}>
                  通过率
                </div>
                <div style={{
                  fontSize: '12px',
                  color: '#64748b'
                }}>
                  质量门禁通过率
                </div>
              </div>

              <div style={{ textAlign: 'center', padding: '16px' }}>
                <div style={{
                  fontSize: '32px',
                  fontWeight: 'bold',
                  color: qualityGates.canDeploy ? '#52c41a' : '#ff4d4f',
                  marginBottom: '8px'
                }}>
                  <span style={{
                    animation: qualityGates.canDeploy ? 'rocketLaunch 0.8s ease-out' : 'shake 0.5s ease-out',
                    display: 'inline-flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    {qualityGates.canDeploy ?
                      (HiOutlineRocketLaunch as any)({}) :
                      (HiOutlineXCircle as any)({})
                    }
                  </span>
                </div>
                <div style={{ fontSize: '14px', color: '#64748b', marginBottom: '4px' }}>
                  部署状态
                </div>
                <div style={{
                  fontSize: '16px',
                  fontWeight: 600,
                  color: qualityGates.canDeploy ? '#52c41a' : '#ff4d4f'
                }}>
                  {qualityGates.canDeploy ? '可部署' : '禁止部署'}
                </div>
              </div>
            </div>
          </Card>
        )}

        {taskDetails && taskDetails.length > 0 && (
          <Card
            title="任务执行详情"
            style={{
              borderRadius: '12px',
              border: '1px solid rgba(226, 232, 240, 0.6)',
              boxShadow: '0 4px 16px rgba(0, 0, 0, 0.04)',
              marginBottom: '24px'
            }}
          >
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '12px' }}>
              {taskDetails.map((task: TaskDetail, index: number) => (
                <div key={index} style={{
                  background: 'rgba(248, 250, 252, 0.8)',
                  padding: '12px',
                  borderRadius: '8px',
                  border: '1px solid rgba(226, 232, 240, 0.6)'
                }}>
                  <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    marginBottom: '8px'
                  }}>
                    <span style={{ fontWeight: 600, fontSize: '14px' }}>
                      {task.taskName}
                    </span>
                    <Tag
                      color={task.problemsFound > 0 ? '#faad14' : '#52c41a'}
                      style={{ fontSize: '10px' }}
                    >
                      {task.problemsFound}个问题
                    </Tag>
                  </div>
                  <div style={{ fontSize: '12px', color: '#64748b' }}>
                    {task.taskSummary}
                  </div>
                </div>
              ))}
            </div>
          </Card>
        )}

        <Card
          title={`问题详情 (${processedStatistics?.totalCount || 0}个)`}
          style={{
            background: 'rgba(255, 255, 255, 0.9)',
            backdropFilter: 'blur(20px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            borderRadius: '16px',
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
            marginBottom: '24px',
            transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)'
          }}
        >
          <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
            {(() => {
              // 优先使用 originalReviewResults 中的问题数据
              let allProblems: Problem[] = [];

              if (originalReviewResults && originalReviewResults.length > 0) {
                // 从 originalReviewResults 中提取所有问题
                originalReviewResults.forEach((result: any) => {
                  if (result.problems && result.problems.length > 0) {
                    result.problems.forEach((problem: any) => {
                      allProblems.push({
                        ...problem,
                        codePosition: result.codePosition, // 添加文件位置信息
                        codePositionArray: result.codePositionArray
                      });
                    });
                  }
                });
              } else if (problems && problems.length > 0) {
                // 回退到原有的 problems 数据
                allProblems = problems;
              }

              if (allProblems.length > 0) {
                // 根据显示状态决定显示的问题数量
                const displayProblems = showAllProblems ? allProblems : allProblems.slice(0, 10);
                const hasHiddenProblems = allProblems.length > 10;

                return (
                  <>
                    {displayProblems.map((problem: any, index: number) => (
                      <div key={`${problem.level}-${index}`} style={{
                        padding: '16px 0',
                        borderBottom: index < displayProblems.length - 1 ? '1px solid rgba(226, 232, 240, 0.6)' : 'none'
                      }}>
                        <div style={{ display: 'flex', alignItems: 'flex-start', marginBottom: '12px' }}>
                          <Tag
                            color={getPriorityColor(problem.level)}
                            style={{ fontSize: '10px', marginRight: '12px' }}
                          >
                            {getPriorityLabel(problem.level)}
                          </Tag>
                          <div style={{ flex: 1 }}>
                            <div style={{ fontWeight: 600, fontSize: '14px', marginBottom: '8px' }}>
                              {problem.problem || '问题描述不可用'}
                            </div>

                            {/* 显示文件位置信息 */}
                            {problem.codePosition && (
                              <div style={{
                                background: 'rgba(102, 126, 234, 0.1)',
                                padding: '4px 8px',
                                borderRadius: '4px',
                                fontSize: '11px',
                                color: '#667eea',
                                marginBottom: '8px',
                                fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace'
                              }}>
                                <span style={{
                                  marginRight: '4px',
                                  display: 'inline-flex',
                                  alignItems: 'center'
                                }}>
                                  {(HiOutlineFolderOpen as any)({})}
                                </span>
                                {problem.codePosition}
                              </div>
                            )}

                            {problem.targetCode && problem.targetCode.trim() && (
                              <div style={{
                                background: 'rgba(248, 250, 252, 0.8)',
                                padding: '8px 12px',
                                borderRadius: '4px',
                                fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
                                fontSize: '12px',
                                border: '1px solid rgba(226, 232, 240, 0.6)',
                                marginBottom: '8px',
                                color: '#1a1a1a',
                                maxHeight: '120px',
                                overflowY: 'auto'
                              }}>
                                {problem.targetCode}
                              </div>
                            )}
                            <div style={{
                              fontSize: '13px',
                              color: '#64748b',
                              display: 'flex',
                              alignItems: 'flex-start',
                              gap: '4px'
                            }}>
                              <span style={{
                                display: 'inline-flex',
                                alignItems: 'center',
                                marginTop: '1px'
                              }}>
                                {(HiOutlineLightBulb as any)({})}
                              </span>
                              {problem.suggestion || '建议根据问题描述进行相应的修复'}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}

                    {/* 显示/隐藏开关 */}
                    {hasHiddenProblems && (
                      <div style={{
                        textAlign: 'center',
                        padding: '16px',
                        borderTop: '1px solid rgba(226, 232, 240, 0.6)'
                      }}>
                        <Button
                          onClick={() => setShowAllProblems(!showAllProblems)}
                          style={{
                            color: '#667eea',
                            fontSize: '14px',
                            fontWeight: 500,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            gap: '6px',
                            margin: '0 auto',
                            padding: '8px 16px',
                            borderRadius: '8px',
                            background: 'rgba(102, 126, 234, 0.05)',
                            border: '1px solid rgba(102, 126, 234, 0.2)',
                            transition: 'all 0.3s ease'
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.background = 'rgba(102, 126, 234, 0.1)';
                            e.currentTarget.style.transform = 'translateY(-1px)';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.background = 'rgba(102, 126, 234, 0.05)';
                            e.currentTarget.style.transform = 'translateY(0)';
                          }}
                        >
                          <span style={{
                            display: 'inline-flex',
                            alignItems: 'center',
                            transform: showAllProblems ? 'rotate(180deg)' : 'rotate(0deg)',
                            transition: 'transform 0.3s ease'
                          }}>
                            ▼
                          </span>
                          {showAllProblems
                            ? `收起问题 (隐藏 ${allProblems.length - 10} 个)`
                            : `展开查看全部问题 (还有 ${allProblems.length - 10} 个)`
                          }
                        </Button>
                      </div>
                    )}
                  </>
                );
              } else {
                return (
                  <div style={{ textAlign: 'center', padding: '32px', color: '#64748b' }}>
                    暂无问题
                  </div>
                );
              }
            })()}
          </div>
        </Card>

        <div style={{ textAlign: 'center' }}>
          <div style={{ display: 'flex', gap: '12px', justifyContent: 'center' }}>
            <Button
              onClick={onClose}
              style={{
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                transition: 'all 0.3s ease',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
              }}
            >
              <span style={{ marginRight: '4px' }}>✕</span>
              关闭
            </Button>
            <Button
              type="primary"
              onClick={onComplete}
              style={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                border: 'none',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                transition: 'all 0.3s ease',
                boxShadow: '0 4px 12px rgba(102, 126, 234, 0.3)'
              }}
            >
              <span style={{ marginRight: '4px' }}>✅</span>
              完成审查
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
});

export default CodeReviewResultView;
