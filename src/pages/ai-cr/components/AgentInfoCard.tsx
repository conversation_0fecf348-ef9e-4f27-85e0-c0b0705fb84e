import React, { useState, useEffect } from '@rome/stone/react'
import { <PERSON>, Button } from '@ss/mtd-react'

interface AgentInfoCardProps {
  agentType: string
  onSubmit: (data: any) => void
  onCancel: () => void
}

const AgentInfoCard: React.FC<AgentInfoCardProps> = ({
  agentType,
  onSubmit,
  onCancel
}) => {
  console.log('AgentInfoCard for type:', agentType);
  const [loading, setLoading] = useState(false)
  const [focusedField, setFocusedField] = useState<string | null>(null)
  const [isVisible, setIsVisible] = useState(false)
  const [formData, setFormData] = useState({
    pr_id: '',
    project: '~wangqichen02',
    repo: 'shangou_ai_cr',
    fromBranch: '',
    toBranch: 'main',
    crMode: 'deep',
    spaceId: ''
  })

  const config = {
    title: '代码审查',
    icon: '🐛',
    color: '#eb2f96',
    description: '基于分支对比的智能代码质量检测和优化建议'
  }

  useEffect(() => {
    // 组件挂载动画
    const timer = setTimeout(() => setIsVisible(true), 50)
    return () => clearTimeout(timer)
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 500))
      onSubmit(formData)
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleFocus = (field: string) => {
    setFocusedField(field)
  }

  const handleBlur = () => {
    setFocusedField(null)
  }

  const inputStyle = (field: string) => ({
    width: '100%',
    padding: '14px 18px',
    border: `2px solid ${focusedField === field ? config.color : 'rgba(0,0,0,0.08)'}`,
    borderRadius: '12px',
    fontSize: '14px',
    fontWeight: '500',
    color: '#1a1a1a',
    backgroundColor: focusedField === field ? '#ffffff' : 'rgba(255,255,255,0.7)',
    backdropFilter: 'blur(10px)',
    transition: 'all 0.4s cubic-bezier(0.16, 1, 0.3, 1)',
    outline: 'none',
    boxShadow: focusedField === field
      ? `0 0 0 4px ${config.color}12, 0 8px 25px -5px rgba(0,0,0,0.1), 0 4px 12px -2px rgba(0,0,0,0.05)`
      : '0 2px 8px -2px rgba(0,0,0,0.05), 0 1px 4px -1px rgba(0,0,0,0.06)',
    transform: focusedField === field ? 'translateY(-2px) scale(1.01)' : 'translateY(0) scale(1)',
    '::placeholder': {
      color: '#9ca3af',
      fontWeight: '400',
    }
  })

  const labelStyle = {
    display: 'block',
    marginBottom: '10px',
    fontSize: '12px',
    fontWeight: '700',
    color: '#374151',
    letterSpacing: '0.5px',
    textTransform: 'uppercase' as const,
    position: 'relative' as const,
  }

  const fieldContainerStyle = {
    marginBottom: '24px',
    position: 'relative' as const,
  }

  return (
    <div style={{
      background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',
      backdropFilter: 'blur(20px)',
      borderRadius: '24px',
      padding: '3px',
      boxShadow: `
        0 20px 40px -12px rgba(0,0,0,0.08),
        0 8px 16px -4px rgba(0,0,0,0.03),
        inset 0 1px 0 rgba(255,255,255,0.1),
        inset 0 -1px 0 rgba(0,0,0,0.05)
      `,
      border: '1px solid rgba(255,255,255,0.1)',
      transform: isVisible ? 'translateY(0) scale(1)' : 'translateY(20px) scale(0.95)',
      opacity: isVisible ? 1 : 0,
      transition: 'all 0.6s cubic-bezier(0.16, 1, 0.3, 1)',
    }}>
      <Card style={{
        borderRadius: '21px',
        border: 'none',
        background: 'linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.85) 100%)',
        backdropFilter: 'blur(20px)',
        padding: '40px',
        margin: '0',
        boxShadow: 'none',
        position: 'relative',
        overflow: 'hidden',
      }}>
        {/* 动态装饰性背景元素 */}
        <div style={{
          position: 'absolute',
          top: '-60px',
          right: '-60px',
          width: '140px',
          height: '140px',
          background: `radial-gradient(circle, ${config.color}08 0%, ${config.color}03 50%, transparent 100%)`,
          borderRadius: '50%',
          zIndex: 0,
          animation: 'float 6s ease-in-out infinite',
        }} />

        <div style={{
          position: 'absolute',
          bottom: '-40px',
          left: '-40px',
          width: '100px',
          height: '100px',
          background: `radial-gradient(circle, ${config.color}06 0%, transparent 70%)`,
          borderRadius: '50%',
          zIndex: 0,
          animation: 'float 8s ease-in-out infinite reverse',
        }} />

        {/* 顶部光晕效果 */}
        <div style={{
          position: 'absolute',
          top: '0',
          left: '50%',
          transform: 'translateX(-50%)',
          width: '60%',
          height: '2px',
          background: `linear-gradient(90deg, transparent 0%, ${config.color}40 50%, transparent 100%)`,
          borderRadius: '1px',
          zIndex: 1,
        }} />

        <div style={{ position: 'relative', zIndex: 2 }}>
          {/* 头部区域 */}
          <div style={{ marginBottom: '36px' }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '16px',
              marginBottom: '16px',
              padding: '20px 24px',
              background: `linear-gradient(135deg, ${config.color}06 0%, ${config.color}03 100%)`,
              backdropFilter: 'blur(10px)',
              borderRadius: '16px',
              border: `1px solid ${config.color}12`,
              boxShadow: `
                0 8px 32px -8px ${config.color}15,
                0 0 0 1px rgba(255,255,255,0.05),
                inset 0 1px 0 rgba(255,255,255,0.1)
              `,
              position: 'relative',
              overflow: 'hidden',
            }}>
              {/* 头部背景光效 */}
              <div style={{
                position: 'absolute',
                top: '0',
                left: '0',
                right: '0',
                height: '1px',
                background: `linear-gradient(90deg, transparent 0%, ${config.color}30 50%, transparent 100%)`,
              }} />

              <div style={{
                fontSize: '28px',
                display: 'flex',
                alignItems: 'center',
                filter: 'drop-shadow(0 4px 8px rgba(0,0,0,0.1))',
                transform: 'scale(1)',
                transition: 'transform 0.3s ease',
              }}>
                {config.icon}
              </div>
              <div>
                <div style={{
                  fontSize: '20px',
                  fontWeight: '800',
                  color: '#111827',
                  letterSpacing: '-0.3px',
                  marginBottom: '4px',
                  background: `linear-gradient(135deg, #111827 0%, #374151 100%)`,
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                }}>
                  {config.title}
                </div>
                <div style={{
                  fontSize: '13px',
                  color: '#6b7280',
                  fontWeight: '500',
                  lineHeight: '1.5',
                  opacity: 0.8,
                }}>
                  {config.description}
                </div>
              </div>
            </div>
          </div>

          <form onSubmit={handleSubmit}>
            {/* 表单字段网格布局 */}
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
              gap: '24px',
              marginBottom: '32px',
            }}>
              <div style={fieldContainerStyle}>
                <label style={labelStyle}>
                  <span style={{
                    background: `linear-gradient(135deg, ${config.color} 0%, ${config.color}cc 100%)`,
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                  }}>
                    PR编号
                  </span>
                  <span style={{
                    color: config.color,
                    fontSize: '11px',
                    marginLeft: '4px',
                    fontWeight: '800'
                  }}>*</span>
                </label>
                <input
                  type="text"
                  placeholder="例如：123"
                  value={formData.pr_id}
                  onChange={(e) => handleChange('pr_id', e.target.value)}
                  onFocus={() => handleFocus('pr_id')}
                  onBlur={handleBlur}
                  style={inputStyle('pr_id')}
                />
              </div>

              <div style={fieldContainerStyle}>
                <label style={labelStyle}>
                  <span style={{
                    background: `linear-gradient(135deg, ${config.color} 0%, ${config.color}cc 100%)`,
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                  }}>
                    项目名称
                  </span>
                  <span style={{
                    color: config.color,
                    fontSize: '11px',
                    marginLeft: '4px',
                    fontWeight: '800'
                  }}>*</span>
                </label>
                <input
                  type="text"
                  placeholder="项目名称"
                  value={formData.project}
                  onChange={(e) => handleChange('project', e.target.value)}
                  onFocus={() => handleFocus('project')}
                  onBlur={handleBlur}
                  style={inputStyle('project')}
                />
              </div>

              <div style={fieldContainerStyle}>
                <label style={labelStyle}>
                  <span style={{
                    background: `linear-gradient(135deg, ${config.color} 0%, ${config.color}cc 100%)`,
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                  }}>
                    仓库名称
                  </span>
                  <span style={{
                    color: config.color,
                    fontSize: '11px',
                    marginLeft: '4px',
                    fontWeight: '800'
                  }}>*</span>
                </label>
                <input
                  type="text"
                  placeholder="仓库名称"
                  value={formData.repo}
                  onChange={(e) => handleChange('repo', e.target.value)}
                  onFocus={() => handleFocus('repo')}
                  onBlur={handleBlur}
                  style={inputStyle('repo')}
                />
              </div>

              <div style={fieldContainerStyle}>
                <label style={labelStyle}>
                  <span style={{
                    background: `linear-gradient(135deg, ${config.color} 0%, ${config.color}cc 100%)`,
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                  }}>
                    源分支
                  </span>
                  <span style={{
                    color: config.color,
                    fontSize: '11px',
                    marginLeft: '4px',
                    fontWeight: '800'
                  }}>*</span>
                </label>
                <input
                  type="text"
                  placeholder="例如：feature/new-feature"
                  value={formData.fromBranch}
                  onChange={(e) => handleChange('fromBranch', e.target.value)}
                  onFocus={() => handleFocus('fromBranch')}
                  onBlur={handleBlur}
                  style={inputStyle('fromBranch')}
                />
              </div>

              <div style={fieldContainerStyle}>
                <label style={labelStyle}>
                  <span style={{
                    background: `linear-gradient(135deg, ${config.color} 0%, ${config.color}cc 100%)`,
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                  }}>
                    目标分支
                  </span>
                  <span style={{
                    color: config.color,
                    fontSize: '11px',
                    marginLeft: '4px',
                    fontWeight: '800'
                  }}>*</span>
                </label>
                <input
                  type="text"
                  placeholder="例如：main"
                  value={formData.toBranch}
                  onChange={(e) => handleChange('toBranch', e.target.value)}
                  onFocus={() => handleFocus('toBranch')}
                  onBlur={handleBlur}
                  style={inputStyle('toBranch')}
                />
              </div>

              <div style={fieldContainerStyle}>
                <label style={labelStyle}>
                  <span style={{
                    background: `linear-gradient(135deg, ${config.color} 0%, ${config.color}cc 100%)`,
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                  }}>
                    审查模式
                  </span>
                  <span style={{
                    color: config.color,
                    fontSize: '11px',
                    marginLeft: '4px',
                    fontWeight: '800'
                  }}>*</span>
                </label>
                <select
                  value={formData.crMode}
                  onChange={(e) => handleChange('crMode', e.target.value)}
                  onFocus={() => handleFocus('crMode')}
                  onBlur={handleBlur}
                  style={{
                    ...inputStyle('crMode'),
                    cursor: 'pointer',
                    backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23374151' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m6 8 4 4 4-4'/%3e%3c/svg%3e")`,
                    backgroundPosition: 'right 16px center',
                    backgroundRepeat: 'no-repeat',
                    backgroundSize: '18px',
                    paddingRight: '48px',
                    appearance: 'none',
                  }}
                >
                  <option value="fast">⚡ 快速审查</option>
                  <option value="deep">🔍 深度审查</option>
                  <option value="security">🛡️ 安全审查</option>
                </select>
              </div>
            </div>

            {/* 可选字段 */}
            <div style={{
              ...fieldContainerStyle,
              marginBottom: '40px',
            }}>
              <label style={{
                ...labelStyle,
                color: '#9ca3af',
                textTransform: 'none',
                fontSize: '12px',
                fontWeight: '600',
              }}>
                空间ID
                <span style={{
                  fontSize: '10px',
                  color: '#d1d5db',
                  fontWeight: '500',
                  marginLeft: '6px',
                  padding: '2px 6px',
                  backgroundColor: 'rgba(156, 163, 175, 0.1)',
                  borderRadius: '4px',
                }}>(可选)</span>
              </label>
              <input
                type="text"
                placeholder="可选，用于特定空间配置"
                value={formData.spaceId}
                onChange={(e) => handleChange('spaceId', e.target.value)}
                onFocus={() => handleFocus('spaceId')}
                onBlur={handleBlur}
                style={{
                  ...inputStyle('spaceId'),
                  backgroundColor: focusedField === 'spaceId' ? '#ffffff' : 'rgba(249, 250, 251, 0.8)',
                  border: `2px solid ${focusedField === 'spaceId' ? config.color : 'rgba(0,0,0,0.06)'}`,
                }}
              />
            </div>

            {/* 按钮区域 */}
            <div style={{
              display: 'flex',
              gap: '16px',
              justifyContent: 'flex-end',
              alignItems: 'center',
              paddingTop: '24px',
              borderTop: '1px solid rgba(0,0,0,0.06)',
              position: 'relative',
            }}>
              {/* 按钮区域顶部光效 */}
              <div style={{
                position: 'absolute',
                top: '0',
                left: '50%',
                transform: 'translateX(-50%)',
                width: '40%',
                height: '1px',
                background: `linear-gradient(90deg, transparent 0%, rgba(0,0,0,0.1) 50%, transparent 100%)`,
              }} />

              <Button
                onClick={onCancel}
                style={{
                  height: '48px',
                  padding: '0 28px',
                  borderRadius: '12px',
                  border: '2px solid rgba(0,0,0,0.08)',
                  backgroundColor: 'rgba(255,255,255,0.8)',
                  backdropFilter: 'blur(10px)',
                  color: '#6b7280',
                  fontWeight: '600',
                  fontSize: '14px',
                  transition: 'all 0.3s cubic-bezier(0.16, 1, 0.3, 1)',
                  cursor: 'pointer',
                  boxShadow: '0 4px 12px -2px rgba(0,0,0,0.05), 0 2px 6px -1px rgba(0,0,0,0.06)',
                  position: 'relative',
                  overflow: 'hidden',
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.borderColor = 'rgba(0,0,0,0.12)'
                  e.currentTarget.style.backgroundColor = 'rgba(255,255,255,0.95)'
                  e.currentTarget.style.transform = 'translateY(-2px) scale(1.02)'
                  e.currentTarget.style.boxShadow = '0 8px 25px -5px rgba(0,0,0,0.1), 0 4px 12px -2px rgba(0,0,0,0.08)'
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.borderColor = 'rgba(0,0,0,0.08)'
                  e.currentTarget.style.backgroundColor = 'rgba(255,255,255,0.8)'
                  e.currentTarget.style.transform = 'translateY(0) scale(1)'
                  e.currentTarget.style.boxShadow = '0 4px 12px -2px rgba(0,0,0,0.05), 0 2px 6px -1px rgba(0,0,0,0.06)'
                }}
              >
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                style={{
                  height: '48px',
                  padding: '0 36px',
                  background: `linear-gradient(135deg, ${config.color} 0%, ${config.color}dd 50%, ${config.color}bb 100%)`,
                  borderColor: config.color,
                  borderRadius: '12px',
                  fontWeight: '700',
                  fontSize: '14px',
                  letterSpacing: '0.3px',
                  boxShadow: `
                    0 8px 25px -5px ${config.color}40,
                    0 4px 12px -2px ${config.color}20,
                    0 0 0 1px rgba(255,255,255,0.05),
                    inset 0 1px 0 rgba(255,255,255,0.1)
                  `,
                  transition: 'all 0.4s cubic-bezier(0.16, 1, 0.3, 1)',
                  position: 'relative',
                  overflow: 'hidden',
                  border: 'none',
                }}
                onMouseEnter={(e) => {
                  if (!loading) {
                    e.currentTarget.style.transform = 'translateY(-3px) scale(1.05)'
                    e.currentTarget.style.boxShadow = `
                      0 12px 40px -8px ${config.color}50,
                      0 8px 20px -4px ${config.color}30,
                      0 0 0 1px rgba(255,255,255,0.1),
                      inset 0 1px 0 rgba(255,255,255,0.2)
                    `
                  }
                }}
                onMouseLeave={(e) => {
                  if (!loading) {
                    e.currentTarget.style.transform = 'translateY(0) scale(1)'
                    e.currentTarget.style.boxShadow = `
                      0 8px 25px -5px ${config.color}40,
                      0 4px 12px -2px ${config.color}20,
                      0 0 0 1px rgba(255,255,255,0.05),
                      inset 0 1px 0 rgba(255,255,255,0.1)
                    `
                  }
                }}
              >
                {loading ? (
                  <span style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <span style={{
                      display: 'inline-block',
                      width: '16px',
                      height: '16px',
                      border: '2px solid rgba(255,255,255,0.3)',
                      borderTop: '2px solid #ffffff',
                      borderRadius: '50%',
                      animation: 'spin 1s linear infinite'
                    }} />
                    启动中...
                  </span>
                ) : (
                  '🚀 启动Agent'
                )}
              </Button>
            </div>
          </form>
        </div>
      </Card>

      {/* CSS 动画定义 */}
      <style>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-10px) rotate(5deg); }
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
          .grid-container {
            grid-template-columns: 1fr !important;
            gap: 20px !important;
          }
        }
      `}</style>
    </div>
  )
}

export default AgentInfoCard;
