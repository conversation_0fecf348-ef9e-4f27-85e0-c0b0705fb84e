// 使用 Stone 统一依赖管理 - 基于参考项目的精确复制
import React, { useState, useEffect } from '@rome/stone/react'
import { observer } from '@rome/stone/mobx-react'
import { useHistory, useLocation } from '@rome/stone/react-router-dom'
import { getNavigationItems, getPageTitle } from '../config/routes'
import { createNavigationHelper, routeChangeManager } from '../utils/navigation'
import { Logo } from './Icons'
// React Icons 导入
import {
  HiOutlineCpuChip,
  HiOutlineChartBarSquare,
  HiOutlineMagnifyingGlass,
  HiOutlineCog6Tooth,
  HiOutlineAdjustmentsHorizontal
} from 'react-icons/hi2' 
import './Layout.css'
import type { IconType } from 'react-icons/lib/index'
interface LayoutProps {
  children: React.ReactNode
}

// 图标映射
// @ts-ignore
const iconMap: { [key: string]: IconType } = {
  'HiOutlineCpuChip': HiOutlineCpuChip,
  'HiOutlineChartBarSquare': HiOutlineChartBarSquare,
  'HiOutlineMagnifyingGlass': HiOutlineMagnifyingGlass,
  'HiOutlineCog6Tooth': HiOutlineCog6Tooth,
  'HiOutlineAdjustmentsHorizontal': HiOutlineAdjustmentsHorizontal
}

const Layout: React.FC<LayoutProps> = observer(({ children }) => {
  const history = useHistory()
  const location = useLocation()
  const navigationItems = getNavigationItems()
  const nav = createNavigationHelper(history)
  const [isScrolled, setIsScrolled] = useState(false)
  const [hoveredItem, setHoveredItem] = useState<string | null>(null)
  // 动态设置页面标题
  React.useEffect(() => {
    document.title = getPageTitle(location.pathname)
    // 通知路由变化
    routeChangeManager.notifyRouteChange(location.pathname)
  }, [location.pathname])

  // 监听滚动状态
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10)
    }
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const handleMenuClick = (path: string) => {
    nav.navigateTo(path)
  }

  const handleLogoClick = () => {
    nav.goHome()
  }

  // 渲染图标
  const renderIcon = (iconName: string) => {
    switch (iconName) {
      case 'HiOutlineCpuChip':
        return React.createElement(HiOutlineCpuChip as any, { style: { fontSize: '16px' } })
      case 'HiOutlineChartBarSquare':
        return React.createElement(HiOutlineChartBarSquare as any, { style: { fontSize: '16px' } })
      case 'HiOutlineMagnifyingGlass':
        return React.createElement(HiOutlineMagnifyingGlass as any, { style: { fontSize: '16px' } })
      case 'HiOutlineCog6Tooth':
        return React.createElement(HiOutlineCog6Tooth as any, { style: { fontSize: '16px' } })
      case 'HiOutlineAdjustmentsHorizontal':
        return React.createElement(HiOutlineAdjustmentsHorizontal as any, { style: { fontSize: '16px' } })
      default:
        return <span>{iconName}</span>
    }
  }

  return (
    <div style={{ minHeight: '100vh', width: '100%' }}>
      {/* 优雅导航栏 */}
      <header
        className={`elegant-navbar ${isScrolled ? 'scrolled' : ''}`}
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          zIndex: 1000,
          height: '64px',
          background: isScrolled
            ? 'rgba(255, 255, 255, 0.95)'
            : 'rgba(255, 255, 255, 0.98)',
          backdropFilter: 'blur(20px)',
          WebkitBackdropFilter: 'blur(20px)',
          borderBottom: `1px solid ${isScrolled ? 'rgba(0, 0, 0, 0.1)' : 'rgba(0, 0, 0, 0.06)'}`,
          boxShadow: isScrolled
            ? '0 4px 20px rgba(0, 0, 0, 0.08), 0 1px 4px rgba(0, 0, 0, 0.04)'
            : '0 2px 8px rgba(0, 0, 0, 0.04)',
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '0 32px',
        }}
      >
        {/* 左侧Logo区域 */}
        <div
          className="elegant-logo"
          onClick={handleLogoClick}
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            cursor: 'pointer',
            padding: '8px 12px',
            borderRadius: '10px',
            transition: 'all 0.3s ease',
            position: 'relative',
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.background = 'rgba(102, 126, 234, 0.08)'
            e.currentTarget.style.transform = 'translateY(-1px)'
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.background = 'transparent'
            e.currentTarget.style.transform = 'translateY(0)'
          }}
        >
          <div style={{
            width: '32px',
            height: '32px',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            borderRadius: '8px',
            display: 'flex',
            alignItems: 'center',
              justifyContent: 'center',
            boxShadow: '0 4px 12px rgba(102, 126, 234, 0.3)',
          }}>
            <Logo size={20} />
            </div>

          <h4 style={{
            margin: 0,
            fontSize: '18px',
            fontWeight: 700,
            color: '#1a1a1a',
            letterSpacing: '-0.5px',
              }}>
            闪购 AI 平台
          </h4>
            </div>

        {/* 中央导航菜单 */}
        <div
          className="elegant-nav-menu"
        style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            padding: '4px',
            borderRadius: '12px',
            background: 'rgba(248, 250, 252, 0.8)',
            border: '1px solid rgba(0, 0, 0, 0.06)',
        }}
      >
          {navigationItems.map((item) => (
            <button
              key={item.path}
              onClick={() => handleMenuClick(item.path)}
              className={`elegant-nav-item ${location.pathname === item.path ? 'active' : ''}`}
              title={item.description}
              onMouseEnter={() => setHoveredItem(item.path)}
              onMouseLeave={() => setHoveredItem(null)}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '6px',
                padding: '8px 16px',
                borderRadius: '8px',
                border: 'none',
                background: location.pathname === item.path
                  ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
                  : hoveredItem === item.path
                  ? 'rgba(102, 126, 234, 0.1)'
                  : 'transparent',
                color: location.pathname === item.path ? '#ffffff' : '#374151',
                fontSize: '14px',
                fontWeight: location.pathname === item.path ? 600 : 500,
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                transform: location.pathname === item.path
                  ? 'translateY(-1px)'
                  : 'translateY(0)',
                boxShadow: location.pathname === item.path
                  ? '0 4px 12px rgba(102, 126, 234, 0.3)'
                  : 'none',
              }}
            >
              <span style={{ fontSize: '16px' }}>{renderIcon(item.icon)}</span>
              <span>{item.label}</span>
            </button>
          ))}
        </div>

        {/* 右侧用户区域 */}
        <div
          className="elegant-user-area"
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
          }}
        >
          <div
            className="elegant-user-profile"
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              padding: '6px 12px',
              borderRadius: '8px',
              background: 'rgba(248, 250, 252, 0.8)',
              border: '1px solid rgba(0, 0, 0, 0.06)',
              cursor: 'pointer',
              transition: 'all 0.2s ease',
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = 'rgba(102, 126, 234, 0.1)'
              e.currentTarget.style.borderColor = 'rgba(102, 126, 234, 0.2)'
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = 'rgba(248, 250, 252, 0.8)'
              e.currentTarget.style.borderColor = 'rgba(0, 0, 0, 0.06)'
            }}
          >
            <div
              style={{
                width: '28px',
                height: '28px',
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
                fontSize: '12px',
                fontWeight: 600,
                boxShadow: '0 2px 6px rgba(102, 126, 234, 0.3)',
              }}
            >
              U
            </div>
            <span style={{
              fontSize: '14px',
              fontWeight: 500,
              color: '#374151',
            }}>
              用户
            </span>
          </div>
        </div>
      </header>

      {/* 主内容区域 - 确保不被遮挡 */}
      <main
        className="elegant-content"
        style={{
          minHeight: '100vh',
          background: 'transparent',
          maxWidth: '2400px',
          margin: '0 auto',
          padding: '0 24px',
          width: '100%',
          boxSizing: 'border-box'
        }}
      >
        {children}
      </main>
    </div>
  )
})

export default Layout
