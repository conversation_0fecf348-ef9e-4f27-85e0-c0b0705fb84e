/**
 * 路由守卫组件
 * 处理路由权限验证和重定向逻辑
 */

import React from '@rome/stone/react'
import { observer } from '@rome/stone/mobx-react'
import { useLocation } from '@rome/stone/react-router-dom'
import { requiresAuth, requiresPermission } from '../config/routes'

interface RouteGuardProps {
  children: React.ReactNode
}

const RouteGuard: React.FC<RouteGuardProps> = observer(({ children }) => {
  const location = useLocation()
  const currentPath = location.pathname

  // 检查是否需要认证
  if (requiresAuth(currentPath)) {
    // 这里可以添加认证逻辑
    // const isAuthenticated = authStore.isAuthenticated
    // if (!isAuthenticated) {
    //   return <Redirect to="/login" />
    // }
  }

  // 检查是否需要特殊权限
  if (requiresPermission(currentPath)) {
    // 这里可以添加权限检查逻辑
    // const hasPermission = authStore.hasPermission(currentPath)
    // if (!hasPermission) {
    //   return <Redirect to="/unauthorized" />
    // }
  }

  // 如果是公开路由或通过了所有检查，渲染子组件
  return <>{children}</>
})

export default RouteGuard
