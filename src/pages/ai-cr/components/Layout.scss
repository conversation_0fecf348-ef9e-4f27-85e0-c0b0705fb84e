// 企业级Layout组件样式 - SCSS版本 (BEM命名规范)
@import '../styles/variables';
@import '../styles/mixins';

// 主布局容器
.layout {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;

  // 顶部导航栏
  &__header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: $header-background;
    backdrop-filter: map-get($backdrop-filter, normal);
    border-bottom: 1px solid rgba(226, 232, 240, 0.8);
    box-shadow: shadow(md);
    padding: 0 spacing(2xl);
    height: map-get($layout, header-height);
    @include flex-between;
    transition: all map-get($transition, normal);
    overflow: hidden;
    max-width: 100vw;
    box-sizing: border-box;
  }
}

// Logo区域
.layout {
  &__logo {
    @include flex-center;
    gap: spacing(md);
    font-size: map-get($font-size, xl);
    font-weight: map-get($font-weight, semibold);
    color: color(primary);
    text-decoration: none;
    transition: all map-get($transition, normal);
    flex-shrink: 0;
    letter-spacing: -0.5px;
    white-space: nowrap;

    &:hover {
      transform: scale(1.02);
      filter: brightness(1.1);
    }

    &-icon {
      width: 32px;
      height: 32px;
      background: $primary-gradient;
      border-radius: radius(sm);
      @include flex-center;
      color: color(white);
      font-size: map-get($font-size, lg);
      box-shadow: shadow(primary);
    }
  }
}

/* 导航菜单区域 */
.layout-nav {
  display: flex;
  align-items: center;
  gap: 24px;
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.top-nav-menu {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
  overflow: hidden;
  justify-content: center;
  max-width: 600px;
}

.nav-menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 20px;
  border: none;
  border-radius: 12px;
  background: transparent;
  color: #475569;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  white-space: nowrap;
  height: 44px;
  font-family: inherit;
}

.nav-menu-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-menu-item:hover::before {
  opacity: 1;
}

.nav-menu-item:hover {
  background: transparent;
  color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.nav-menu-item.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
  transform: translateY(-2px);
}

.nav-menu-item.active::before {
  opacity: 0;
}

.nav-icon {
  font-size: 16px;
  flex-shrink: 0;
}

.nav-label {
  flex-shrink: 0;
}

/* 用户操作区域 */
.layout-user-actions {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  min-width: 0;
}

/* 紧凑用户资料按钮 */
.user-profile-compact {
  display: flex;
  align-items: center;
  padding: 4px;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.user-profile-compact:hover {
  background: rgba(102, 126, 234, 0.1);
}

.user-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #667eea;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  font-weight: bold;
  cursor: pointer;
}

/* 内容区域 */
.layout-content {
  margin-top: 80px;
  min-height: calc(100vh - 80px);
  padding: 0;
  background: transparent;
  min-width: 0;
  overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .layout-nav {
    gap: 16px;
  }
  
  .top-nav-menu {
    max-width: 500px;
  }
}

@media (max-width: 1024px) {
  .layout-header {
    padding: 0 20px;
  }

  .layout-nav {
    gap: 12px;
  }

  .top-nav-menu {
    max-width: 400px;
  }

  .nav-menu-item {
    padding: 0 16px;
  }
}

@media (max-width: 768px) {
  .layout-header {
    padding: 0 16px;
    height: 64px;
  }

  .layout-logo {
    font-size: 16px;
  }

  .layout-logo-icon {
    width: 28px;
    height: 28px;
    font-size: 14px;
  }

  .top-nav-menu {
    display: none;
  }

  .layout-content {
    margin-top: 64px;
    min-height: calc(100vh - 64px);
  }
}

@media (max-width: 480px) {
  .layout-header {
    padding: 0 12px;
  }

  .layout-logo {
    font-size: 14px;
  }
}
