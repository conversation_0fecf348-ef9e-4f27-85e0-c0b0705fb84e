<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>输入框修复测试</title>
    <style>
        /* 模拟MTD组件的默认样式 */
        .mtd-input {
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            padding: 8px 12px;
            font-size: 14px;
            background: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .mtd-select-selection {
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            padding: 8px 12px;
            font-size: 14px;
            background: #fff;
            height: 32px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        /* 导入我们的修复样式 */
        /* CSS变量定义 */
        :root {
            /* 主色系 */
            --primary-color: #667eea;
            --primary-light: rgba(102, 126, 234, 0.1);
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

            /* 文字色系 */
            --text-primary: #1a202c;
            --text-secondary: #4a5568;
            --text-muted: #718096;

            /* 背景色系 */
            --bg-primary: rgba(255, 255, 255, 0.95);
            --border-light: rgba(226, 232, 240, 0.4);

            /* 动画系统 */
            --transition-quick: all 0.2s ease;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 40px;
            margin: 0;
        }

        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.9);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
        }

        .test-section {
            margin-bottom: 40px;
        }

        .test-title {
            font-size: 24px;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 20px;
        }

        .test-subtitle {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-secondary);
            margin: 30px 0 15px 0;
        }

        .form-group {
            margin-bottom: 24px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: 600;
            color: var(--text-primary);
        }

        /* 应用我们的修复样式 */
        .settings-page .enhanced-input,
        .settings-page .enhanced-input.mtd-input,
        .settings-page .mtd-input.enhanced-input,
        .settings-page input.enhanced-input,
        .settings-page input.mtd-input.enhanced-input,
        .enhanced-input {
            width: 100% !important;
            height: 48px !important;
            padding: 0 16px !important;
            border: 2px solid var(--border-light) !important;
            border-radius: 16px !important;
            font-size: 14px !important;
            font-weight: 500 !important;
            background: var(--bg-primary) !important;
            transition: var(--transition-quick) !important;
            outline: none !important;
            box-shadow: none !important;
            line-height: normal !important;
            min-height: 48px !important;
            max-height: 48px !important;
            color: var(--text-primary) !important;
        }

        .enhanced-input:focus {
            border-color: var(--primary-color) !important;
            box-shadow: 0 0 0 4px var(--primary-light) !important;
            background: rgba(255, 255, 255, 1) !important;
        }

        .enhanced-input:hover {
            border-color: var(--primary-color) !important;
        }

        .enhanced-input::placeholder {
            color: var(--text-muted) !important;
            font-weight: 400 !important;
        }

        /* MTD组件样式重置 */
        .mtd-input {
            border: none !important;
            box-shadow: none !important;
            background: transparent !important;
        }

        .mtd-input:focus {
            border: none !important;
            box-shadow: none !important;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
        }

        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 20px;
        }

        .comparison-item {
            padding: 20px;
            border-radius: 12px;
            border: 1px solid var(--border-light);
        }

        .comparison-item h4 {
            margin: 0 0 15px 0;
            font-size: 16px;
            font-weight: 600;
        }

        .old-style {
            background: #fef2f2;
            border-color: #fecaca;
        }

        .new-style {
            background: #f0fdf4;
            border-color: #bbf7d0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-section">
            <h1 class="test-title">输入框样式修复测试</h1>
            
            <div class="test-subtitle">修复后的样式（参考代码审查页搜索框）</div>
            <div class="form-row">
                <div class="form-group">
                    <label class="form-label">姓名</label>
                    <input type="text" class="enhanced-input" placeholder="请输入您的姓名" value="张三">
                </div>
                <div class="form-group">
                    <label class="form-label">邮箱</label>
                    <input type="email" class="enhanced-input" placeholder="请输入邮箱地址" value="<EMAIL>">
                </div>
            </div>

            <div class="form-group">
                <label class="form-label">个人简介</label>
                <input type="text" class="enhanced-input" placeholder="简单介绍一下自己...">
            </div>

            <div class="test-subtitle">对比测试</div>
            <div class="comparison">
                <div class="comparison-item old-style">
                    <h4>修复前（MTD默认样式）</h4>
                    <input type="text" class="mtd-input" placeholder="旧样式输入框" value="双重边框问题">
                </div>
                <div class="comparison-item new-style">
                    <h4>修复后（增强样式）</h4>
                    <input type="text" class="enhanced-input mtd-input" placeholder="新样式输入框" value="统一设计风格">
                </div>
            </div>

            <div style="margin-top: 30px; padding: 20px; background: rgba(102, 126, 234, 0.1); border-radius: 12px; border: 1px solid rgba(102, 126, 234, 0.2);">
                <h4 style="margin: 0 0 10px 0; color: var(--primary-color);">修复要点：</h4>
                <ul style="margin: 0; padding-left: 20px; color: var(--text-secondary);">
                    <li>统一高度为 48px，与代码审查页搜索框一致</li>
                    <li>使用 2px 边框，圆角 16px</li>
                    <li>焦点时显示 4px 主色阴影</li>
                    <li>悬浮时边框变为主色</li>
                    <li>强制覆盖 MTD 组件的默认样式</li>
                    <li>移除双重边框问题</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
