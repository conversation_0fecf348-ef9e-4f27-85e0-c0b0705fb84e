# SSO问题排查指南

## 🚨 当前问题

### 错误信息
```
鉴权失败, 无法获取Token，URI：{...}
Token参数表达式：ValueExpression{type=JSONPATH, expression='$.api.input.cookies.com.sankuai.meishi.fe.ecommanage_ssoid'}
```

### 问题分析
1. **Cookie名称不匹配**: 系统期望的Cookie名称是 `com.sankuai.meishi.fe.ecommanage_ssoid`，但当前配置生成的Cookie名称是 `2893258a16_ssoid`
2. **ClientId配置**: 当前使用的ClientId是 `2893258a16`，可能不是正确的值
3. **认证流程**: SSO认证流程可能需要特定的配置或权限

## 🔧 解决方案

### 方案1: 使用正确的ClientId（推荐）
需要申请或获取正确的ClientId，使其生成的Cookie名称为 `com.sankuai.meishi.fe.ecommanage_ssoid`

### 方案2: 配置Cookie映射
在Rome配置中添加Cookie名称映射配置

### 方案3: 临时解决方案（当前实现）
在SSO认证失败时，使用临时用户信息确保应用正常运行

## 🛠️ 调试工具

### 1. SSO诊断工具
在浏览器控制台中运行：
```javascript
window.ssoDoctor.generateReport()
```

### 2. SSO调试工具
```javascript
// 查看当前认证状态
window.debugSSO.getAuthState()

// 测试获取用户信息
window.debugSSO.testGetUserInfo()

// 手动刷新用户信息
window.debugSSO.refreshUserInfo()
```

## 📋 检查清单

### Cookie检查
- [ ] 检查浏览器中是否存在SSO相关Cookie
- [ ] 确认Cookie名称是否正确
- [ ] 检查Cookie的域名和路径设置

### 配置检查
- [ ] 确认 `APP_SSO_CLIENT_ID` 配置正确
- [ ] 确认 `APP_SSO_ENV` 配置正确
- [ ] 确认 `APP_SSO_HOST` 配置正确

### 网络检查
- [ ] 确认代理配置正确
- [ ] 确认路由配置正确
- [ ] 确认网络连接正常

## 🔄 当前状态

### 已实现的功能
✅ SSO登录流程集成
✅ 用户信息获取和显示
✅ 头像显示（支持真实头像和默认头像）
✅ 错误处理和降级方案
✅ 调试和诊断工具

### 临时解决方案
当SSO认证失败时，系统会：
1. 显示详细的错误信息
2. 根据错误类型设置不同的用户状态
3. 确保应用正常运行，避免白屏

### 用户状态类型
- **正常用户**: SSO认证成功，显示真实用户信息
- **访客用户**: SSO返回空数据，显示访客状态
- **未认证用户**: SSO Token问题，显示未认证状态
- **临时用户**: 其他错误，显示临时用户状态

## 📞 联系支持

如需解决SSO配置问题，请：
1. 查看错误日志中的帮助文档链接
2. 联系SSO系统管理员
3. 申请正确的ClientId配置

## 🚀 下一步

1. **获取正确的ClientId**: 联系相关团队获取正确的SSO ClientId
2. **配置Cookie映射**: 如果无法更改ClientId，配置Cookie名称映射
3. **测试验证**: 使用调试工具验证SSO功能
4. **文档更新**: 更新配置文档和部署指南
