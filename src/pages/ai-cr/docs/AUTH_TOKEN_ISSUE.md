# 认证Token问题解决方案

## 🚨 问题描述

### 错误信息
```
606：未登录，鉴权失败，无法获取TOKEN
Token参数表达式：ValueExpression{type=JSONPATH, expression='$.api.input.cookies.agbsid'}
```

### 问题分析
1. **缺少agbsid Cookie**: 系统期望在Cookie中找到 `agbsid` 参数作为认证Token
2. **前端未传递Token**: 前端请求时没有在Cookie中包含必要的认证信息
3. **网关配置**: 网关API配置的Token取参位置与前端实际传输位置不一致

## 🔍 问题诊断

### 使用诊断工具
在浏览器控制台运行：
```javascript
window.ssoDoctor.generateReport()
```

### 手动检查Cookie
```javascript
// 检查当前所有Cookie
console.log(document.cookie)

// 检查特定Cookie
function getCookie(name) {
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) return parts.pop().split(';').shift();
}

console.log('agbsid:', getCookie('agbsid'))
```

## 🔧 解决方案

### 方案1: 确保认证流程完整
1. **检查登录状态**: 确保用户已完成完整的登录流程
2. **验证Cookie设置**: 确认登录后设置了正确的认证Cookie
3. **检查Cookie域名**: 确认Cookie的域名和路径设置正确

### 方案2: 修复Cookie传递
```javascript
// 在请求中确保包含认证Cookie
const response = await fetch('/api/endpoint', {
  method: 'GET',
  credentials: 'include', // 确保包含Cookie
  headers: {
    'Content-Type': 'application/json'
  }
})
```

### 方案3: 手动设置Cookie（临时方案）
```javascript
// 仅用于测试，不推荐在生产环境使用
document.cookie = "agbsid=your_token_value; path=/; domain=.meituan.com"
```

## 🛠️ 技术实现

### 检查axios配置
确保axios配置正确传递Cookie：

```typescript
// src/lib/axios.ts
const baseConfig = {
  timeout: 200000,
  withCredentials: true, // 确保包含Cookie
  headers: {
    'x-requested-with': 'XMLHttpRequest',
  },
}
```

### 添加请求拦截器
```typescript
// 添加请求拦截器来检查Cookie
instance.interceptors.request.use(
  (config) => {
    // 检查是否有必要的认证Cookie
    const agbsid = getCookie('agbsid')
    if (!agbsid) {
      console.warn('⚠️ 缺少认证Cookie: agbsid')
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)
```

## 📋 检查清单

### Cookie检查
- [ ] 检查浏览器中是否存在 `agbsid` Cookie
- [ ] 确认Cookie的值不为空
- [ ] 检查Cookie的域名设置（应该是 `.meituan.com` 或相应域名）
- [ ] 检查Cookie的路径设置（应该是 `/` 或相应路径）
- [ ] 确认Cookie没有过期

### 网络请求检查
- [ ] 确认请求头中包含 `credentials: 'include'`
- [ ] 确认axios配置中 `withCredentials: true`
- [ ] 检查浏览器开发者工具中的网络请求是否包含Cookie

### 认证流程检查
- [ ] 确认用户已完成登录流程
- [ ] 检查登录接口是否正确设置了Cookie
- [ ] 验证认证系统是否正常工作

## 🔄 临时解决方案

在认证问题解决之前，可以使用以下临时方案：

### 1. 模拟认证状态
```typescript
// 在authStore中添加模拟认证
if (process.env.NODE_ENV === 'development') {
  // 开发环境下使用模拟用户
  this.setUser({
    id: 'dev-user',
    email: '<EMAIL>',
    nickname: '开发用户',
    login_channel: 'dev',
    avatar: ''
  })
  this.setAuthenticated(true)
}
```

### 2. 跳过认证检查
```typescript
// 临时跳过某些需要认证的接口
const skipAuthForDev = process.env.NODE_ENV === 'development'
if (skipAuthForDev) {
  // 返回模拟数据
  return mockUserData
}
```

## 🚀 长期解决方案

### 1. 完善认证流程
- 实现完整的登录/登出流程
- 添加Token刷新机制
- 实现认证状态持久化

### 2. 改进错误处理
- 添加认证失败的用户友好提示
- 实现自动重定向到登录页面
- 添加认证状态监控

### 3. 安全性增强
- 实现CSRF保护
- 添加请求签名验证
- 实现安全的Token存储

## 📞 联系支持

如果问题持续存在，请：
1. 联系认证系统管理员
2. 检查网关配置是否正确
3. 确认前端和后端的Token传递方式一致
4. 查看相关文档：https://km.sankuai.com/page/218376223

## 🎯 总结

认证Token问题主要是由于缺少 `agbsid` Cookie导致的。解决方案包括：
1. 确保完整的登录流程
2. 正确配置Cookie传递
3. 验证网关和前端的Token处理一致性

通过使用提供的诊断工具和检查清单，可以快速定位和解决认证问题。
