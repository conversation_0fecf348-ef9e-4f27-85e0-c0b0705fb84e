# 路由系统说明

## 📋 概述

本项目采用统一的路由管理系统，确保路由逻辑清晰、易于维护。首页默认展示 AI 助手聊天界面，提供直观的用户体验。

## 🏗️ 路由架构

### 路由配置文件
- `config/routes.ts` - 统一的路由配置
- `components/RouteGuard.tsx` - 路由守卫组件
- `utils/navigation.ts` - 导航工具函数

### 路由结构
```
/ai-cr/                    # 应用基础路径
├── /                      # 首页 - AI助手聊天界面 ✨
├── /dashboard             # 仪表板
├── /code-review           # 代码审查
├── /rule-config           # 规则配置
└── /settings              # 设置
```

## 🎯 首页设计

### 默认路由
- **路径**: `/` (根路径)
- **组件**: `AIAssistant`
- **功能**: AI 助手聊天界面
- **特色**: 智能对话驱动的 Agent 系统

### 访问方式
```bash
# 直接访问首页
http://localhost:3002/ai-cr/

# 或者
http://localhost:3002/ai-cr

# Logo 点击也会回到首页
```

## 🔧 路由配置

### 路由定义
```typescript
// config/routes.ts
export const routes: RouteConfig[] = [
  {
    path: '/',
    icon: '🤖',
    label: 'AI助手',
    component: 'AIAssistant',
    exact: true,
    description: '智能对话驱动的 Agent 系统'
  },
  // ... 其他路由
]
```

### 动态路由生成
```typescript
// app.tsx
{routes.map((route) => (
  <Route
    key={route.path}
    exact={route.exact}
    path={route.path}
    component={getComponentByName(route.component)}
  />
))}
```

## 🛡️ 路由守卫

### 权限控制
```typescript
// config/routes.ts
export const routeGuards = {
  authRequired: ['/settings'],           // 需要认证
  permissionRequired: ['/rule-config'],  // 需要特殊权限
  publicRoutes: ['/', '/dashboard']      // 公开访问
}
```

### 守卫组件
```typescript
// components/RouteGuard.tsx
const RouteGuard: React.FC<RouteGuardProps> = ({ children }) => {
  // 检查认证和权限
  // 处理重定向逻辑
  return <>{children}</>
}
```

## 🧭 导航工具

### NavigationHelper 类
```typescript
import { createNavigationHelper } from '../utils/navigation'

const nav = createNavigationHelper(history)

// 基础导航
nav.navigateTo('/dashboard')
nav.goHome()
nav.goBack()

// 快捷方法
nav.goToDashboard()
nav.goToCodeReview()
nav.goToSettings()
```

### 路由工具函数
```typescript
import { routeUtils } from '../utils/navigation'

// 路径匹配
routeUtils.isPathMatch(currentPath, targetPath)

// 参数处理
routeUtils.getPathParams(path)
routeUtils.buildPathWithParams(basePath, params)

// 路径清理
routeUtils.cleanPath(path)
```

## 📊 页面标题管理

### 动态标题
```typescript
// Layout.tsx
React.useEffect(() => {
  document.title = getPageTitle(location.pathname)
}, [location.pathname])
```

### 标题格式
- 首页: `闪购 AI 平台`
- 其他页面: `页面名称 - 闪购 AI 平台`

## 🔄 路由变化监听

### 监听器管理
```typescript
import { routeChangeManager } from '../utils/navigation'

// 添加监听器
routeChangeManager.addListener((currentPath, previousPath) => {
  console.log(`路由从 ${previousPath} 变更到 ${currentPath}`)
})
```

### 自动通知
Layout 组件会自动通知路由变化，无需手动调用。

## 🍞 面包屑导航

### 自动生成
```typescript
import { breadcrumbUtils } from '../utils/navigation'

const breadcrumbs = breadcrumbUtils.generateBreadcrumbs(currentPath)
```

### 面包屑结构
- 非首页会自动包含首页链接
- 支持多级路径导航
- 显示图标和标签

## 🎨 用户体验优化

### Logo 交互
- 点击 Logo 始终回到首页
- 悬停效果和动画
- 视觉反馈

### 导航高亮
- 当前页面菜单项高亮显示
- 支持精确匹配和模糊匹配
- 工具提示显示页面描述

### 404 处理
- 未匹配路由自动重定向到首页
- 保持用户体验连续性

## 🚀 最佳实践

### 1. 路由跳转
```typescript
// ✅ 推荐：使用导航工具
const nav = createNavigationHelper(history)
nav.goHome()

// ✅ 也可以：直接使用 history
history.push('/')

// ❌ 避免：硬编码路径
window.location.href = '/ai-cr/'
```

### 2. 路由配置
```typescript
// ✅ 推荐：在 routes.ts 中统一配置
export const routes = [...]

// ❌ 避免：在组件中硬编码路由
<Route path="/hardcoded" component={Component} />
```

### 3. 权限检查
```typescript
// ✅ 推荐：使用路由守卫
<RouteGuard>
  <Component />
</RouteGuard>

// ❌ 避免：在组件内部检查权限
```

## 🔍 调试工具

### 路由信息查看
```typescript
import { getRouteByPath } from '../config/routes'

// 获取当前路由信息
const currentRoute = getRouteByPath(location.pathname)
console.log('当前路由:', currentRoute)
```

### 导航状态监控
```typescript
// 开发环境下启用路由调试
if (process.env.NODE_ENV === 'development') {
  routeChangeManager.addListener((current, previous) => {
    console.log(`🧭 路由变化: ${previous} → ${current}`)
  })
}
```

## 📝 注意事项

1. **basename 配置**: 应用使用 `/ai-cr` 作为基础路径
2. **精确匹配**: 大部分路由使用 `exact` 匹配
3. **重定向逻辑**: 404 页面重定向到首页而非错误页
4. **权限控制**: 预留了认证和权限检查接口
5. **SEO 友好**: 动态设置页面标题

## 🔗 相关文件

- `app.tsx` - 主应用路由配置
- `components/Layout.tsx` - 布局和导航
- `config/routes.ts` - 路由配置
- `utils/navigation.ts` - 导航工具
- `components/RouteGuard.tsx` - 路由守卫
