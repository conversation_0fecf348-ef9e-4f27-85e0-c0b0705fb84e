# Rome Stone 依赖管理指南

## 📋 概述

本项目严格遵循 Rome Stone 的开发依赖管控规范，通过统一的版本管理避免幽灵依赖风险，确保项目的稳定运行。

## 🏛️ Stone 依赖管控原理

### 版本锁定依赖
以下依赖被 Stone 强制锁定版本，无论是否自行安装，都会解析到 Stone 指定版本：

| 依赖名称 | React@17 版本 | React@18 版本 |
|---------|--------------|--------------|
| react | 17.0.2 | 18.3.1 |
| react-dom | 17.0.2 | 18.3.0 |
| react-router | 5.3.4 | 6.25.1 |
| react-router-dom | 5.3.4 | 6.25.1 |

### 管控依赖版本
| 依赖介绍 | 模块名称 | 维护的依赖 | 版本 |
|---------|---------|-----------|------|
| 状态库 | mobx | mobx | 6.12.3 |
| 状态库 | mobx-react | mobx-react | 9.1.1 |
| 请求库 | axios | axios | 0.21.1 |
| 鉴权相关 | runtime | @rome/runtime | 1.2.5 |

## 📦 项目中的使用方式

### 1. 统一依赖导出文件
我们创建了 `utils/dependencies.ts` 文件来统一管理所有 Stone 依赖：

```typescript
// 从 Stone 引入依赖
export { default as React } from '@rome/stone/react'
export { observer } from '@rome/stone/mobx-react'
export { BrowserRouter, Switch, Route } from '@rome/stone/react-router-dom'
```

### 2. 组件中的使用
在组件中统一从 dependencies.ts 引入：

```typescript
// ✅ 推荐写法
import { React, observer, useHistory } from '../utils/dependencies'

// ❌ 避免直接引入
import React from '@rome/stone/react'
import { observer } from '@rome/stone/mobx-react'
```

### 3. TypeScript 配置
确保 `tsconfig.json` 中配置了正确的路径映射：

```json
{
  "compilerOptions": {
    "paths": {
      "@/*": ["src/*"],
      "@rome/stone/*": ["node_modules/@rome/cli-plugin-stone/lib/*"]
    }
  }
}
```

## 🔧 开发最佳实践

### 1. 依赖引入规范
```typescript
// ✅ 正确：使用统一依赖管理
import { React, observer, makeAutoObservable } from '../utils/dependencies'

// ✅ 也可接受：直接从 Stone 引入
import { makeAutoObservable } from '@rome/stone/mobx'

// ❌ 避免：自行安装相同依赖
import { makeAutoObservable } from 'mobx' // 可能导致版本冲突
```

### 2. 新增依赖流程
1. 首先检查 Stone 是否已提供该依赖
2. 如果 Stone 提供，使用 Stone 版本
3. 如果 Stone 不提供，可自行安装
4. 更新 `dependencies.ts` 文件

### 3. 版本验证
开发环境下会自动打印依赖版本信息：

```typescript
// 开发环境下自动验证依赖版本
if (process.env.NODE_ENV === 'development') {
  validateStoneDependencies()
}
```

## 🚀 优势

### 1. 避免幽灵依赖
- 统一版本管理，避免不同版本冲突
- 减少 bundle 体积
- 提高构建稳定性

### 2. 版本一致性
- 团队开发环境一致
- 生产环境稳定可靠
- 减少因版本差异导致的问题

### 3. 维护便利性
- 集中管理依赖版本
- 升级时统一处理
- 便于问题排查

## 📝 注意事项

1. **强制锁定**：React 相关依赖被强制锁定，无法覆盖
2. **兼容性**：确保使用的 API 与 Stone 提供的版本兼容
3. **更新策略**：依赖更新需要等待 Stone 版本更新

## 🔍 问题排查

### 常见问题
1. **类型错误**：确保 TypeScript 路径映射正确
2. **版本冲突**：检查是否混用了 Stone 和自安装的依赖
3. **构建失败**：验证 Stone 版本是否为最新

### 调试工具
```typescript
// 查看当前使用的依赖版本
import { getDependencyVersions } from './utils/dependencies'
console.log(getDependencyVersions())
```

## 📚 参考资料

- [Rome Stone 官方文档](https://sky.sankuai.com/docs/nibfe/rome-doc/)
- [依赖管控详细说明](https://sky.sankuai.com/docs/nibfe/rome-doc/plugins/infrastructure/dependencies.html)
