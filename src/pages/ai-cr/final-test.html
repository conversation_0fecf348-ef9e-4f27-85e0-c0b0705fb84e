<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最终修复测试 - 模拟React组件</title>
    <style>
        /* 导入Settings.scss的关键样式 */
        :root {
            /* 主色系 */
            --primary-color: #667eea;
            --primary-light: rgba(102, 126, 234, 0.1);
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

            /* 文字色系 */
            --text-primary: #1a202c;
            --text-secondary: #4a5568;
            --text-muted: #718096;

            /* 背景色系 */
            --bg-primary: rgba(255, 255, 255, 0.95);
            --bg-glass: rgba(255, 255, 255, 0.8);
            --border-light: rgba(226, 232, 240, 0.4);

            /* 动画系统 */
            --transition-quick: all 0.2s ease;
            --transition-normal: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

            /* 阴影系统 */
            --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.06);
            --blur-md: blur(20px);
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            margin: 0;
            padding: 20px;
        }

        /* 模拟MTD组件的默认样式 */
        .mtd-input {
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            padding: 8px 12px;
            font-size: 14px;
            background: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            height: 32px;
            line-height: 1.5;
        }

        .mtd-input:focus {
            border-color: #40a9ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .mtd-input-wrapper {
            position: relative;
            display: inline-block;
            width: 100%;
        }

        /* 应用我们的修复样式 */
        .elegant-content .settings-page {
            /* 强制应用我们的样式 */
        }

        /* 全局MTD组件样式重置 - 最高优先级 */
        .settings-page .mtd-input {
            border: none !important;
            box-shadow: none !important;
            background: transparent !important;
        }

        .settings-page .mtd-input:focus {
            border: none !important;
            box-shadow: none !important;
        }

        /* 额外的MTD组件覆盖 */
        .settings-page .mtd-input-wrapper {
            border: none !important;
            box-shadow: none !important;
        }

        .settings-page .mtd-input-inner {
            border: none !important;
            box-shadow: none !important;
        }

        /* 增强输入框样式 */
        .settings-page .enhanced-input,
        .settings-page .enhanced-input.mtd-input,
        .settings-page .mtd-input.enhanced-input,
        .settings-page input.enhanced-input,
        .settings-page input.mtd-input.enhanced-input,
        .settings-page .mtd-input-wrapper .enhanced-input,
        .settings-page .mtd-input-wrapper input.enhanced-input {
            width: 100% !important;
            height: 48px !important;
            padding: 0 16px !important;
            border: 2px solid var(--border-light) !important;
            border-radius: 16px !important;
            font-size: 14px !important;
            font-weight: 500 !important;
            background: var(--bg-primary) !important;
            transition: var(--transition-quick) !important;
            outline: none !important;
            box-shadow: none !important;
            line-height: normal !important;
            min-height: 48px !important;
            max-height: 48px !important;
            color: var(--text-primary) !important;
            position: relative !important;
            z-index: 1 !important;
        }

        .settings-page .enhanced-input:focus,
        .settings-page .enhanced-input.mtd-input:focus,
        .settings-page .mtd-input.enhanced-input:focus,
        .settings-page input.enhanced-input:focus,
        .settings-page input.mtd-input.enhanced-input:focus,
        .settings-page .mtd-input-wrapper .enhanced-input:focus,
        .settings-page .mtd-input-wrapper input.enhanced-input:focus {
            border-color: var(--primary-color) !important;
            box-shadow: 0 0 0 4px var(--primary-light) !important;
            background: rgba(255, 255, 255, 1) !important;
        }

        .settings-page .enhanced-input:hover,
        .settings-page .enhanced-input.mtd-input:hover,
        .settings-page .mtd-input.enhanced-input:hover,
        .settings-page input.enhanced-input:hover,
        .settings-page input.mtd-input.enhanced-input:hover,
        .settings-page .mtd-input-wrapper .enhanced-input:hover,
        .settings-page .mtd-input-wrapper input.enhanced-input:hover {
            border-color: var(--primary-color) !important;
        }

        .settings-page .enhanced-input::placeholder,
        .settings-page .enhanced-input.mtd-input::placeholder,
        .settings-page .mtd-input.enhanced-input::placeholder,
        .settings-page input.enhanced-input::placeholder,
        .settings-page input.mtd-input.enhanced-input::placeholder,
        .settings-page .mtd-input-wrapper .enhanced-input::placeholder,
        .settings-page .mtd-input-wrapper input.enhanced-input::placeholder {
            color: var(--text-muted) !important;
            font-weight: 400 !important;
        }

        /* 页面布局样式 */
        .settings-page {
            max-width: 1200px;
            margin: 0 auto;
        }

        .settings-header {
            background: var(--bg-glass);
            backdrop-filter: var(--blur-md);
            border-radius: 20px;
            border: 1px solid var(--border-light);
            padding: 32px;
            margin-bottom: 32px;
            box-shadow: var(--shadow-sm);
        }

        .header-title h1 {
            margin: 0 0 4px 0;
            font-size: 28px;
            font-weight: 800;
            color: var(--text-primary);
            letter-spacing: -0.5px;
        }

        .header-title p {
            margin: 0;
            font-size: 14px;
            color: var(--text-muted);
            font-weight: 500;
        }

        .settings-section {
            background: var(--bg-glass);
            backdrop-filter: var(--blur-md);
            border-radius: 20px;
            border: 1px solid var(--border-light);
            padding: 32px;
            margin-bottom: 32px;
            box-shadow: var(--shadow-sm);
        }

        .form-group {
            margin-bottom: 24px;
        }

        .form-group label {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
            margin-bottom: 24px;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
        }

        .test-info {
            background: rgba(102, 126, 234, 0.1);
            border: 1px solid rgba(102, 126, 234, 0.2);
            border-radius: 12px;
            padding: 20px;
            margin-top: 30px;
        }

        .test-info h3 {
            margin: 0 0 10px 0;
            color: var(--primary-color);
            font-size: 16px;
        }

        .test-info ul {
            margin: 0;
            padding-left: 20px;
            color: var(--text-secondary);
        }
    </style>
</head>
<body>
    <div class="elegant-content">
        <div class="settings-page">
            <!-- 页面头部 -->
            <div class="settings-header">
                <div class="header-title">
                    <h1>系统设置</h1>
                    <p>个人信息、通知偏好和系统配置</p>
                </div>
            </div>

            <!-- 设置内容 -->
            <div class="settings-section">
                <h3 style="margin: 0 0 24px 0; font-size: 20px; font-weight: 700; color: var(--text-primary);">个人资料</h3>
                
                <div class="form-row">
                    <div class="form-group">
                        <label>👤 昵称</label>
                        <div class="mtd-input-wrapper">
                            <input type="text" class="enhanced-input mtd-input" placeholder="请输入昵称" value="张三">
                        </div>
                    </div>
                    <div class="form-group">
                        <label>✉️ 邮箱</label>
                        <div class="mtd-input-wrapper">
                            <input type="email" class="enhanced-input mtd-input" placeholder="请输入邮箱" value="<EMAIL>">
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label>🖼️ 头像 URL</label>
                    <div class="mtd-input-wrapper">
                        <input type="text" class="enhanced-input mtd-input" placeholder="请输入头像 URL">
                    </div>
                </div>

                <div class="test-info">
                    <h3>✅ 修复验证</h3>
                    <ul>
                        <li>输入框高度统一为 48px</li>
                        <li>边框为 2px，圆角 16px</li>
                        <li>焦点时显示主色边框和阴影</li>
                        <li>悬浮时边框变为主色</li>
                        <li>完全覆盖 MTD 组件默认样式</li>
                        <li>标题显示正常，无渐变色问题</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
