// 使用 Stone 统一依赖管理 - 基于参考项目的精确复制
import React, { useState } from '@rome/stone/react'
import { Input, Select, Switch } from '@ss/mtd-react'
import { observer } from '@rome/stone/mobx-react'
import authStore from '../store/authStore'
// React Icons 导入 - 与代码审查页保持一致
import {
  HiOutlineUser,
  HiOutlineBell,
  HiOutlineAdjustmentsHorizontal,
  HiOutlineLink,
  HiOutlineShieldCheck,
  HiOutlineGlobeAlt,
  HiOutlineEnvelope,
  HiOutlinePhoto,
  HiOutlineArrowPath,
  HiOutlineCheckCircle,
  HiOutlineExclamationTriangle,
  HiOutlineInformationCircle,
  HiOutlineXMark,
  HiOutlineChartBarSquare,
  HiOutlinePaintBrush,
  HiOutlineRocketLaunch,
  HiOutlineMagnifyingGlass,
  HiOutlineClock
} from 'react-icons/hi2'
import './Settings.css'

interface SettingsData {
  profile: {
    nickname: string
    email: string
    avatar: string
  }
  notifications: {
    emailNotifications: boolean
    reviewReminders: boolean
    criticalIssueAlerts: boolean
    weeklyReports: boolean
  }
  preferences: {
    language: string
    theme: string
    defaultReviewMode: string
    autoStartReview: boolean
  }
  integration: {
    gitlabToken: string
    slackWebhook: string
    jiraUrl: string
  }
}

const Settings: React.FC = observer(() => {
  // 统一的输入框样式 - 确保覆盖MTD组件
  const enhancedInputStyle = {
    width: '100%',
    height: '48px',
    padding: '0 16px',
    border: '2px solid rgba(226, 232, 240, 0.4)',
    borderRadius: '16px',
    fontSize: '14px',
    fontWeight: '500',
    background: 'rgba(255, 255, 255, 0.95)',
    outline: 'none',
    boxShadow: 'none',
    lineHeight: '44px',
    verticalAlign: 'middle',
    display: 'block'
  }

  // 强制样式覆盖 - 确保在任何情况下都能生效
  React.useEffect(() => {
    const styleId = 'settings-force-styles'
    if (!document.getElementById(styleId)) {
      const style = document.createElement('style')
      style.id = styleId
      style.textContent = `
        /* 强制覆盖MTD组件样式 - 最高优先级 */
        .settings-page .enhanced-input,
        .settings-page input.enhanced-input,
        .settings-page .mtd-input.enhanced-input,
        .settings-page .mtd-input-wrapper .enhanced-input {
          width: 100% !important;
          height: 48px !important;
          padding: 0 16px !important;
          border: 2px solid rgba(226, 232, 240, 0.4) !important;
          border-radius: 16px !important;
          font-size: 14px !important;
          font-weight: 500 !important;
          background: rgba(255, 255, 255, 0.95) !important;
          outline: none !important;
          box-shadow: none !important;
          line-height: 44px !important;
          vertical-align: middle !important;
          display: block !important;
          color: #1e293b !important;
          font-family: inherit !important;
          transition: all 0.2s ease !important;
        }

        .settings-page .enhanced-input:focus,
        .settings-page input.enhanced-input:focus,
        .settings-page .mtd-input.enhanced-input:focus,
        .settings-page .mtd-input-wrapper .enhanced-input:focus {
          border-color: #667eea !important;
          box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1) !important;
          background: rgba(255, 255, 255, 1) !important;
          outline: none !important;
        }

        .settings-page .enhanced-input:hover,
        .settings-page input.enhanced-input:hover,
        .settings-page .mtd-input.enhanced-input:hover,
        .settings-page .mtd-input-wrapper .enhanced-input:hover {
          border-color: #667eea !important;
        }

        .settings-page .enhanced-input::placeholder,
        .settings-page input.enhanced-input::placeholder,
        .settings-page .mtd-input.enhanced-input::placeholder,
        .settings-page .mtd-input-wrapper .enhanced-input::placeholder {
          color: #64748b !important;
          font-weight: 400 !important;
          line-height: 44px !important;
          vertical-align: middle !important;
          opacity: 1 !important;
        }

        /* 重置MTD组件容器样式 */
        .settings-page .mtd-input-wrapper,
        .settings-page .mtd-input-affix-wrapper,
        .settings-page .mtd-input-content {
          border: none !important;
          box-shadow: none !important;
          background: transparent !important;
        }

        /* 覆盖全局焦点样式 */
        .settings-page input:focus-visible,
        .settings-page .enhanced-input:focus-visible {
          outline: none !important;
          outline-offset: 0 !important;
        }
      `
      document.head.appendChild(style)
    }

    return () => {
      const existingStyle = document.getElementById(styleId)
      if (existingStyle) {
        existingStyle.remove()
      }
    }
  }, [])

  const [settings, setSettings] = useState<SettingsData>({
    profile: {
      nickname: authStore.user?.nickname || '',
      email: authStore.user?.email || '',
      avatar: authStore.user?.avatar || ''
    },
    notifications: {
      emailNotifications: true,
      reviewReminders: true,
      criticalIssueAlerts: true,
      weeklyReports: false
    },
    preferences: {
      language: 'zh-CN',
      theme: 'light',
      defaultReviewMode: 'deep',
      autoStartReview: false
    },
    integration: {
      gitlabToken: '',
      slackWebhook: '',
      jiraUrl: ''
    }
  })

  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState('profile')
  const [hasChanges, setHasChanges] = useState(false)

  const handleSave = async () => {
    setLoading(true)
    try {
      // 模拟保存设置
      await new Promise(resolve => setTimeout(resolve, 1500))

      // 更新用户信息
      if (activeTab === 'profile') {
        authStore.updateUser({
          nickname: settings.profile.nickname,
          email: settings.profile.email,
          avatar: settings.profile.avatar
        })
      }

      // 保存其他设置到 localStorage
      localStorage.setItem('app_settings', JSON.stringify(settings))

      setHasChanges(false)
      alert('设置保存成功！')
    } catch (error) {
      console.error('Failed to save settings:', error)
      alert('保存失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  const updateSettings = (section: keyof SettingsData, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [key]: value
      }
    }))
    setHasChanges(true)
  }

  const resetSettings = () => {
    if (window.confirm('确定要重置所有设置吗？此操作不可撤销。')) {
      window.location.reload()
    }
  }

  const tabs = [
    {
      key: 'profile',
      label: '个人信息',
      icon: React.createElement(HiOutlineUser as any),
      description: '管理个人资料和账户信息',
      color: '#667eea'
    },
    {
      key: 'notifications',
      label: '通知设置',
      icon: React.createElement(HiOutlineBell as any),
      description: '配置通知偏好和提醒方式',
      color: '#f59e0b'
    },
    {
      key: 'preferences',
      label: '偏好设置',
      icon: React.createElement(HiOutlineAdjustmentsHorizontal as any),
      description: '自定义系统行为和界面',
      color: '#10b981'
    },
    {
      key: 'integration',
      label: '集成配置',
      icon: React.createElement(HiOutlineLink as any),
      description: '第三方服务集成配置',
      color: '#8b5cf6'
    }
  ]

  const getTabStats = (tabKey: string) => {
    switch (tabKey) {
      case 'profile':
        const profileComplete = Object.values(settings.profile).filter(v => v.trim()).length
        return `${profileComplete}/3 已完成`
      case 'notifications':
        const enabledNotifications = Object.values(settings.notifications).filter(v => v).length
        return `${enabledNotifications}/4 已启用`
      case 'preferences':
        return settings.preferences.theme === 'auto' ? '自动主题' : '已配置'
      case 'integration':
        const connectedServices = Object.values(settings.integration).filter(v => v.trim()).length
        return `${connectedServices}/3 已连接`
      default:
        return ''
    }
  }

  const renderProfileSettings = () => (
    <div className="settings-section animate-fadeIn">
      <div className="section-header">
        <div className="section-title-group">
          <div className="section-icon" style={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' }}>
            {React.createElement(HiOutlineUser as any)}
          </div>
          <div>
            <h3>个人资料</h3>
            <p>管理您的个人信息和账户设置</p>
          </div>
        </div>
        <div className="section-stats">
          <span className="stats-badge">
            {getTabStats('profile')}
          </span>
        </div>
      </div>

      <div className="settings-form">
        <div className="form-row">
          <div className="form-group">
            <label>
              <span className="label-icon">{React.createElement(HiOutlineUser as any)}</span>
              昵称
            </label>
            <input
              type="text"
              value={settings.profile.nickname}
              onChange={(e) => updateSettings('profile', 'nickname', e.target.value)}
              placeholder="请输入昵称"
              className="custom-input"
            />
          </div>

          <div className="form-group">
            <label>
              <span className="label-icon">{React.createElement(HiOutlineEnvelope as any)}</span>
              邮箱
            </label>
            <input
              type="email"
              value={settings.profile.email}
              onChange={(e) => updateSettings('profile', 'email', e.target.value)}
              placeholder="请输入邮箱"
              className="custom-input"
            />
          </div>
        </div>

        <div className="form-group">
          <label>
            <span className="label-icon">{React.createElement(HiOutlinePhoto as any)}</span>
            头像 URL
          </label>
          <input
            type="url"
            value={settings.profile.avatar}
            onChange={(e) => updateSettings('profile', 'avatar', e.target.value)}
            placeholder="请输入头像 URL"
            className="custom-input"
          />
          <div className="form-help">
            <span className="help-icon">{React.createElement(HiOutlineInformationCircle as any)}</span>
            支持 HTTP/HTTPS 图片链接，建议使用 1:1 比例的图片
          </div>
        </div>

        {settings.profile.avatar && (
          <div className="avatar-preview">
            <div className="preview-label">头像预览</div>
            <img
              src={settings.profile.avatar}
              alt="头像预览"
              className="avatar-image"
              onError={(e) => {
                e.currentTarget.style.display = 'none'
              }}
            />
          </div>
        )}
      </div>
    </div>
  )

  const renderNotificationSettings = () => (
    <div className="settings-section animate-fadeIn">
      <div className="section-header">
        <div className="section-title-group">
          <div className="section-icon" style={{ background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)' }}>
            {React.createElement(HiOutlineBell as any)}
          </div>
          <div>
            <h3>通知偏好</h3>
            <p>控制您接收通知的方式和频率</p>
          </div>
        </div>
        <div className="section-stats">
          <span className="stats-badge">
            {getTabStats('notifications')}
          </span>
        </div>
      </div>

      <div className="settings-form">
        <div className="switch-group">
          <div className="switch-item enhanced">
            <div className="switch-content">
              <div className="switch-header">
                <span className="switch-icon">{React.createElement(HiOutlineEnvelope as any)}</span>
                <div className="switch-title">邮件通知</div>
              </div>
              <div className="switch-description">接收系统邮件通知和重要更新</div>
            </div>
            <Switch
              checked={settings.notifications.emailNotifications}
              onChange={(e) => updateSettings('notifications', 'emailNotifications', e.target.checked)}
            />
          </div>

          <div className="switch-item enhanced">
            <div className="switch-content">
              <div className="switch-header">
                <span className="switch-icon">{React.createElement(HiOutlineClock as any)}</span>
                <div className="switch-title">审查提醒</div>
              </div>
              <div className="switch-description">待审查 PR 的定时提醒通知</div>
            </div>
            <Switch
              checked={settings.notifications.reviewReminders}
              onChange={(e) => updateSettings('notifications', 'reviewReminders', e.target.checked)}
            />
          </div>

          <div className="switch-item enhanced critical">
            <div className="switch-content">
              <div className="switch-header">
                <span className="switch-icon">{React.createElement(HiOutlineExclamationTriangle as any)}</span>
                <div className="switch-title">严重问题警报</div>
              </div>
              <div className="switch-description">发现严重代码问题时立即通知</div>
            </div>
            <Switch
              checked={settings.notifications.criticalIssueAlerts}
              onChange={(e) => updateSettings('notifications', 'criticalIssueAlerts', e.target.checked)}
            />
          </div>

          <div className="switch-item enhanced">
            <div className="switch-content">
              <div className="switch-header">
                <span className="switch-icon">{React.createElement(HiOutlineChartBarSquare as any)}</span>
                <div className="switch-title">周报</div>
              </div>
              <div className="switch-description">每周代码审查统计报告和趋势分析</div>
            </div>
            <Switch
              checked={settings.notifications.weeklyReports}
              onChange={(e) => updateSettings('notifications', 'weeklyReports', e.target.checked)}
            />
          </div>
        </div>
      </div>
    </div>
  )

  const renderPreferenceSettings = () => (
    <div className="settings-section animate-fadeIn">
      <div className="section-header">
        <div className="section-title-group">
          <div className="section-icon" style={{ background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)' }}>
            {React.createElement(HiOutlineAdjustmentsHorizontal as any)}
          </div>
          <div>
            <h3>系统偏好</h3>
            <p>自定义系统行为和界面设置</p>
          </div>
        </div>
        <div className="section-stats">
          <span className="stats-badge">
            {getTabStats('preferences')}
          </span>
        </div>
      </div>

      <div className="settings-form">
        <div className="form-row">
          <div className="form-group">
            <label>
              <span className="label-icon">{React.createElement(HiOutlineGlobeAlt as any)}</span>
              语言
            </label>
            <Select
              value={settings.preferences.language}
              onChange={(value) => updateSettings('preferences', 'language', value)}
              style={{ width: '100%' }}
              className="enhanced-select"
            >
              <Select.Option value="zh-CN">🇨🇳 简体中文</Select.Option>
              <Select.Option value="en-US">🇺🇸 English</Select.Option>
            </Select>
          </div>

          <div className="form-group">
            <label>
              <span className="label-icon">{React.createElement(HiOutlinePaintBrush as any)}</span>
              主题
            </label>
            <Select
              value={settings.preferences.theme}
              onChange={(value) => updateSettings('preferences', 'theme', value)}
              style={{ width: '100%' }}
              className="enhanced-select"
            >
              <Select.Option value="light">☀️ 浅色主题</Select.Option>
              <Select.Option value="dark">🌙 深色主题</Select.Option>
              <Select.Option value="auto">🔄 跟随系统</Select.Option>
            </Select>
          </div>
        </div>

        <div className="form-group">
          <label>
            <span className="label-icon">{React.createElement(HiOutlineMagnifyingGlass as any)}</span>
            默认审查模式
          </label>
          <Select
            value={settings.preferences.defaultReviewMode}
            onChange={(value) => updateSettings('preferences', 'defaultReviewMode', value)}
            style={{ width: '100%' }}
            className="enhanced-select"
          >
            <Select.Option value="fast">⚡ 快速审查</Select.Option>
            <Select.Option value="deep">🔬 深度审查</Select.Option>
            <Select.Option value="security">🛡️ 安全审查</Select.Option>
          </Select>
          <div className="form-help">
            <span className="help-icon">{React.createElement(HiOutlineInformationCircle as any)}</span>
            新建审查任务时的默认模式，可在创建时修改
          </div>
        </div>

        <div className="switch-group">
          <div className="switch-item enhanced">
            <div className="switch-content">
              <div className="switch-header">
                <span className="switch-icon">{React.createElement(HiOutlineRocketLaunch as any)}</span>
                <div className="switch-title">自动开始审查</div>
              </div>
              <div className="switch-description">新 PR 创建时自动启动代码审查流程</div>
            </div>
            <Switch
              checked={settings.preferences.autoStartReview}
              onChange={(e) => updateSettings('preferences', 'autoStartReview', e.target.checked)}
            />
          </div>
        </div>
      </div>
    </div>
  )

  const renderIntegrationSettings = () => (
    <div className="settings-section animate-fadeIn">
      <div className="section-header">
        <div className="section-title-group">
          <div className="section-icon" style={{ background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)' }}>
            {React.createElement(HiOutlineLink as any)}
          </div>
          <div>
            <h3>第三方集成</h3>
            <p>配置与外部服务的集成</p>
          </div>
        </div>
        <div className="section-stats">
          <span className="stats-badge">
            {getTabStats('integration')}
          </span>
        </div>
      </div>

      <div className="settings-form">
        <div className="integration-card">
          <div className="integration-header">
            <div className="integration-logo">
              <span style={{ fontSize: '24px' }}>🦊</span>
            </div>
            <div className="integration-info">
              <h4>GitLab</h4>
              <p>连接 GitLab 获取 PR 信息和代码变更</p>
            </div>
            <div className="integration-status">
              {settings.integration.gitlabToken ? (
                <span className="status-badge connected">
                  {React.createElement(HiOutlineCheckCircle as any)} 已连接
                </span>
              ) : (
                <span className="status-badge disconnected">
                  {React.createElement(HiOutlineXMark as any)} 未连接
                </span>
              )}
            </div>
          </div>
          <div className="form-group">
            <label>
              <span className="label-icon">{React.createElement(HiOutlineShieldCheck as any)}</span>
              Personal Access Token
            </label>
            <Input
              value={settings.integration.gitlabToken}
              onChange={(e) => updateSettings('integration', 'gitlabToken', e.target.value)}
              placeholder="请输入 GitLab Personal Access Token"
              type="password"
              className="enhanced-input"
              style={enhancedInputStyle}
            />
            <div className="form-help">
              <span className="help-icon">{React.createElement(HiOutlineInformationCircle as any)}</span>
              需要 api、read_user、read_repository 权限
            </div>
          </div>
        </div>

        <div className="integration-card">
          <div className="integration-header">
            <div className="integration-logo">
              <span style={{ fontSize: '24px' }}>💬</span>
            </div>
            <div className="integration-info">
              <h4>Slack</h4>
              <p>发送审查结果和通知到 Slack 频道</p>
            </div>
            <div className="integration-status">
              {settings.integration.slackWebhook ? (
                <span className="status-badge connected">
                  {React.createElement(HiOutlineCheckCircle as any)} 已连接
                </span>
              ) : (
                <span className="status-badge disconnected">
                  {React.createElement(HiOutlineXMark as any)} 未连接
                </span>
              )}
            </div>
          </div>
          <div className="form-group">
            <label>
              <span className="label-icon">{React.createElement(HiOutlineLink as any)}</span>
              Webhook URL
            </label>
            <Input
              value={settings.integration.slackWebhook}
              onChange={(e) => updateSettings('integration', 'slackWebhook', e.target.value)}
              placeholder="请输入 Slack Webhook URL"
              className="enhanced-input"
              style={enhancedInputStyle}
            />
            <div className="form-help">
              <span className="help-icon">{React.createElement(HiOutlineInformationCircle as any)}</span>
              在 Slack 应用设置中创建 Incoming Webhook
            </div>
          </div>
        </div>

        <div className="integration-card">
          <div className="integration-header">
            <div className="integration-logo">
              <span style={{ fontSize: '24px' }}>📋</span>
            </div>
            <div className="integration-info">
              <h4>JIRA</h4>
              <p>关联 JIRA 任务和代码审查</p>
            </div>
            <div className="integration-status">
              {settings.integration.jiraUrl ? (
                <span className="status-badge connected">已连接</span>
              ) : (
                <span className="status-badge disconnected">未连接</span>
              )}
            </div>
          </div>
          <div className="form-group">
            <label>JIRA 实例 URL</label>
            <Input
              value={settings.integration.jiraUrl}
              onChange={(e) => updateSettings('integration', 'jiraUrl', e.target.value)}
              placeholder="https://your-company.atlassian.net"
              className="enhanced-input"
              style={enhancedInputStyle}
            />
            <div className="form-help">
              <span className="help-icon">🌐</span>
              您的 JIRA 实例的完整 URL 地址
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  const renderTabContent = () => {
    switch (activeTab) {
      case 'profile': return renderProfileSettings()
      case 'notifications': return renderNotificationSettings()
      case 'preferences': return renderPreferenceSettings()
      case 'integration': return renderIntegrationSettings()
      default: return null
    }
  }

  return (
    <div className="settings-page">
      {/* 页面头部 */}
      <div className="settings-header">
        <div className="settings-header-content">
          <div className="header-title">
            <h1>系统设置</h1>
            <p>个人信息、通知偏好和系统配置</p>
          </div>

          <div className="header-actions">
            {hasChanges && (
              <div className="changes-indicator">
                <span className="changes-dot animate-pulse"></span>
                有未保存的更改
              </div>
            )}
            <button
              className="action-btn secondary"
              onClick={resetSettings}
            >
              {React.createElement(HiOutlineArrowPath as any)} 重置
            </button>
            <button
              className="action-btn primary"
              onClick={handleSave}
              disabled={loading}
            >
              {loading ? (
                <>
                  <div className="loading-spinner" />
                  保存中...
                </>
              ) : (
                <>
                  {React.createElement(HiOutlineCheckCircle as any)} 保存设置
                </>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* 页面内容 */}
      <div className="settings-content">
        {/* 左侧导航 */}
        <div className="settings-nav">
          <div className="settings-nav-header">
            <h3>设置分类</h3>
            <p>选择要配置的设置类别</p>
          </div>

          <div className="settings-nav-list">
            {tabs.map((tab) => (
              <div
                key={tab.key}
                onClick={() => setActiveTab(tab.key)}
                className={`settings-nav-item ${activeTab === tab.key ? 'active' : ''}`}
                data-color={tab.color}
              >
                <div className="nav-item-indicator" />

                <div className="nav-item-content">
                  <div className="nav-item-header">
                    <div className="nav-item-icon" style={{ background: `linear-gradient(135deg, ${tab.color}20, ${tab.color}10)`, border: `1px solid ${tab.color}30` }}>
                      {tab.icon}
                    </div>
                    <div className="nav-item-info">
                      <div className="nav-item-title">{tab.label}</div>
                      <div className="nav-item-stats" style={{ color: tab.color, background: `${tab.color}15` }}>
                        {getTabStats(tab.key)}
                      </div>
                    </div>
                  </div>
                  <div className="nav-item-description">
                    {tab.description}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 右侧内容 */}
        <div className="settings-main">
          {renderTabContent()}
        </div>
      </div>
    </div>
  )
})

export default Settings
