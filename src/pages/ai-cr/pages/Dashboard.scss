/* Dashboard 页面样式 - 高级感优化版本 */
@import '../styles/variables';
@import '../styles/mixins';

/* 页面容器 - 增强版本 */
.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #f1f5f9 100%);
  position: relative;
  overflow-x: hidden;

  /* 添加动态背景效果 */
  &::before {
    content: '';
    position: fixed;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle at 25% 25%, rgba(102, 126, 234, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(118, 75, 162, 0.03) 0%, transparent 50%);
    animation: backgroundFloat 20s ease-in-out infinite;
    pointer-events: none;
    z-index: 0;
  }
}

/* 页面内容区域 */
.page-content {
  position: relative;
  z-index: 1;
  padding: 32px 24px;
  max-width: 2400px;
  margin: 0 auto;
  animation: pageSlideIn 0.8s cubic-bezier(0.16, 1, 0.3, 1);
}

/* 统计卡片网格 - 增强版本 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: spacing(2xl);
  margin-bottom: spacing(4xl);

  @include mobile {
    grid-template-columns: 1fr;
    gap: spacing(xl);
  }
}

/* 统计卡片 - 重新设计 */
.stat-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: radius(xl);
  padding: spacing(2xl);
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);

  /* 悬浮光效 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.6s ease;
  }

  &:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    border-color: rgba(102, 126, 234, 0.3);

    &::before {
      left: 100%;
    }

    .stat-card-icon {
      transform: scale(1.1) rotate(5deg);
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    }

    .stat-value {
      transform: scale(1.05);
      color: $primary-color;
    }
  }

  /* 激活状态动画 */
  &:active {
    transform: translateY(-4px) scale(1.01);
  }
}

/* 统计卡片头部 */
.stat-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: spacing(lg);
}

/* 统计卡片图标 - 增强版本 */
.stat-card-icon {
  width: 56px;
  height: 56px;
  border-radius: radius(lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);

  /* 内部光效 */
  &::after {
    content: '';
    position: absolute;
    top: 10%;
    left: 10%;
    width: 80%;
    height: 80%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%);
    border-radius: inherit;
    pointer-events: none;
  }

  &.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  &.warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  }

  &.success {
    background: linear-gradient(135deg, #10b981 0%, #047857 100%);
  }

  &.error {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  }
}

/* 统计数值 - 增强版本 */
.stat-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: color(primary);
  margin: spacing(md) 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  letter-spacing: -1px;

  @include mobile {
    font-size: 2rem;
  }
}

/* 统计标签 */
.stat-label {
  font-size: map-get($font-size, base);
  color: color(secondary);
  font-weight: map-get($font-weight, semibold);
  margin-bottom: spacing(sm);
}

/* 统计变化 - 增强版本 */
.stat-change {
  display: flex;
  align-items: center;
  gap: spacing(xs);
  font-size: map-get($font-size, sm);
  font-weight: map-get($font-weight, medium);
  padding: spacing(xs) spacing(sm);
  border-radius: radius(sm);
  transition: all 0.3s ease;

  &.positive {
    color: $success-color;
    background: rgba(16, 185, 129, 0.1);
  }

  &.negative {
    color: $error-color;
    background: rgba(239, 68, 68, 0.1);
  }

  &.neutral {
    color: color(tertiary);
    background: rgba(107, 114, 128, 0.1);
  }
}

/* 内容网格 */
.content-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: spacing(3xl);

  @include tablet {
    grid-template-columns: 1fr;
    gap: spacing(2xl);
  }
}

/* 图表卡片 - 重新设计 */
.chart-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: radius(xl);
  padding: spacing(2xl);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  /* 微妙的边框光效 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.3), transparent);
  }

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12);
    border-color: rgba(102, 126, 234, 0.2);
  }
}

/* 图表标题 - 增强版本 */
.chart-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: spacing(xl);
  font-size: map-get($font-size, lg);
  font-weight: map-get($font-weight, bold);
  color: color(primary);
  gap: spacing(sm);

  /* 图标样式 */
  svg {
    color: $primary-color;
    filter: drop-shadow(0 2px 4px rgba(102, 126, 234, 0.3));
  }

  a {
    color: $primary-color;
    text-decoration: none;
    font-size: map-get($font-size, sm);
    font-weight: map-get($font-weight, medium);
    padding: spacing(xs) spacing(md);
    border-radius: radius(md);
    transition: all 0.2s ease;
    position: relative;

    &::after {
      content: '→';
      margin-left: spacing(xs);
      transition: transform 0.2s ease;
    }

    &:hover {
      background: rgba(102, 126, 234, 0.1);
      transform: translateX(2px);

      &::after {
        transform: translateX(2px);
      }
    }
  }
}

/* 活动列表 - 重新设计 */
.activity-list {
  display: flex;
  flex-direction: column;
  gap: spacing(lg);
}

/* 活动项 - 增强版本 */
.activity-item {
  display: flex;
  align-items: center;
  gap: spacing(lg);
  padding: spacing(lg);
  border-radius: radius(lg);
  background: rgba(248, 250, 252, 0.5);
  border: 1px solid rgba(226, 232, 240, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  /* 悬浮效果 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.05), transparent);
    transition: left 0.5s ease;
  }

  &:hover {
    transform: translateX(4px);
    background: rgba(255, 255, 255, 0.8);
    border-color: rgba(102, 126, 234, 0.2);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);

    &::before {
      left: 100%;
    }

    .activity-avatar {
      transform: scale(1.1);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    }
  }
}

/* 活动头像 - 增强版本 */
.activity-avatar {
  width: 40px;
  height: 40px;
  border-radius: radius(md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

  /* 内部高光 */
  &::after {
    content: '';
    position: absolute;
    top: 15%;
    left: 15%;
    width: 70%;
    height: 70%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%);
    border-radius: inherit;
    pointer-events: none;
  }
}

/* 活动内容 */
.activity-content {
  flex: 1;
  min-width: 0;
}

/* 活动文本 */
.activity-text {
  display: flex;
  align-items: center;
  gap: spacing(sm);
  margin-bottom: spacing(xs);

  a {
    color: color(primary);
    text-decoration: none;
    font-weight: map-get($font-weight, medium);
    transition: color 0.2s ease;

    &:hover {
      color: $primary-color;
    }
  }
}

/* 活动时间 */
.activity-time {
  font-size: map-get($font-size, xs);
  color: color(tertiary);
  font-weight: map-get($font-weight, medium);
}

/* 快速操作区域 */
.quick-actions {
  display: flex;
  flex-direction: column;
  gap: spacing(md);
}

/* 快速操作卡片 - 重新设计 */
.quick-action-card {
  display: flex;
  align-items: center;
  gap: spacing(lg);
  padding: spacing(lg);
  background: rgba(248, 250, 252, 0.6);
  border: 1px solid rgba(226, 232, 240, 0.4);
  border-radius: radius(lg);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  /* 渐变边框效果 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: inherit;
  }

  &:hover {
    transform: translateY(-2px) scale(1.02);
    background: rgba(255, 255, 255, 0.9);
    border-color: rgba(102, 126, 234, 0.3);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);

    &::before {
      opacity: 1;
    }

    .quick-action-icon {
      transform: scale(1.2) rotate(5deg);
      background: $primary-gradient;
      color: white;
    }

    .quick-action-title {
      color: $primary-color;
    }
  }

  &:active {
    transform: translateY(-1px) scale(1.01);
  }
}

/* 快速操作图标 */
.quick-action-icon {
  width: 44px;
  height: 44px;
  border-radius: radius(md);
  background: rgba(102, 126, 234, 0.1);
  color: $primary-color;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 2;
}

/* 快速操作标题 */
.quick-action-title {
  font-size: map-get($font-size, base);
  font-weight: map-get($font-weight, semibold);
  color: color(primary);
  margin-bottom: spacing(xs);
  transition: color 0.3s ease;
  position: relative;
  z-index: 2;
}

/* 快速操作描述 */
.quick-action-desc {
  font-size: map-get($font-size, xs);
  color: color(tertiary);
  font-weight: map-get($font-weight, medium);
  position: relative;
  z-index: 2;
}

/* 动画定义 */
@keyframes pageSlideIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes backgroundFloat {
  0%, 100% {
    transform: translate(0, 0) rotate(0deg);
  }
  33% {
    transform: translate(30px, -30px) rotate(120deg);
  }
  66% {
    transform: translate(-20px, 20px) rotate(240deg);
  }
}

/* 响应式优化 */
@include mobile {
  .stat-card {
    padding: spacing(xl);
  }

  .chart-card {
    padding: spacing(xl);
  }

  .activity-item {
    padding: spacing(md);
    gap: spacing(md);
  }

  .quick-action-card {
    padding: spacing(md);
    gap: spacing(md);
  }
}

/* 滚动条优化 */
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(102, 126, 234, 0.3) transparent;
}

*::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

*::-webkit-scrollbar-track {
  background: transparent;
}

*::-webkit-scrollbar-thumb {
  background: rgba(102, 126, 234, 0.3);
  border-radius: 3px;
  transition: background 0.3s ease;
}

*::-webkit-scrollbar-thumb:hover {
  background: rgba(102, 126, 234, 0.5);
}

/* 选择文本样式 */
::selection {
  background: rgba(102, 126, 234, 0.1);
  color: $primary-color;
}

::-moz-selection {
  background: rgba(102, 126, 234, 0.1);
  color: $primary-color;
}


.page-content {
  position: relative;
  z-index: 1;
  padding: spacing(3xl) spacing(2xl);
  max-width: 2400px; /* 与其他页面保持一致 */
  margin: 0 auto;
  animation: pageSlideIn 0.8s cubic-bezier(0.16, 1, 0.3, 1);
  
  @include mobile {
    padding: spacing(2xl) spacing(xl);
  }
}