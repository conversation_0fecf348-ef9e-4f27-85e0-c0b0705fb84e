/* Settings页面样式 - 与代码审查页完全一致的设计系统 */

/* CSS变量定义 - 与CodeReviewPage.scss完全一致 */
:root {
  /* 主色系 - 与全局变量统一 */
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --primary-color: #667eea;
  --primary-color-hover: #5a6fd8;
  --primary-light: rgba(102, 126, 234, 0.1);
  --primary-shadow: rgba(102, 126, 234, 0.3);

  /* 状态色系 */
  --success-color: #52c41a;
  --success-light: rgba(82, 196, 26, 0.1);
  --warning-color: #faad14;
  --warning-light: rgba(250, 173, 20, 0.1);
  --danger-color: #ff4d4f;
  --danger-light: rgba(255, 77, 79, 0.1);
  --info-color: #1890ff;
  --info-light: rgba(24, 144, 255, 0.1);

  /* 文字色系 */
  --text-primary: #1a1a1a;
  --text-secondary: #666666;
  --text-tertiary: #999999;
  --text-muted: #64748b;

  /* 背景色系 */
  --bg-primary: rgba(255, 255, 255, 0.95);
  --bg-secondary: rgba(248, 250, 252, 0.8);
  --bg-glass: rgba(255, 255, 255, 0.8);
  --bg-glass-hover: rgba(255, 255, 255, 0.9);
  --bg-card: rgba(255, 255, 255, 0.8);
  --bg-card-hover: rgba(255, 255, 255, 0.9);

  /* 边框色系 */
  --border-light: rgba(226, 232, 240, 0.4);
  --border-medium: rgba(226, 232, 240, 0.6);
  --border-strong: rgba(226, 232, 240, 0.8);

  /* 阴影系统 */
  --shadow-xs: 0 1px 4px rgba(0, 0, 0, 0.04);
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 20px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.08);
  --shadow-xl: 0 20px 60px rgba(0, 0, 0, 0.12);
  --shadow-primary: 0 8px 24px rgba(102, 126, 234, 0.3);

  /* 模糊效果 */
  --blur-light: blur(10px);
  --blur-normal: blur(20px);
  --blur-strong: blur(32px);

  /* 动画系统 - 统一的缓动函数 */
  --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-smooth: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);

  /* 间距系统 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 20px;
  --spacing-2xl: 24px;
  --spacing-3xl: 32px;
  --spacing-4xl: 48px;

  /* 圆角系统 */
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 20px;

  /* 字体系统 */
  --font-xs: 11px;
  --font-sm: 12px;
  --font-base: 14px;
  --font-md: 15px;
  --font-lg: 16px;
  --font-xl: 18px;
  --font-2xl: 20px;
  --font-3xl: 24px;

  /* 全局背景 */
  --global-background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

/* 页面容器 - 与代码审查页一致 */
.settings-page {
  min-height: 100vh;
  background: var(--global-background);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', sans-serif;
}

/* MTD组件样式覆盖 - 使用与代码审查页搜索框一致的样式 */
.mtd-input,
input[class*="mtd"],
[class*="mtd-input"],
.mtd-input-wrapper input,
.mtd-input-affix-wrapper input,
.mtd-input-content input,
.enhanced-input {
  all: unset !important;
  display: block !important;
  width: 100% !important;
  height: 48px !important;
  padding: 0 16px !important;
  border: 2px solid var(--border-light) !important;
  border-radius: 16px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  background: var(--bg-primary) !important;
  color: var(--text-primary) !important;
  line-height: 1.5 !important;
  transition: var(--transition-fast) !important;
  outline: none !important;
  box-shadow: none !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif !important;
  box-sizing: border-box !important;
  vertical-align: top !important;
}

/* 输入框焦点状态 - 与代码审查页搜索框一致 */
.mtd-input:focus,
input[class*="mtd"]:focus,
[class*="mtd-input"]:focus,
.mtd-input-wrapper input:focus,
.mtd-input-affix-wrapper input:focus,
.mtd-input-content input:focus,
.enhanced-input:focus {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 4px var(--primary-light) !important;
  background: var(--bg-primary) !important;
  outline: none !important;
}

/* 输入框悬停状态 */
.mtd-input:hover,
input[class*="mtd"]:hover,
[class*="mtd-input"]:hover,
.mtd-input-wrapper input:hover,
.mtd-input-affix-wrapper input:hover,
.mtd-input-content input:hover,
.enhanced-input:hover {
  border-color: var(--primary-color) !important;
}

/* Placeholder样式 - 确保完美对齐 */
.mtd-input::placeholder,
input[class*="mtd"]::placeholder,
[class*="mtd-input"]::placeholder,
.mtd-input-wrapper input::placeholder,
.mtd-input-affix-wrapper input::placeholder,
.mtd-input-content input::placeholder,
.enhanced-input::placeholder {
  color: var(--text-muted) !important;
  font-weight: 400 !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  opacity: 1 !important;
  vertical-align: top !important;
  transform: translateY(0) !important;
}

/* 重置MTD组件容器样式 */
.mtd-input-wrapper,
.mtd-input-affix-wrapper,
.mtd-input-content,
[class*="mtd-input-wrapper"],
[class*="mtd-input-affix"],
[class*="mtd-input-content"] {
  all: unset !important;
  display: block !important;
  width: 100% !important;
  background: transparent !important;
}

/* 覆盖MTD Select组件 */
.mtd-select,
.mtd-select-selection,
[class*="mtd-select"] {
  all: unset !important;
  display: block !important;
  width: 100% !important;
  height: 48px !important;
  padding: 0 16px !important;
  border: 2px solid rgba(226, 232, 240, 0.4) !important;
  border-radius: 12px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  background: rgba(255, 255, 255, 0.95) !important;
  color: #1e293b !important;
  line-height: 44px !important;
  transition: all 0.2s ease !important;
  outline: none !important;
  box-shadow: none !important;
  font-family: inherit !important;
  cursor: pointer !important;
}

.mtd-select:hover,
.mtd-select-selection:hover,
[class*="mtd-select"]:hover {
  border-color: #667eea !important;
}

.mtd-select:focus,
.mtd-select-selection:focus,
[class*="mtd-select"]:focus,
.mtd-select-selection.mtd-select-selection-focused {
  border-color: #667eea !important;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1) !important;
  background: rgba(255, 255, 255, 1) !important;
  outline: none !important;
}

/* enhanced-input类的特定样式 - 确保与MTD覆盖一致 */
.enhanced-input,
input.enhanced-input,
.mtd-input.enhanced-input,
.mtd-input-wrapper .enhanced-input {
  all: unset !important;
  display: block !important;
  width: 100% !important;
  height: 48px !important;
  padding: 0 16px !important;
  border: 2px solid rgba(226, 232, 240, 0.4) !important;
  border-radius: 16px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  background: rgba(255, 255, 255, 0.95) !important;
  color: #1e293b !important;
  line-height: 44px !important;
  transition: all 0.2s ease !important;
  outline: none !important;
  box-shadow: none !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif !important;
  box-sizing: border-box !important;
}

.enhanced-input:focus,
input.enhanced-input:focus,
.mtd-input.enhanced-input:focus,
.mtd-input-wrapper .enhanced-input:focus {
  border-color: #667eea !important;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1) !important;
  background: rgba(255, 255, 255, 1) !important;
  outline: none !important;
}

.enhanced-input:hover,
input.enhanced-input:hover,
.mtd-input.enhanced-input:hover,
.mtd-input-wrapper .enhanced-input:hover {
  border-color: #667eea !important;
}

.enhanced-input::placeholder,
input.enhanced-input::placeholder,
.mtd-input.enhanced-input::placeholder,
.mtd-input-wrapper .enhanced-input::placeholder {
  color: #64748b !important;
  font-weight: 400 !important;
  line-height: 44px !important;
  opacity: 1 !important;
}

/* 覆盖全局焦点样式 */
input:focus-visible,
textarea:focus-visible,
select:focus-visible,
.enhanced-input:focus-visible {
  outline: none !important;
  outline-offset: 0 !important;
}

/* 页面头部 - 与代码审查页完全一致 */
.settings-header {
  background: var(--bg-glass);
  backdrop-filter: var(--blur-normal);
  border-bottom: 1px solid var(--border-light);
  padding: 24px 32px;
  position: sticky;
  top: 64px;
  z-index: 100;
  box-shadow: var(--shadow-sm);
}

/* 头部内容 - 与代码审查页一致 */
.settings-header-content {
  max-width: 2400px; /* 与代码审查页统一最大宽度 */
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  animation: slideInDown 0.8s cubic-bezier(0.16, 1, 0.3, 1);
  gap: 20px;
}

.header-title h1 {
  margin: 0 0 4px 0;
  font-size: 28px;
  font-weight: 800;
  color: var(--text-primary);
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.5px;
  transition: var(--transition-normal);
}

.header-title h1:hover {
  transform: scale(1.02);
}

.header-title p {
  margin: 0;
  font-size: 14px;
  color: var(--text-muted);
  font-weight: 500;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}
/* 页面内容区域 - 与代码审查页一致 */
.settings-content {
  position: relative;
  z-index: 1;
  max-width: 2400px; /* 与代码审查页统一最大宽度 */
  margin: 0 auto;
  padding: var(--spacing-3xl);
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: var(--spacing-3xl);
  min-height: calc(100vh - 200px);
}

/* 设置区块样式 - 与代码审查页任务卡片样式一致 */
.settings-section {
  background: var(--bg-glass);
  backdrop-filter: var(--blur-normal);
  border-radius: 20px;
  border: 2px solid var(--border-light);
  padding: var(--spacing-3xl);
  margin-bottom: 40px; /* 增加区块间距 */
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
  transition: var(--transition-smooth);
}

.settings-section:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
}

/* 设置区块顶部装饰条 - 与代码审查页状态指示器一致 */
.settings-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-gradient);
  border-radius: 20px 20px 0 0;
}

/* 区块标题 - 与代码审查页一致 */
.section-title {
  margin: 0 0 var(--spacing-2xl) 0;
  font-size: var(--font-2xl);
  font-weight: 800;
  color: var(--text-primary);
  position: relative;
  z-index: 2;
}

.section-description {
  margin: 0 0 var(--spacing-3xl) 0;
  font-size: var(--font-base);
  color: var(--text-muted);
  line-height: 1.5;
  position: relative;
  z-index: 2;
}

/* 表单样式 - 与代码审查页筛选区域样式一致 */
.form-group {
  margin-bottom: 28px; /* 优化表单组间距 */
  position: relative;
  z-index: 2;
}

.form-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
  font-size: var(--font-base);
  font-weight: 600;
  color: var(--text-primary);
}

.label-icon {
  font-size: var(--font-lg);
  color: var(--primary-color);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-2xl);
  margin-bottom: var(--spacing-2xl);
}

.form-description {
  margin-top: var(--spacing-sm);
  font-size: var(--font-sm);
  color: var(--text-muted);
  line-height: 1.4;
}

/* 按钮样式 - 与代码审查页一致 */
.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--radius-md);
  font-size: var(--font-base);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-smooth);
  border: none;
  outline: none;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.btn-primary {
  background: var(--primary-gradient);
  color: white;
  box-shadow: var(--shadow-primary);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

.btn-secondary {
  background: var(--bg-glass);
  color: var(--text-secondary);
  border: 2px solid var(--border-light);
}

.btn-secondary:hover {
  background: var(--bg-glass-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

/* 设置导航 - 与代码审查页筛选区域样式一致 */
.settings-nav {
  background: var(--bg-glass);
  backdrop-filter: var(--blur-normal);
  border-radius: 20px;
  border: 2px solid var(--border-light);
  padding: var(--spacing-2xl);
  position: sticky;
  top: calc(64px + var(--spacing-2xl));
  height: fit-content;
  box-shadow: var(--shadow-sm);
  transition: var(--transition-smooth);
}

.settings-nav:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
}

/* 导航项样式 - 与代码审查页任务卡片样式一致 */
.settings-nav-item {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  border: 2px solid var(--border-light);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  cursor: pointer;
  transition: var(--transition-smooth);
  position: relative;
  overflow: hidden;
}

.settings-nav-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--primary-light);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.settings-nav-item:hover::before {
  opacity: 1;
}

.settings-nav-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.settings-nav-item.active {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4px var(--primary-light), var(--shadow-sm);
}

.nav-item-title {
  font-size: var(--font-md);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  position: relative;
  z-index: 2;
}

.nav-item-description {
  font-size: var(--font-sm);
  color: var(--text-muted);
  line-height: 1.4;
  position: relative;
  z-index: 2;
}

/* 响应式设计 - 与代码审查页一致 */
@media (max-width: 1024px) {
  .settings-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
    padding: var(--spacing-2xl);
  }

  .settings-nav {
    position: static;
    margin-bottom: var(--spacing-2xl);
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }
}

@media (max-width: 768px) {
  .settings-header {
    top: 56px;
    padding: var(--spacing-xl) var(--spacing-2xl);
  }

  .settings-content {
    padding: var(--spacing-xl);
    gap: var(--spacing-xl);
  }

  .settings-section {
    padding: var(--spacing-2xl);
    border-radius: var(--radius-lg);
  }

  .header-title h1 {
    font-size: var(--font-3xl);
  }

  .settings-header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-lg);
  }
}

/* 动画定义 - 与代码审查页一致 */
@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 更改指示器 - 与代码审查页一致 */
.changes-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-base);
  color: var(--warning-color);
  font-weight: 500;
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--warning-light);
  border-radius: var(--radius-sm);
  border: 1px solid rgba(250, 173, 20, 0.2);
}

.changes-dot {
  width: 8px;
  height: 8px;
  background: var(--warning-color);
  border-radius: 50%;
}

/* 加载动画 */
.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 动画类 */
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-fadeIn {
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 覆盖全局焦点样式 */
input:focus-visible,
textarea:focus-visible,
select:focus-visible,
.enhanced-input:focus-visible {
  outline: none !important;
  outline-offset: 0 !important;
}

/* 额外的输入框对齐优化 */
.form-group input,
.form-group .mtd-input,
.form-group .enhanced-input {
  display: flex !important;
  align-items: center !important;
  justify-content: flex-start !important;
}

/* 确保所有输入框文本垂直居中 */
input[type="text"],
input[type="url"],
input[type="email"],
input[type="password"],
.mtd-input,
.enhanced-input {
  display: flex !important;
  align-items: center !important;
  text-align: left !important;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  line-height: 48px !important;
}

/* 优化placeholder的垂直对齐 */
input::placeholder,
.mtd-input::placeholder,
.enhanced-input::placeholder {
  line-height: 48px !important;
  vertical-align: middle !important;
  display: inline-block !important;
}
