/* 头部内容 */
.settings-header-content {
  max-width: 1600px; /* 统一最大宽度 */
  margin: 0 auto;
  animation: slideInDown 0.8s cubic-bezier(0.16, 1, 0.3, 1);
  
  h1 {
    margin: 0 0 spacing(sm) 0;
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, #10b981 0%, #047857 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    letter-spacing: -1px;
    transition: all 0.3s ease;
    
    &:hover {
      transform: scale(1.02);
    }
    
    @include mobile {
      font-size: 2rem;
    }
  }
  
  p {
    margin: 0;
    color: color(tertiary);
    font-size: map-get($font-size, lg);
    font-weight: map-get($font-weight, medium);
    opacity: 0;
    animation: fadeIn 0.8s cubic-bezier(0.16, 1, 0.3, 1) 0.3s forwards;
  }
}
/* 页面内容区域 */
.settings-content {
  position: relative;
  z-index: 1;
  max-width: 1600px; /* 统一最大宽度 */
  margin: 0 auto;
  padding: spacing(3xl) spacing(3xl) spacing(4xl);
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: spacing(4xl);

  @include tablet {
    grid-template-columns: 1fr;
    gap: spacing(3xl);
    padding: spacing(2xl) spacing(xl);
  }

  @include mobile {
    padding: spacing(xl);
  }
}
