// 使用 Stone 统一依赖管理 - 基于参考项目的精确复制
import React, { useState, useRef, useEffect } from '@rome/stone/react'
import { observer } from '@rome/stone/mobx-react'
import CodeReviewAgentInfoCard from '../components/agents/CodeReviewAgentInfoCard'
import CodeReviewResultView from '../components/results/CodeReviewResultView'
// React Icons 导入
import {
  HiOutlineDocument,
  HiOutlineDocumentText,
  HiOutlinePaintBrush,
  HiOutlineQrCode,
  HiOutlineLink,
  HiOutlineUser,
  HiOutlineCpuChip,
  HiOutlinePaperClip,
  HiOutlinePlay,
  HiOutlineEllipsisHorizontal,
  HiOutlineSparkles,
  HiOutlineRocketLaunch,
  HiOutlineSquares2X2,
  HiOutlineXMark,
  HiOutlineChevronRight,
  HiOutlineMagnifyingGlass
} from 'react-icons/hi2'
import './AIAssistant.scss'

interface ChatMessage {
  id: string
  type: 'user' | 'ai' | 'agent' | 'agent-info'
  content: string
  timestamp: Date
  files?: string[]
  agentType?: string
  agentResult?: any
  pendingAgent?: string // 用于agent-info类型消息
}

const AIAssistant: React.FC = observer(() => {
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      type: 'ai',
      content: '你好！我是闪购 AI 工作助手，可以帮助你完成从需求分析到代码审查的完整开发流程。请告诉我你想要做什么？\n\n💡 你可以说：\n• "我要分析需求" - 启动需求分析Agent\n• "帮我审查代码" - 启动代码审查Agent\n• "生成PRD文档" - 启动PRD生成Agent\n• 或者直接描述你的具体需求',
      timestamp: new Date(),
    }
  ])

  const [inputMessage, setInputMessage] = useState('')
  const [isTyping, setIsTyping] = useState(false)
  const [selectedFile, setSelectedFile] = useState<string | null>(null)
  const [showSplitView, setShowSplitView] = useState(false)
  const [reviewData, setReviewData] = useState<any>(null)
  const [isReviewing, setIsReviewing] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [chatMessages])

  // 意图识别函数
  const detectIntent = (message: string): string | null => {
    const lowerMessage = message.toLowerCase()

    // 代码审查相关关键词
    const codeReviewKeywords = [
      '代码审查', '审查代码', 'code review', 'cr', '代码检查',
      '代码质量', '代码分析', '检查代码', '审核代码'
    ]

    if (codeReviewKeywords.some(keyword => lowerMessage.includes(keyword))) {
      return 'code-review'
    }

    return null
  }

  // 发送消息处理
  const handleSendMessage = async () => {
    if (!inputMessage.trim()) return

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: inputMessage,
      timestamp: new Date(),
      files: selectedFile ? [selectedFile] : undefined
    }

    setChatMessages(prev => [...prev, userMessage])
    setInputMessage('')
    setSelectedFile(null)
    setIsTyping(true)

    // 检测意图
    const intent = detectIntent(inputMessage)

    setTimeout(() => {
      setIsTyping(false)

      if (intent === 'code-review') {
        // 添加AI回复消息
        const aiResponse: ChatMessage = {
          id: (Date.now() + 1).toString(),
          type: 'ai',
          content: '我来帮你进行代码审查！请提供以下信息以便我更好地分析你的代码：',
          timestamp: new Date(),
        }

        // 添加信息补充卡片消息
        const infoCardMessage: ChatMessage = {
          id: (Date.now() + 2).toString(),
          type: 'agent-info',
          content: '',
          timestamp: new Date(),
          pendingAgent: 'code-review'
        }

        setChatMessages(prev => [...prev, aiResponse, infoCardMessage])
      } else {
        // 普通AI回复
        const aiResponse: ChatMessage = {
          id: (Date.now() + 1).toString(),
          type: 'ai',
          content: '我理解了你的需求。让我来帮你分析一下...\n\n你可以尝试说：\n• "帮我审查代码" - 我会启动代码审查流程\n• "我要分析需求" - 我会帮你梳理需求\n• 或者告诉我更具体的需求',
          timestamp: new Date(),
        }
        setChatMessages(prev => [...prev, aiResponse])
      }
    }, 1000 + Math.random() * 1000)
  }

  // 快速操作处理 - 保持原有的聊天触发逻辑
  const handleQuickAction = (actionLabel: string) => {
    setInputMessage(`${actionLabel}`)
    setTimeout(() => {
      handleSendMessage()
    }, 100)
  }

  // 处理代码审查信息卡片完成
  const handleCodeReviewInfoComplete = async (data: any) => {
    // 先不移除信息补充卡片，等API调用成功后再移除

    // 添加用户确认消息
    const userConfirmMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: `已提交代码审查信息：\n• PR编号: #${data.pr_id}\n• 项目: ${data.project}\n• 仓库: ${data.repo}\n• 审查模式: ${data.crMode === 'fast' ? '快速审查' : data.crMode === 'standard' ? '标准审查' : '深度审查'}\n• 分支对比: ${data.fromBranch} → ${data.toBranch}`,
      timestamp: new Date(),
    }

    // 添加AI开始审查消息
    const aiStartMessage: ChatMessage = {
      id: (Date.now() + 1).toString(),
      type: 'ai',
      content: '收到！我正在启动代码审查Agent，开始分析你的代码...',
      timestamp: new Date(),
    }

    setChatMessages(prev => [...prev, userConfirmMessage, aiStartMessage])

    // 显示分栏视图并开始审查
    setShowSplitView(true)
    setIsReviewing(true)
    setReviewData(null)

    try {
      // 调用真实API
      const result = await callCodeReviewAPI(data)

      // 开发模式：如果需要使用模拟数据进行测试，可以注释掉上面的API调用，启用下面的模拟数据
      // const result = getMockReviewData(data)

      // API调用成功，移除信息补充卡片
      setChatMessages(prev => prev.filter(msg => msg.type !== 'agent-info' || msg.pendingAgent !== 'code-review'))

      setReviewData(result)
      setIsReviewing(false)

      // 添加完成消息
      const aiCompleteMessage: ChatMessage = {
        id: (Date.now() + 2).toString(),
        type: 'agent',
        content: '代码审查完成！请查看右侧详细报告。',
        timestamp: new Date(),
        agentType: 'code-review',
        agentResult: result
      }
      setChatMessages(prev => [...prev, aiCompleteMessage])
    } catch (error) {
      // API调用失败，保留信息补充卡片，关闭分栏视图
      setIsReviewing(false)
      setShowSplitView(false)
      setReviewData(null)

      const aiErrorMessage: ChatMessage = {
        id: (Date.now() + 2).toString(),
        type: 'ai',
        content: '抱歉，代码审查过程中出现了问题。请检查填写的信息是否正确，然后重新提交。你之前填写的信息已保留。',
        timestamp: new Date(),
      }
      setChatMessages(prev => [...prev, aiErrorMessage])
    }
  }

  // 处理代码审查信息卡片关闭
  const handleCodeReviewInfoClose = () => {
    // 移除信息补充卡片消息
    setChatMessages(prev => prev.filter(msg => msg.type !== 'agent-info' || msg.pendingAgent !== 'code-review'))

    // 添加取消消息
    const aiCancelMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'ai',
      content: '好的，已取消代码审查。如果你需要其他帮助，请随时告诉我！',
      timestamp: new Date(),
    }
    setChatMessages(prev => [...prev, aiCancelMessage])
  }

  // 处理审查完成
  // @ts-ignore
  const handleReviewComplete = () => {
    const aiCompleteMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'ai',
      content: '代码审查已完成！如果你需要重新审查或有其他需求，请随时告诉我。',
      timestamp: new Date(),
    }
    setChatMessages(prev => [...prev, aiCompleteMessage])
    setShowSplitView(false)
    setReviewData(null)
  }

  // 关闭分栏视图
  // @ts-ignore
  const handleCloseSplitView = () => {
    setShowSplitView(false)
    setReviewData(null)
    setIsReviewing(false)
  }

  // 调用代码审查API
  const callCodeReviewAPI = async (reviewParams: any) => {
    try {
      const response = await fetch('http://127.0.0.1:9000/yunzhuan/api/v1/main/cr_lc', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          project: reviewParams.project,
          repo: reviewParams.repo,
          fromBranch: reviewParams.fromBranch,
          toBranch: reviewParams.toBranch,
          pr_id: reviewParams.pr_id,
          crMode: reviewParams.crMode,
          spaceId: reviewParams.project // 使用项目名作为spaceId
        })
      })

      if (!response.ok) {
        throw new Error(`API请求失败: HTTP ${response.status}`)
      }

      const data = await response.json()
      return data
    } catch (error) {
      console.error('代码审查API调用失败:', error)

      // 检查是否是网络错误或服务器不可达
      if (error instanceof TypeError && error.message.includes('fetch')) {
        throw new Error('无法连接到代码审查服务，请检查网络连接或联系管理员')
      }

      // 检查是否是HTTP错误
      if (error instanceof Error && error.message.includes('HTTP')) {
        throw new Error(`代码审查服务返回错误: ${error.message}`)
      }

      // 其他错误
      throw new Error('代码审查过程中发生未知错误，请稍后重试')
    }
  }

  // 获取模拟数据（用于开发测试）
  // const getMockReviewData = (reviewParams: any) => {
  //   return {
  //     summary: {
  //       checkBranch: `${reviewParams.project}:${reviewParams.fromBranch}`,
  //       reviewTime: new Date().toLocaleTimeString(),
  //       reviewer: "AI代码审查系统",
  //       overallResult: "通过",
  //       resultDescription: "P0:0个,P1:2个,P2:1个",
  //       totalProblems: 3
  //     },
  //     scoring: {
  //       overallScore: 85,
  //       maxScore: 100,
  //       qualityGrade: "B",
  //       isPassed: true
  //     },
  //     problems: [
  //       {
  //         level: "P1",
  //         problem: "函数复杂度过高，建议拆分",
  //         suggestion: "将复杂函数拆分为多个小函数，提高代码可读性",
  //         targetCode: "function complexFunction() { /* 复杂逻辑 */ }",
  //         codePosition: [15, 0, 25, 0]
  //       },
  //       {
  //         level: "P1",
  //         problem: "缺少错误处理",
  //         suggestion: "添加try-catch块处理可能的异常",
  //         targetCode: "const data = JSON.parse(response);",
  //         codePosition: [31, 0, 31, 35]
  //       },
  //       {
  //         level: "P2",
  //         problem: "变量命名不够清晰",
  //         suggestion: "使用更具描述性的变量名",
  //         targetCode: "let x = getData();",
  //         codePosition: [8, 0, 8, 16]
  //       }
  //     ],
  //     statistics: {
  //       totalProblems: 3,
  //       criticalCount: 0,
  //       warningCount: 2,
  //       moderateCount: 1,
  //       minorCount: 0
  //     },
  //     taskDetails: [
  //       {
  //         taskName: "安全风险检查",
  //         taskType: "security_check",
  //         executionStatus: "completed",
  //         problemsFound: 0,
  //         taskSummary: "安全检查完成，未发现安全风险"
  //       },
  //       {
  //         taskName: "代码质量检查",
  //         taskType: "quality_check",
  //         executionStatus: "completed",
  //         problemsFound: 2,
  //         taskSummary: "代码质量检查完成，发现2个问题"
  //       },
  //       {
  //         taskName: "性能优化检查",
  //         taskType: "performance_check",
  //         executionStatus: "completed",
  //         problemsFound: 1,
  //         taskSummary: "性能检查完成，发现1个优化建议"
  //       },
  //       {
  //         taskName: "可维护性检查",
  //         taskType: "maintainability_check",
  //         executionStatus: "completed",
  //         problemsFound: 0,
  //         taskSummary: "可维护性检查完成，代码结构良好"
  //       }
  //     ]
  //   }
  // }

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setSelectedFile(file.name)
    }
  }

  const quickActions = [
    { key: 'requirements', icon: React.createElement(HiOutlineDocument as any), label: '需求分析', color: '#1890ff' },
    { key: 'prd', icon: React.createElement(HiOutlineDocumentText as any), label: 'PRD生成', color: '#52c41a' },
    { key: 'ui-prototype', icon: React.createElement(HiOutlinePaintBrush as any), label: 'UI原型', color: '#722ed1' },
    { key: 'tech-doc', icon: React.createElement(HiOutlineQrCode as any), label: '技术文档', color: '#fa8c16' },
    { key: 'api-doc', icon: React.createElement(HiOutlineLink as any), label: '接口文档', color: '#13c2c2' },
    { key: 'code-review', icon: React.createElement(HiOutlineMagnifyingGlass as any), label: '代码审查', color: '#eb2f96' },
  ]

  const workflowTemplates = [
    { key: 'web-app', label: 'Web应用开发', desc: '完整的前后端开发流程' },
    { key: 'mobile-app', label: '移动应用开发', desc: 'iOS/Android应用开发' },
    { key: 'api-service', label: 'API服务开发', desc: '后端API服务设计与开发' },
    { key: 'ui-component', label: 'UI组件库', desc: '组件库设计与开发' },
  ]

  return (
    <div className="ai-assistant-page" style={{ maxWidth: '2400px' , maxHeight: '90%' }}>
      <div className="ai-assistant-page__content"  style={{ maxWidth: '2400px', margin: '0 auto', width: '1600px', maxHeight: '95%'}}>
        {/* 聊天区域 */}
        <div className="ai-assistant-page__chat">
          {/* 聊天头部 */}
          <div className="chat-header">
            <div className="chat-header__content">
              <div className="chat-header__info">
                <h2 className="chat-header__title">
                  {React.createElement(HiOutlineCpuChip as any, { style: { marginRight: '8px' } })}
                  AI 工作助手
                </h2>
                <div className="chat-header__subtitle">
                  智能 · 高效 · 全流程
                </div>
              </div>
              <div className="chat-header__actions">
                <button
                  style={{
                    background: 'transparent',
                    border: 'none',
                    cursor: 'pointer',
                    padding: '8px',
                    borderRadius: '6px',
                    color: '#666',
                    fontSize: '16px'
                  }}
                >
                  {React.createElement(HiOutlineEllipsisHorizontal as any)}
                </button>
              </div>
            </div>
          </div>

          {/* 消息容器 */}
          <div className="messages-container">
            {chatMessages.map((message) => (
              <div key={message.id} className={`message ${message.type === 'user' ? 'message--user' : ''}`}>
                <div className={`message__avatar message__avatar--${message.type === 'user' ? 'user' : 'assistant'}`}>
                  {message.type === 'user' ? React.createElement(HiOutlineUser as any) : React.createElement(HiOutlineCpuChip as any)}
                </div>

                <div className="message__content">
                  {message.type === 'agent-info' && message.pendingAgent === 'code-review' ? (
                    // 渲染代码审查信息补充卡片
                    <div style={{ marginTop: '8px' }}>
                      <CodeReviewAgentInfoCard
                        onComplete={handleCodeReviewInfoComplete}
                        onClose={handleCodeReviewInfoClose}
                      />
                    </div>
                  ) : (
                    <>
                      <div className={`message__bubble message__bubble--${message.type === 'user' ? 'user' : 'assistant'}`}>
                        {message.type === 'agent' && (
                          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
                            {React.createElement(HiOutlineSparkles as any, { style: { marginRight: '6px' } })}
                            <span style={{ color: '#fff', fontSize: '13px', fontWeight: 600 }}>
                              Agent任务完成
                            </span>
                          </div>
                        )}
                        {message.content}

                        {message.files && (
                          <div style={{ marginTop: 8, display: 'flex', flexWrap: 'wrap', gap: 4 }}>
                            {message.files.map(file => (
                              <span key={file} style={{
                                fontSize: '11px',
                                background: 'rgba(255,255,255,0.2)',
                                padding: '2px 6px',
                                borderRadius: '4px',
                                display: 'inline-flex',
                                alignItems: 'center',
                                gap: '4px'
                              }}>
                                {React.createElement(HiOutlineDocument as any, { style: { fontSize: '10px' } })}
                                {file}
                              </span>
                            ))}
                          </div>
                        )}
                      </div>

                      <div className="message__timestamp">
                        {message.timestamp.toLocaleTimeString()}
                      </div>
                    </>
                  )}
                </div>
              </div>
            ))}

            {isTyping && (
              <div className="message">
                <div className="message__avatar message__avatar--assistant">
                  {React.createElement(HiOutlineCpuChip as any)}
                </div>
                <div className="message__content">
                  <div className="loading__message">
                    <div className="loading__dots">
                      <div className="loading__dot"></div>
                      <div className="loading__dot"></div>
                      <div className="loading__dot"></div>
                    </div>
                    <span>AI正在思考...</span>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* 输入区域 */}
          <div className="chat-input">
            <div className="chat-input__container">
              <div className="chat-input__wrapper">
                <textarea
                  className="chat-input__field"
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  placeholder="描述你的需求，我来帮你规划完整的开发流程..."
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault()
                      handleSendMessage()
                    }
                  }}
                  rows={1}
                />
                {/* 输入框内部的操作按钮 */}
                <div className="chat-input__inner-actions">
                  <label className="chat-input__upload" title="上传文件">
                    {React.createElement(HiOutlinePaperClip as any)}
                    <input
                      type="file"
                      style={{ display: 'none' }}
                      onChange={handleFileUpload}
                    />
                  </label>
                  <button
                    className="chat-input__send"
                    onClick={handleSendMessage}
                    disabled={!inputMessage.trim()}
                  >
                    {React.createElement(HiOutlinePlay as any)}
                  </button>
                </div>
              </div>
            </div>

            {/* 已选择文件显示 */}
            {selectedFile && (
              <div style={{ marginTop: '8px' }}>
                <span style={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  gap: '4px',
                  background: '#f0f0f0',
                  padding: '4px 8px',
                  borderRadius: '8px',
                  fontSize: '12px'
                }}>
                  {React.createElement(HiOutlineDocument as any, { style: { fontSize: '12px' } })}
                  {selectedFile}
                  <button
                    onClick={() => setSelectedFile(null)}
                    style={{
                      background: 'none',
                      border: 'none',
                      cursor: 'pointer',
                      marginLeft: '4px',
                      color: '#999',
                      fontSize: '14px'
                    }}
                  >
                    {React.createElement(HiOutlineXMark as any)}
                  </button>
                </span>
              </div>
            )}
          </div>
        </div>

        {/* 右侧面板 */}
        {showSplitView ? (
          // 代码审查结果分栏
          <div style={{
            flex: '1 1 50%',
            minWidth: '40%',
            maxWidth: '60%',
            height: '100%',
            transition: 'all 0.3s ease'
          }}>
            <CodeReviewResultView
              reviewData={reviewData}
              isLoading={isReviewing}
              onComplete={() => {}}
              onClose={() => setShowSplitView(false)}
            />
          </div>
        ) : (
          // 侧边栏 - 参考第二张图的纳米AI搜索界面布局
          <div className="ai-assistant-page__sidebar" style={{width: '100%'}}>
            <div className="sidebar">
              {/* 侧边栏头部 */}
              <div className="sidebar__header">
                <h4 className="sidebar__title">
                  {React.createElement(HiOutlineRocketLaunch as any, { style: { marginRight: '8px' } })}
                  智能工具箱
                </h4>
                <p className="sidebar__subtitle">
                  选择工具开始你的开发流程
                </p>
              </div>

              {/* 侧边栏内容 */}
              <div className="sidebar__content">
                {/* 快速操作 - 参考纳米AI搜索的卡片布局 */}
                <div className="sidebar__section">
                  <h5 className="sidebar__section-title">
                    {React.createElement(HiOutlineSparkles as any, { style: { marginRight: '8px' } })}
                    快速开始
                  </h5>
                  <div className="quick-actions">
                    {quickActions.map(action => (
                      <div
                        key={action.key}
                        className="quick-actions__item"
                        onClick={() => handleQuickAction(action.label)}
                      >
                        <div className="quick-actions__icon" style={{ color: action.color }}>
                          {action.icon}
                        </div>
                        <div className="quick-actions__label">
                          {action.label}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 工作流模板 */}
                <div className="sidebar__section">
                  <h5 className="sidebar__section-title">
                    {React.createElement(HiOutlineSquares2X2 as any, { style: { marginRight: '8px' } })}
                    工作流模板
                  </h5>
                  <div className="workflow-templates">
                    {workflowTemplates.map(template => (
                      <div key={template.key} className="workflow-templates__item">
                        <div className="workflow-templates__content">
                          <div className="workflow-templates__icon">
                            {React.createElement(HiOutlineChevronRight as any)}
                          </div>
                          <div className="workflow-templates__info">
                            <div className="workflow-templates__title">
                              {template.label}
                            </div>
                            <div className="workflow-templates__desc">
                              {template.desc}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
})

export default AIAssistant
