// 使用 Stone 统一依赖管理 - 基于参考项目的精确复制
import React, { useState, useMemo } from '@rome/stone/react'
import { observer } from '@rome/stone/mobx-react'
// React Icons 导入
import {
  HiOutlineMagnifyingGlass,
  HiOutlineClock,
  HiOutlineCheckCircle,
  HiOutlineExclamationTriangle,
  HiOutlineBugAnt,
  HiOutlineXMark,
  HiOutlineSquares2X2,
  HiOutlineBars3,
  HiOutlineChartBarSquare
} from 'react-icons/hi2'
import './CodeReviewPage.scss'

interface CodeReviewTask {
  id: string
  prId: string
  title: string
  author: string
  status: 'pending' | 'reviewing' | 'completed'
  priority: 'high' | 'medium' | 'low'
  project: string
  repo: string
  fromBranch: string
  toBranch: string
  createdAt: string
  issuesCount?: number
  issues?: Array<{
    id: string
    type: 'security' | 'performance' | 'code-style' | 'logic'
    level: 'high' | 'medium' | 'low'
    message: string
    file: string
    line: number
  }>
}

const CodeReviewPage: React.FC = observer(() => {
  const [searchText, setSearchText] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [priorityFilter, setPriorityFilter] = useState<string>('all')
  const [selectedTask, setSelectedTask] = useState<CodeReviewTask | null>(null)
  const [showDetails, setShowDetails] = useState(false)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')

  // 模拟数据
  const mockCodeReviewTasks: CodeReviewTask[] = [
    {
      id: '1',
      prId: 'PR#123',
      title: 'feat: 添加用户认证模块',
      author: '张三',
      status: 'pending',
      priority: 'high',
      project: 'sg-frontend',
      repo: 'user-center',
      fromBranch: 'feature/auth',
      toBranch: 'develop',
      createdAt: '2小时前',
      issuesCount: 3,
      issues: [
        {
          id: '1',
          type: 'security',
          level: 'high',
          message: '密码未进行加密存储，存在安全风险',
          file: 'src/auth/login.ts',
          line: 45
        },
        {
          id: '2',
          type: 'performance',
          level: 'medium',
          message: '数据库查询未使用索引，可能影响性能',
          file: 'src/auth/user.service.ts',
          line: 23
        }
      ]
    },
    {
      id: '2',
      prId: 'PR#124',
      title: 'fix: 修复登录页面样式问题',
      author: '李四',
      status: 'completed',
      priority: 'medium',
      project: 'sg-frontend',
      repo: 'ui-components',
      fromBranch: 'fix/login-style',
      toBranch: 'main',
      createdAt: '4小时前',
      issuesCount: 1
    },
    {
      id: '3',
      prId: 'PR#125',
      title: 'refactor: 重构API接口层',
      author: '王五',
      status: 'reviewing',
      priority: 'high',
      project: 'sg-backend',
      repo: 'api-gateway',
      fromBranch: 'refactor/api',
      toBranch: 'develop',
      createdAt: '6小时前',
      issuesCount: 5
    },
    {
      id: '4',
      prId: 'PR#126',
      title: 'docs: 更新README文档',
      author: '赵六',
      status: 'completed',
      priority: 'low',
      project: 'sg-docs',
      repo: 'documentation',
      fromBranch: 'docs/readme',
      toBranch: 'main',
      createdAt: '1天前',
      issuesCount: 0
    },
    {
      id: '5',
      prId: 'PR#127',
      title: 'feat: 实现实时通知功能',
      author: '钱七',
      status: 'pending',
      priority: 'high',
      project: 'sg-backend',
      repo: 'notification-service',
      fromBranch: 'feature/realtime-notification',
      toBranch: 'develop',
      createdAt: '30分钟前',
      issuesCount: 2
    }
  ]

  // 过滤任务
  const filteredTasks = useMemo(() => {
    return mockCodeReviewTasks.filter(task => {
      const matchesSearch = task.title.toLowerCase().includes(searchText.toLowerCase()) ||
                           task.prId.toLowerCase().includes(searchText.toLowerCase()) ||
                           task.author.toLowerCase().includes(searchText.toLowerCase())
      const matchesStatus = statusFilter === 'all' || task.status === statusFilter
      const matchesPriority = priorityFilter === 'all' || task.priority === priorityFilter
      return matchesSearch && matchesStatus && matchesPriority
    })
  }, [searchText, statusFilter, priorityFilter])

  // 统计数据
  const statusStats = useMemo(() => {
    const total = mockCodeReviewTasks.length
    const completed = mockCodeReviewTasks.filter(task => task.status === 'completed').length
    const reviewing = mockCodeReviewTasks.filter(task => task.status === 'reviewing').length
    const pending = mockCodeReviewTasks.filter(task => task.status === 'pending').length
    const highPriority = mockCodeReviewTasks.filter(task => task.priority === 'high').length
    const totalIssues = mockCodeReviewTasks.reduce((sum, task) => sum + (task.issuesCount || 0), 0)
    return { total, completed, reviewing, pending, highPriority, totalIssues }
  }, [])

  // 获取状态标签
  const getStatusTag = (status: string) => {
    const statusMap = {
      pending: { color: '#f59e0b', text: '待审查', icon: React.createElement(HiOutlineClock as any) },
      reviewing: { color: '#3b82f6', text: '审查中', icon: React.createElement(HiOutlineMagnifyingGlass as any) },
      completed: { color: '#10b981', text: '已完成', icon: React.createElement(HiOutlineCheckCircle as any) }
    }
    const config = statusMap[status as keyof typeof statusMap]
    return (
      <span 
        style={{
          background: config?.color || '#6b7280',
          color: 'white',
          padding: '6px 12px',
          borderRadius: '12px',
          fontSize: '12px',
          fontWeight: 600,
          display: 'inline-flex',
          alignItems: 'center',
          gap: '4px'
        }}
      >
        <span>{config?.icon || React.createElement(HiOutlineExclamationTriangle as any)}</span>
        {config?.text || status}
      </span>
    )
  }

  // 获取优先级标签
  const getPriorityTag = (priority: string) => {
    const priorityMap = {
      high: { color: '#ef4444', text: '高', icon: React.createElement(HiOutlineExclamationTriangle as any) },
      medium: { color: '#f59e0b', text: '中', icon: React.createElement(HiOutlineExclamationTriangle as any) },
      low: { color: '#10b981', text: '低', icon: React.createElement(HiOutlineCheckCircle as any) }
    }
    const config = priorityMap[priority as keyof typeof priorityMap]
    return (
      <span 
        style={{
          display: 'inline-flex',
          alignItems: 'center',
          gap: '4px',
          padding: '4px 8px',
          borderRadius: '6px',
          fontSize: '11px',
          fontWeight: 600,
          color: 'white',
          background: config?.color || '#6b7280'
        }}
      >
        <span>{config?.icon || React.createElement(HiOutlineExclamationTriangle as any)}</span>
        {config?.text || priority}
      </span>
    )
  }

  // 处理查看详情
  const handleViewDetails = (task: CodeReviewTask) => {
    setSelectedTask(task)
    setShowDetails(true)
  }

  // 关闭详情面板
  const handleCloseDetails = () => {
    setShowDetails(false)
    setSelectedTask(null)
  }

  return (
    <div className="code-review-page">
      {/* 页面头部 */}
      <div className="page-header">
        <div className="header-content">
          <div className="header-title">
            <h1>代码审查</h1>
            <p>智能代码质量检测和优化建议</p>
          </div>

          <div className="header-actions">
            <div className="view-toggle">
              <button
                className={`toggle-btn ${viewMode === 'grid' ? 'active' : ''}`}
                onClick={() => setViewMode('grid')}
                title="网格视图"
              >
                <span>{React.createElement(HiOutlineSquares2X2 as any)}</span>
              </button>
              <button
                className={`toggle-btn ${viewMode === 'list' ? 'active' : ''}`}
                onClick={() => setViewMode('list')}
                title="列表视图"
              >
                <span>{React.createElement(HiOutlineBars3 as any)}</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="page-content" style={{
        padding: 0,
        overflow: 'hidden',
        position: 'relative'
      }}>
        {/* 主内容区域 */}
        <div style={{
          width: showDetails ? '60%' : '100%',
          height: '100%',
          overflow: 'auto',
          transition: 'width 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
          padding: '0 32px 32px',
          boxSizing: 'border-box'
        }}>
          {/* 统计卡片 */}
          <div className="enhanced-stats-grid">
            <div className="enhanced-stat-card primary">
              <div className="stat-icon">{React.createElement(HiOutlineChartBarSquare as any)}</div>
              <div className="stat-content">
                <div className="stat-value">{statusStats.total}</div>
                <div className="stat-label">总任务</div>
              </div>
              <div className="stat-trend">+{Math.floor(statusStats.total * 0.1)} 本周</div>
            </div>

            <div className="enhanced-stat-card success">
              <div className="stat-icon">{React.createElement(HiOutlineCheckCircle as any)}</div>
              <div className="stat-content">
                <div className="stat-value">{statusStats.completed}</div>
                <div className="stat-label">已完成</div>
              </div>
              <div className="stat-progress">
                <div className="progress-bar">
                  <div
                    className="progress-fill"
                    style={{ width: `${(statusStats.completed / statusStats.total) * 100}%` }}
                  />
                </div>
                <span>{Math.round((statusStats.completed / statusStats.total) * 100)}%</span>
              </div>
            </div>

            <div className="enhanced-stat-card warning">
              <div className="stat-icon">{React.createElement(HiOutlineMagnifyingGlass as any)}</div>
              <div className="stat-content">
                <div className="stat-value">{statusStats.reviewing}</div>
                <div className="stat-label">审查中</div>
              </div>
              <div className="stat-trend">进行中</div>
            </div>

            <div className="enhanced-stat-card danger">
              <div className="stat-icon">{React.createElement(HiOutlineExclamationTriangle as any)}</div>
              <div className="stat-content">
                <div className="stat-value">{statusStats.highPriority}</div>
                <div className="stat-label">高优先级</div>
              </div>
              <div className="stat-trend">需关注</div>
            </div>

            <div className="enhanced-stat-card info">
              <div className="stat-icon">{React.createElement(HiOutlineBugAnt as any)}</div>
              <div className="stat-content">
                <div className="stat-value">{statusStats.totalIssues}</div>
                <div className="stat-label">发现问题</div>
              </div>
              <div className="stat-trend">待修复</div>
            </div>
          </div>

          {/* 搜索和筛选 */}
          <div className="enhanced-filter-section">
            <div className="filter-group">
              <div className="search-container">
                <div className="search-icon">{React.createElement(HiOutlineMagnifyingGlass as any)}</div>
                <input
                  type="text"
                  placeholder="搜索PR标题、编号或作者..."
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  className="enhanced-search-input"
                />
                {searchText && (
                  <button
                    className="clear-search"
                    onClick={() => setSearchText('')}
                  >
                    {React.createElement(HiOutlineXMark as any)}
                  </button>
                )}
              </div>

              <div className="filter-controls">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="enhanced-filter-select"
                >
                  <option value="all">全部状态</option>
                  <option value="pending">待审查</option>
                  <option value="reviewing">审查中</option>
                  <option value="completed">已完成</option>
                </select>

                <select
                  value={priorityFilter}
                  onChange={(e) => setPriorityFilter(e.target.value)}
                  className="enhanced-filter-select"
                >
                  <option value="all">全部优先级</option>
                  <option value="high">高优先级</option>
                  <option value="medium">中优先级</option>
                  <option value="low">低优先级</option>
                </select>
              </div>
            </div>

            <div className="results-info">
              <span>找到 <strong>{filteredTasks.length}</strong> 个任务</span>
            </div>
          </div>

          {/* 任务列表 */}
          <div className={`tasks-container ${viewMode}`}>
            {filteredTasks.length > 0 ? (
              filteredTasks.map((task: any) => (
                <div
                  key={task.id}
                  className={`enhanced-task-card ${task.status} ${selectedTask?.id === task.id ? 'selected' : ''}`}
                  onClick={() => handleViewDetails(task)}
                >
                  {/* 状态指示器 */}
                  <div className="task-status-indicator" />

                  {/* 卡片头部 */}
                  <div className="task-card-header">
                    <div className="task-meta">
                      <span className="pr-id">{task.prId}</span>
                      <div className="task-badges">
                        {getPriorityTag(task.priority)}
                        {(task.issuesCount || 0) > 0 && (
                          <span className="issues-badge">
                            🐛 {task.issuesCount || 0}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* 任务内容 */}
                  <div className="task-card-content">
                    <h3 className="task-title">{task.title}</h3>

                    <div className="task-details">
                      <div className="detail-item">
                        <span className="detail-icon">📁</span>
                        <span className="detail-text">{task.project}/{task.repo}</span>
                      </div>
                  
                      <div className="detail-item">
                        <span className="detail-icon">👤</span>
                        <span className="detail-text">{task.author}</span>
                      </div>

                      <div className="detail-item">
                        <span className="detail-icon">🕒</span>
                        <span className="detail-text">{task.createdAt}</span>
                      </div>
                    </div>

                    <div className="branch-flow">
                      <code className="branch-name">{task.fromBranch}</code>
                      <span className="branch-arrow">→</span>
                      <code className="branch-name">{task.toBranch}</code>
                    </div>
                  </div>

                  {/* 卡片底部 */}
                  <div className="task-card-footer">
                    {getStatusTag(task.status)}
                    <button className="view-details-btn">
                      查看详情 →
                    </button>
                  </div>
                </div>
              ))
            ) : (
              <div className="empty-state">
                <div className="empty-icon">🔍</div>
                <h3>未找到匹配的任务</h3>
                <p>尝试调整搜索条件或筛选器</p>
                <button
                  className="reset-filters-btn"
                  onClick={() => {
                    setSearchText('')
                    setStatusFilter('all')
                    setPriorityFilter('all')
                  }}
                >
                  重置筛选条件
                </button>
              </div>
            )}
          </div>
        </div>

        {/* 右侧详情面板 */}
        {showDetails && selectedTask && (
          <div className="enhanced-details-panel">
            <div className="details-header">
              <div className="details-title">
                <h3>代码审查详情</h3>
                <div className="task-status-badge">
                  {getStatusTag(selectedTask.status)}
                </div>
              </div>
              <button onClick={handleCloseDetails} className="close-btn">
                ✕
              </button>
            </div>
            
            <div className="details-content">
              {/* 任务基本信息 */}
              <div className="task-overview">
                <div className="overview-header">
                  <h4>{selectedTask.prId}</h4>
                  {getPriorityTag(selectedTask.priority)}
                </div>
                <h2 className="task-main-title">{selectedTask.title}</h2>

                <div className="task-metadata">
                  <div className="metadata-item">
                    <span className="metadata-label">作者</span>
                    <span className="metadata-value">👤 {selectedTask.author}</span>
                  </div>
                  <div className="metadata-item">
                    <span className="metadata-label">项目</span>
                    <span className="metadata-value">📁 {selectedTask.project}/{selectedTask.repo}</span>
                  </div>
                  <div className="metadata-item">
                    <span className="metadata-label">分支</span>
                    <div className="branch-info-detailed">
                      <code>{selectedTask.fromBranch}</code>
                      <span>→</span>
                      <code>{selectedTask.toBranch}</code>
                    </div>
                  </div>
                  <div className="metadata-item">
                    <span className="metadata-label">创建时间</span>
                    <span className="metadata-value">🕒 {selectedTask.createdAt}</span>
                  </div>
                </div>
              </div>

              {/* 问题列表 */}
              {selectedTask.issues && selectedTask.issues.length > 0 && (
                <div className="issues-section">
                  <div className="section-header">
                    <h4>发现的问题</h4>
                    <span className="issues-count-badge">
                      {selectedTask.issues.length} 个问题
                    </span>
                  </div>

                  <div className="issues-list">
                    {selectedTask.issues.map((issue, index) => (
                      <div key={issue.id} className="enhanced-issue-item">
                        <div className="issue-header">
                          <div className="issue-badges">
                            <span className={`issue-level-badge ${issue.level}`}>
                              {issue.level.toUpperCase()}
                            </span>
                            <span className={`issue-type-badge ${issue.type}`}>
                              {issue.type.replace('-', ' ').toUpperCase()}
                            </span>
                          </div>
                          <span className="issue-number">#{index + 1}</span>
                        </div>

                        <div className="issue-content">
                          <p className="issue-message">{issue.message}</p>
                          <div className="issue-location">
                            <span className="file-icon">📄</span>
                            <code className="file-path">{issue.file}</code>
                            <span className="line-number">:{issue.line}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 操作按钮 */}
              <div className="details-actions">
                <button className="action-btn primary">
                  开始审查
                </button>
                <button className="action-btn secondary">
                  查看代码
                </button>
                <button className="action-btn secondary">
                  添加评论
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
})

export default CodeReviewPage
