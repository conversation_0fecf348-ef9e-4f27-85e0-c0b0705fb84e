import React, { useState, useEffect } from '@rome/stone/react'
import { Button, Switch, Select, Input } from '@ss/mtd-react'
import { observer } from '@rome/stone/mobx-react'
import './CodeReviewPage.scss'
import '../styles/animations.css'

interface Rule {
  id: string
  level: 'P0' | 'P1' | 'P2'
  title: string
  description: string
  examples: string[]
  keywords: string[]
  enabled: boolean
}

interface RuleConfig {
  language: string
  business: string
  version: string
  description: string
  custom_instructions: string
  output_format: string
  rules: Rule[]
}

const RuleConfigPage: React.FC = observer(() => {
  const [configs, setConfigs] = useState<RuleConfig[]>([])
  const [selectedConfig, setSelectedConfig] = useState<RuleConfig | null>(null)
  const [showRuleEditor, setShowRuleEditor] = useState(false)
  const [editingRule, setEditingRule] = useState<Rule | null>(null)
  const [loading, setLoading] = useState(true)
  const [searchText, setSearchText] = useState('')
  const [levelFilter, setLevelFilter] = useState<string>('all')

  // 表单状态
  const [ruleForm, setRuleForm] = useState({
    id: '',
    level: 'P1' as 'P0' | 'P1' | 'P2',
    title: '',
    description: '',
    examples: '',
    keywords: '',
    enabled: true
  })

  useEffect(() => {
    loadConfigsData()
  }, [])

  const loadConfigsData = async () => {
    setLoading(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const mockConfigs: RuleConfig[] = [
        {
          language: "python",
          business: "security",
          version: "1.0",
          description: "Python安全审查专用规范",
          custom_instructions: "重点关注安全漏洞和风险点，对所有用户输入进行安全性检查",
          output_format: "JSON结构化输出，重点标注安全风险等级",
          rules: [
            {
              id: "SEC-P0-1",
              level: "P0",
              title: "SQL注入漏洞",
              description: "存在SQL注入风险，使用字符串拼接构建SQL语句",
              examples: [
                "cursor.execute(f\"SELECT * FROM users WHERE id = {user_id}\")",
                "query = \"SELECT * FROM \" + table_name",
                "直接拼接用户输入到SQL语句"
              ],
              keywords: ["SQL注入", "字符串拼接", "execute", "query"],
              enabled: true
            },
            {
              id: "SEC-P0-2",
              level: "P0",
              title: "命令注入漏洞",
              description: "存在命令注入风险，直接执行用户输入的命令",
              examples: [
                "os.system(user_input)",
                "subprocess.call(shell_command)",
                "eval(user_data)"
              ],
              keywords: ["命令注入", "os.system", "subprocess", "eval", "exec"],
              enabled: true
            },
            {
              id: "SEC-P1-1",
              level: "P1",
              title: "敏感信息泄露",
              description: "代码中包含敏感信息，如密码、密钥等",
              examples: [
                "password = '123456'",
                "api_key = 'sk-1234567890abcdef'",
                "secret_token = 'abc123'"
              ],
              keywords: ["password", "secret", "key", "token"],
              enabled: true
            },
            {
              id: "QUA-P2-1",
              level: "P2",
              title: "代码重复检测",
              description: "检测重复的代码块，提高代码可维护性",
              examples: [
                "重复的函数实现",
                "相似的逻辑块",
                "重复的常量定义"
              ],
              keywords: ["重复", "duplicate", "similar"],
              enabled: false
            }
          ]
        },
        {
          language: "javascript",
          business: "quality",
          version: "1.2",
          description: "JavaScript代码质量检查规范",
          custom_instructions: "关注代码规范、性能优化和最佳实践",
          output_format: "详细的问题报告和修复建议",
          rules: [
            {
              id: "JS-P1-1",
              level: "P1",
              title: "未使用变量检测",
              description: "检测未使用的变量和导入",
              examples: [
                "const unusedVar = 'test'",
                "import { unused } from 'module'",
                "function test(unusedParam) {}"
              ],
              keywords: ["unused", "variable", "import"],
              enabled: true
            },
            {
              id: "JS-P2-1",
              level: "P2",
              title: "函数复杂度检查",
              description: "检查函数的圈复杂度",
              examples: [
                "过多的if-else嵌套",
                "复杂的循环结构",
                "多层条件判断"
              ],
              keywords: ["complexity", "nested", "if-else"],
              enabled: true
            }
          ]
        }
      ]
      
      setConfigs(mockConfigs)
      setSelectedConfig(mockConfigs[0])
    } catch (error) {
      console.error('Failed to load configs data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'P0': return '#ef4444'
      case 'P1': return '#f59e0b'
      case 'P2': return '#3b82f6'
      default: return '#6b7280'
    }
  }

  const getLevelText = (level: string) => {
    switch (level) {
      case 'P0': return 'P0 严重'
      case 'P1': return 'P1 重要'
      case 'P2': return 'P2 一般'
      default: return '未知'
    }
  }

  const handleToggleRule = (ruleId: string) => {
    if (!selectedConfig) return

    const updatedConfig = {
      ...selectedConfig,
      rules: selectedConfig.rules.map(rule =>
        rule.id === ruleId ? { ...rule, enabled: !rule.enabled } : rule
      )
    }

    setSelectedConfig(updatedConfig)
    setConfigs(prev => prev.map(config =>
      config.language === updatedConfig.language ? updatedConfig : config
    ))
  }

  const handleEditRule = (rule: Rule) => {
    setEditingRule(rule)
    setRuleForm({
      id: rule.id,
      level: rule.level,
      title: rule.title,
      description: rule.description,
      examples: rule.examples.join('\n'),
      keywords: rule.keywords.join(', '),
      enabled: rule.enabled
    })
    setShowRuleEditor(true)
  }

  const handleAddRule = () => {
    setEditingRule(null)
    setRuleForm({
      id: '',
      level: 'P1',
      title: '',
      description: '',
      examples: '',
      keywords: '',
      enabled: true
    })
    setShowRuleEditor(true)
  }

  const handleSaveRule = () => {
    if (!selectedConfig) return

    const newRule: Rule = {
      id: ruleForm.id,
      level: ruleForm.level,
      title: ruleForm.title,
      description: ruleForm.description,
      examples: ruleForm.examples.split('\n').filter(ex => ex.trim()),
      keywords: ruleForm.keywords.split(',').map(kw => kw.trim()).filter(kw => kw),
      enabled: ruleForm.enabled
    }

    const updatedConfig = { ...selectedConfig }

    if (editingRule) {
      const index = updatedConfig.rules.findIndex(r => r.id === editingRule.id)
      updatedConfig.rules[index] = newRule
    } else {
      updatedConfig.rules.push(newRule)
    }

    setSelectedConfig(updatedConfig)
    setConfigs(prev => prev.map(config =>
      config.language === updatedConfig.language ? updatedConfig : config
    ))

    setShowRuleEditor(false)
    alert('规则保存成功！')
  }

  const handleDeleteRule = (ruleId: string) => {
    if (!selectedConfig) return

    if (window.confirm('确定要删除这条规则吗？')) {
      const updatedConfig = {
        ...selectedConfig,
        rules: selectedConfig.rules.filter(r => r.id !== ruleId)
      }

      setSelectedConfig(updatedConfig)
      setConfigs(prev => prev.map(config =>
        config.language === updatedConfig.language ? updatedConfig : config
      ))

      alert('规则删除成功！')
    }
  }

  const filteredRules = selectedConfig?.rules.filter(rule => {
    const matchesSearch = !searchText || 
      rule.title.toLowerCase().includes(searchText.toLowerCase()) ||
      rule.description.toLowerCase().includes(searchText.toLowerCase())
    const matchesLevel = levelFilter === 'all' || rule.level === levelFilter
    return matchesSearch && matchesLevel
  }) || []

  if (loading) {
    return (
      <div className="code-review-page">
        <div className="page-content" style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '400px'
        }}>
          <div style={{ textAlign: 'center' }}>
            <div
              className="animate-spin"
              style={{
                width: '40px',
                height: '40px',
                border: '4px solid #f3f3f3',
                borderTop: '4px solid #667eea',
                borderRadius: '50%',
                margin: '0 auto 16px'
              }}
            />
            <div style={{ color: 'var(--text-secondary)' }}>加载中...</div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="code-review-page">
      {/* 页面头部 */}
      <div className="page-header">
        <div className="header-content">
          <div className="header-title">
            <h1>规则配置</h1>
            <p>代码审查规则管理和配置</p>
          </div>

          <div className="header-actions">
            <button
              className="action-btn primary"
              onClick={handleAddRule}
              style={{
                background: 'var(--primary-gradient)',
                color: 'white',
                border: 'none',
                padding: '12px 24px',
                borderRadius: '12px',
                fontSize: '14px',
                fontWeight: 600,
                cursor: 'pointer',
                transition: 'var(--transition-quick)',
                boxShadow: 'var(--shadow-sm)'
              }}
            >
              ➕ 添加规则
            </button>
          </div>
        </div>
      </div>

      <div className="page-content" style={{
        padding: 0,
        overflow: 'hidden',
        position: 'relative'
      }}>
        {/* 左侧配置选择面板 */}
        <div style={{
          width: '320px',
          height: '100%',
          background: 'var(--bg-glass)',
          backdropFilter: 'var(--blur-md)',
          borderRight: '1px solid var(--border-light)',
          padding: '24px',
          overflow: 'auto',
          position: 'absolute',
          left: 0,
          top: 0,
          bottom: 0
        }}>
          <div style={{ marginBottom: '24px' }}>
            <h3 style={{
              margin: '0 0 8px 0',
              fontSize: '18px',
              fontWeight: 700,
              color: 'var(--text-primary)'
            }}>
              配置列表
            </h3>
            <p style={{
              margin: 0,
              fontSize: '14px',
              color: 'var(--text-muted)'
            }}>
              选择要管理的规则配置
            </p>
          </div>

          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            {configs.map((config) => (
              <div
                key={config.language}
                onClick={() => setSelectedConfig(config)}
                style={{
                  padding: '16px',
                  borderRadius: '12px',
                  background: selectedConfig?.language === config.language
                    ? 'var(--primary-light)'
                    : 'rgba(255, 255, 255, 0.5)',
                  border: selectedConfig?.language === config.language
                    ? '2px solid var(--primary-color)'
                    : '1px solid var(--border-light)',
                  cursor: 'pointer',
                  transition: 'var(--transition-quick)'
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px' }}>
                  <span style={{ fontSize: '16px' }}>💻</span>
                  <span style={{
                    fontSize: '14px',
                    fontWeight: 600,
                    color: 'var(--text-primary)',
                    textTransform: 'capitalize'
                  }}>
                    {config.language}
                  </span>
                  <span style={{
                    padding: '2px 6px',
                    borderRadius: '4px',
                    fontSize: '11px',
                    background: 'var(--info-color)',
                    color: 'white'
                  }}>
                    {config.business}
                  </span>
                </div>
                <div style={{
                  fontSize: '12px',
                  color: 'var(--text-muted)'
                }}>
                  {config.rules.length} 条规则 · v{config.version}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 右侧规则详情面板 */}
        <div style={{
          marginLeft: '320px',
          height: '100%',
          padding: '24px 32px',
          overflow: 'auto'
        }}>
          {selectedConfig && (
            <>
              {/* 配置信息头部 */}
              <div style={{ marginBottom: '32px' }}>
                <div style={{ marginBottom: '16px' }}>
                  <h2 style={{
                    margin: '0 0 8px 0',
                    fontSize: '24px',
                    fontWeight: 700,
                    color: 'var(--text-primary)',
                    textTransform: 'capitalize'
                  }}>
                    {selectedConfig.language} 规则配置
                  </h2>
                  <p style={{
                    margin: 0,
                    fontSize: '14px',
                    color: 'var(--text-secondary)'
                  }}>
                    {selectedConfig.description}
                  </p>
                </div>
            
                {/* 统计卡片 */}
                <div className="enhanced-stats-grid" style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(4, 1fr)',
                  gap: '16px',
                  marginBottom: '24px'
                }}>
                  <div className="enhanced-stat-card danger">
                    <div className="stat-icon">🚨</div>
                    <div className="stat-content">
                      <div className="stat-value">
                        {selectedConfig.rules.filter(r => r.level === 'P0').length}
                      </div>
                      <div className="stat-label">P0 严重</div>
                    </div>
                  </div>
                    
                  <div className="enhanced-stat-card warning">
                    <div className="stat-icon">⚠️</div>
                    <div className="stat-content">
                      <div className="stat-value">
                        {selectedConfig.rules.filter(r => r.level === 'P1').length}
                      </div>
                      <div className="stat-label">P1 重要</div>
                    </div>
                  </div>
                    
                  <div className="enhanced-stat-card info">
                    <div className="stat-icon">ℹ️</div>
                    <div className="stat-content">
                      <div className="stat-value">
                        {selectedConfig.rules.filter(r => r.level === 'P2').length}
                      </div>
                      <div className="stat-label">P2 一般</div>
                    </div>
                  </div>
                    
                  <div className="enhanced-stat-card success">
                    <div className="stat-icon">✅</div>
                    <div className="stat-content">
                      <div className="stat-value">
                        {selectedConfig.rules.filter(r => r.enabled).length}
                      </div>
                      <div className="stat-label">已启用</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* 搜索和筛选 */}
              <div className="enhanced-filter-section">
                <div className="filter-group">
                  <div className="search-container">
                    <div className="search-icon">🔍</div>
                    <input
                      type="text"
                      placeholder="搜索规则标题或描述..."
                      value={searchText}
                      onChange={(e) => setSearchText(e.target.value)}
                      className="enhanced-search-input"
                    />
                    {searchText && (
                      <button
                        className="clear-search"
                        onClick={() => setSearchText('')}
                      >
                        ✕
                      </button>
                    )}
                  </div>

                  <div className="filter-controls">
                    <select
                      value={levelFilter}
                      onChange={(e) => setLevelFilter(e.target.value)}
                      className="enhanced-filter-select"
                    >
                      <option value="all">全部级别</option>
                      <option value="P0">P0 严重</option>
                      <option value="P1">P1 重要</option>
                      <option value="P2">P2 一般</option>
                    </select>
                  </div>
                </div>

                <div className="results-info">
                  <span>找到 <strong>{filteredRules.length}</strong> 条规则</span>
                </div>
              </div>

              {/* 规则列表 */}
              <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                {filteredRules.map((rule) => (
                  <div
                    key={rule.id}
                    className="enhanced-task-card"
                    style={{
                      minHeight: 'auto',
                      cursor: 'default'
                    }}
                  >
                    <div className="task-card-header">
                      <div style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'flex-start',
                        width: '100%'
                      }}>
                        <div style={{ flex: 1 }}>
                          <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '8px' }}>
                            <span style={{
                              padding: '4px 8px',
                              borderRadius: '8px',
                              fontSize: '11px',
                              fontWeight: 700,
                              background: getLevelColor(rule.level),
                              color: 'white'
                            }}>
                              {getLevelText(rule.level)}
                            </span>
                            <h3 style={{
                              margin: 0,
                              fontSize: '16px',
                              fontWeight: 600,
                              color: 'var(--text-primary)'
                            }}>
                              {rule.title}
                            </h3>
                            <span style={{
                              padding: '2px 6px',
                              borderRadius: '4px',
                              fontSize: '11px',
                              background: rule.enabled ? '#10b981' : '#6b7280',
                              color: 'white'
                            }}>
                              {rule.enabled ? '已启用' : '已禁用'}
                            </span>
                          </div>

                          <p style={{
                            margin: '0 0 12px 0',
                            fontSize: '14px',
                            color: 'var(--text-secondary)',
                            lineHeight: 1.5
                          }}>
                            {rule.description}
                          </p>

                          {/* 关键词 */}
                          <div style={{ marginBottom: '12px' }}>
                            <span style={{
                              fontSize: '12px',
                              color: 'var(--text-muted)',
                              marginRight: '8px'
                            }}>
                              关键词：
                            </span>
                            {rule.keywords.slice(0, 3).map((keyword) => (
                              <span
                                key={keyword}
                                style={{
                                  padding: '2px 6px',
                                  borderRadius: '4px',
                                  fontSize: '11px',
                                  background: 'rgba(0, 0, 0, 0.05)',
                                  color: 'var(--text-secondary)',
                                  marginRight: '4px'
                                }}
                              >
                                {keyword}
                              </span>
                            ))}
                            {rule.keywords.length > 3 && (
                              <span style={{
                                fontSize: '11px',
                                color: 'var(--text-muted)'
                              }}>
                                +{rule.keywords.length - 3}
                              </span>
                            )}
                          </div>

                          {/* 示例代码 */}
                          {rule.examples.length > 0 && (
                            <div style={{
                              background: 'var(--bg-secondary)',
                              padding: '12px',
                              borderRadius: '8px',
                              border: '1px solid var(--border-light)'
                            }}>
                              <div style={{
                                fontSize: '12px',
                                color: 'var(--text-muted)',
                                marginBottom: '8px'
                              }}>
                                示例：
                              </div>
                              <code style={{
                                fontFamily: 'Monaco, Consolas, "Courier New", monospace',
                                fontSize: '12px',
                                color: 'var(--text-secondary)',
                                lineHeight: 1.4
                              }}>
                                {rule.examples[0].length > 80
                                  ? rule.examples[0].substring(0, 80) + '...'
                                  : rule.examples[0]
                                }
                              </code>
                            </div>
                          )}
                        </div>

                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '8px',
                          marginLeft: '16px'
                        }}>
                          <Switch
                            checked={rule.enabled}
                            onChange={() => handleToggleRule(rule.id)}
                          />
                          <button
                            onClick={() => handleEditRule(rule)}
                            style={{
                              padding: '6px 12px',
                              border: '1px solid var(--primary-color)',
                              borderRadius: '6px',
                              background: 'transparent',
                              color: 'var(--primary-color)',
                              fontSize: '12px',
                              cursor: 'pointer',
                              transition: 'var(--transition-fast)'
                            }}
                          >
                            编辑
                          </button>
                          <button
                            onClick={() => handleDeleteRule(rule.id)}
                            style={{
                              padding: '6px 12px',
                              border: '1px solid var(--danger-color)',
                              borderRadius: '6px',
                              background: 'transparent',
                              color: 'var(--danger-color)',
                              fontSize: '12px',
                              cursor: 'pointer',
                              transition: 'var(--transition-fast)'
                            }}
                          >
                            删除
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
                  
                {filteredRules.length === 0 && (
                  <div className="empty-state">
                    <div className="empty-icon">📋</div>
                    <h3>未找到匹配的规则</h3>
                    <p>尝试调整搜索条件或筛选器</p>
                  </div>
                )}
              </div>
            </>
          )}
        </div>
      </div>
          
      {/* 规则编辑弹窗 */}
      {showRuleEditor && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            width: '600px',
            maxHeight: '80vh',
            background: 'var(--bg-primary)',
            borderRadius: '16px',
            padding: '24px',
            overflow: 'auto',
            boxShadow: 'var(--shadow-xl)'
          }}>
            <div style={{ marginBottom: '24px' }}>
              <h3 style={{
                margin: '0 0 8px 0',
                fontSize: '20px',
                fontWeight: 700,
                color: 'var(--text-primary)'
              }}>
                {editingRule ? '编辑规则' : '添加规则'}
              </h3>
              <p style={{
                margin: 0,
                fontSize: '14px',
                color: 'var(--text-secondary)'
              }}>
                {editingRule ? '修改规则信息' : '创建新的审查规则'}
              </p>
            </div>

            <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '8px',
                  fontSize: '14px',
                  fontWeight: 500,
                  color: 'var(--text-primary)'
                }}>
                  规则ID
                </label>
                <Input
                  value={ruleForm.id}
                  onChange={(e) => setRuleForm({...ruleForm, id: e.target.value})}
                  placeholder="例如：SEC-P0-1"
                />
              </div>

              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '8px',
                  fontSize: '14px',
                  fontWeight: 500,
                  color: 'var(--text-primary)'
                }}>
                  优先级
                </label>
                <Select
                  value={ruleForm.level}
                  onChange={(value) => setRuleForm({...ruleForm, level: value as 'P0' | 'P1' | 'P2'})}
                >
                  <Select.Option value="P0">P0 严重</Select.Option>
                  <Select.Option value="P1">P1 重要</Select.Option>
                  <Select.Option value="P2">P2 一般</Select.Option>
                </Select>
              </div>

              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '8px',
                  fontSize: '14px',
                  fontWeight: 500,
                  color: 'var(--text-primary)'
                }}>
                  规则标题
                </label>
                <Input
                  value={ruleForm.title}
                  onChange={(e) => setRuleForm({...ruleForm, title: e.target.value})}
                  placeholder="例如：SQL注入漏洞"
                />
              </div>

              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '8px',
                  fontSize: '14px',
                  fontWeight: 500,
                  color: 'var(--text-primary)'
                }}>
                  规则描述
                </label>
                <Input
                  value={ruleForm.description}
                  onChange={(e) => setRuleForm({...ruleForm, description: e.target.value})}
                  placeholder="详细描述这个规则的检查内容和目的"
                  style={{ minHeight: '80px' }}
                />
              </div>

              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '8px',
                  fontSize: '14px',
                  fontWeight: 500,
                  color: 'var(--text-primary)'
                }}>
                  关键词 <span style={{ fontSize: '12px', color: 'var(--text-muted)' }}>(用逗号分隔)</span>
                </label>
                <Input
                  value={ruleForm.keywords}
                  onChange={(e) => setRuleForm({...ruleForm, keywords: e.target.value})}
                  placeholder="例如：SQL注入, 字符串拼接, execute, query"
                />
              </div>

              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '8px',
                  fontSize: '14px',
                  fontWeight: 500,
                  color: 'var(--text-primary)'
                }}>
                  示例代码 <span style={{ fontSize: '12px', color: 'var(--text-muted)' }}>(每行一个示例)</span>
                </label>
                <Input
                  value={ruleForm.examples}
                  onChange={(e) => setRuleForm({...ruleForm, examples: e.target.value})}
                  placeholder={`例如：
cursor.execute(f"SELECT * FROM users WHERE id = {user_id}")
query = "SELECT * FROM " + table_name
直接拼接用户输入到SQL语句`}
                  style={{
                    fontFamily: 'Monaco, Consolas, "Courier New", monospace',
                    minHeight: '100px'
                  }}
                />
              </div>

              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <Switch
                  checked={ruleForm.enabled}
                  onChange={(e) => setRuleForm({...ruleForm, enabled: e.target.checked})}
                />
                <span style={{ fontSize: '14px', color: 'var(--text-primary)' }}>
                  启用此规则
                </span>
              </div>
            </div>

            <div style={{
              display: 'flex',
              gap: '12px',
              marginTop: '24px',
              paddingTop: '16px',
              borderTop: '1px solid var(--border-light)'
            }}>
              <Button
                onClick={() => setShowRuleEditor(false)}
                style={{ flex: 1 }}
              >
                取消
              </Button>
              <Button
                type="primary"
                onClick={handleSaveRule}
                style={{
                  flex: 1,
                  background: 'var(--primary-gradient)',
                  border: 'none'
                }}
              >
                保存
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
})

export default RuleConfigPage
