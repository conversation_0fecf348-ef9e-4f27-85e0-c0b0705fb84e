// 测试代码审查结果展示页面
import React, { useState } from '@rome/stone/react'
import { observer } from '@rome/stone/mobx-react'
import { Button } from '@ss/mtd-react'
import CodeReviewResultView from '../components/results/CodeReviewResultView'

// 您提供的测试数据
const testReviewData = {
  "summary": {
    "checkBranch": "reco/state_subsidies_audit:feature/add_audit_thrift_client_squirrel",
    "reviewTime": "09:52:17",
    "reviewer": "AI代码审查系统",
    "overallResult": "不通过",
    "resultDescription": "P2:12个",
    "totalProblems": 12,
    "taskExecutionSummary": "执行12个任务，0个成功，0个发现问题",
    "qualityGatesSummary": "质量门禁未通过，通过率50.0%"
  },
  "scoring": {
    "overallScore": 0,
    "maxScore": 100,
    "scoreBreakdown": {
      "criticalIssues": {
        "score": 30,
        "maxScore": 30,
        "description": "严重问题扣分 (0个)"
      },
      "warningIssues": {
        "score": 25,
        "maxScore": 25,
        "description": "警告问题扣分 (0个)"
      },
      "moderateIssues": {
        "score": 0,
        "maxScore": 25,
        "description": "中等问题扣分 (12个)"
      },
      "minorIssues": {
        "score": 20,
        "maxScore": 20,
        "description": "轻微问题扣分 (0个)"
      }
    },
    "qualityGrade": "F",
    "passThreshold": 80,
    "isPassed": false
  },
  "problems": [
    {
      "level": "P2",
      "problem": "缺少必要的异常处理。Redis操作可能会抛出异常，但当前代码没有进行捕获和处理。",
      "suggestion": "在get和set方法中添加try-catch块来捕获和处理可能的Redis异常，例如连接超时、网络错误等。同时，考虑添加日志记录以便于问题排查。",
      "targetCode": "public String getToken(String key) {\n    StoreKey storeKey = new StoreKey(category, key);\n    return redisStoreClient0.get(storeKey);\n}",
      "codePosition": [24, 4, 24, 26]
    },
    {
      "level": "P2",
      "problem": "变量命名不清晰。'redisStoreClient0'的命名不符合Java命名规范，且不能清晰表达其用途。",
      "suggestion": "将'redisStoreClient0'重命名为更具描述性的名称，例如'redisStoreClient'或'tokenRedisClient'。",
      "targetCode": "private RedisStoreClient redisStoreClient0;",
      "codePosition": [18, 4, 18, 11]
    },
    {
      "level": "P2",
      "problem": "缺少方法级别的JavaDoc注释。公共方法getToken和setToken缺少必要的文档注释。",
      "suggestion": "为getToken和setToken方法添加JavaDoc注释，说明方法的功能、参数和返回值。",
      "targetCode": "public String getToken(String key) {\n    StoreKey storeKey = new StoreKey(category, key);\n    return redisStoreClient0.get(storeKey);\n}",
      "codePosition": [24, 4, 24, 26]
    },
    {
      "level": "P1",
      "problem": "缺少必要的类级别docstring注释",
      "suggestion": "添加类级别的docstring，说明该类的用途、主要功能和使用方法",
      "targetCode": "@Component\npublic class CouponOrderAuditQueryServiceClient {",
      "codePosition": [14, 0, 14, 6]
    },
    {
      "level": "P2",
      "problem": "方法注释不够详细，缺少参数和返回值说明",
      "suggestion": "为每个方法添加完整的JavaDoc注释，包括方法描述、参数说明和返回值说明",
      "targetCode": "/**\n     * 审核数据检索接口\n     */",
      "codePosition": [20, 7, 20, 15]
    },
    {
      "level": "P1",
      "problem": "类注释不准确，当前注释为'地图API配置类'，但实际是优惠券订单审核查询服务的配置类",
      "suggestion": "修改类注释为'优惠券订单审核查询服务配置类'或类似的准确描述",
      "targetCode": "/**\n * 地图API配置类\n */",
      "codePosition": [11, 3, 11, 11]
    },
    {
      "level": "P2",
      "problem": "硬编码的超时时间值，可能影响代码的可维护性",
      "suggestion": "将超时时间值提取为常量或配置项，如 private static final int TIMEOUT_MS = 5000;",
      "targetCode": "proxy.setTimeout(5000);",
      "codePosition": [22, 8, 22, 24]
    },
    {
      "level": "P1",
      "problem": "Redis集群名称使用了环境特定的值，可能导致配置在不同环境中需要手动修改",
      "suggestion": "建议使用占位符或环境变量来设置集群名称，以便在不同环境中灵活配置。例如：<property name=\"clusterName\" value=\"${redis.cluster.name}\"/>",
      "targetCode": "<property name=\"clusterName\" value=\"redis-sg-state-subsidies-beijing_qa\"/>",
      "codePosition": [12, 9, 12, 22]
    }
  ],
  "statistics": {
    "totalProblems": 12,
    "criticalCount": 0,
    "warningCount": 0,
    "moderateCount": 12,
    "minorCount": 0,
    "totalCount": 12,
    "segmentsCount": 19,
    "reviewResultsCount": 19,
    "problemDistribution": {
      "P0": 0,
      "P1": 3,
      "P2": 9,
      "P3+": 0
    }
  },
  "taskDetails": [
    {
      "taskName": "Redis操作异常处理",
      "taskType": "general_check",
      "executionStatus": "partial",
      "problemsFound": 1,
      "taskSummary": "Redis操作异常处理检查部分完成，发现1个问题"
    },
    {
      "taskName": "变量命名规范",
      "taskType": "general_check",
      "executionStatus": "partial",
      "problemsFound": 1,
      "taskSummary": "变量命名规范检查部分完成，发现1个问题"
    },
    {
      "taskName": "方法文档注释",
      "taskType": "general_check",
      "executionStatus": "partial",
      "problemsFound": 1,
      "taskSummary": "方法文档注释检查部分完成，发现1个问题"
    },
    {
      "taskName": "类级别docstring注释",
      "taskType": "general_check",
      "executionStatus": "partial",
      "problemsFound": 1,
      "taskSummary": "类级别docstring注释检查部分完成，发现1个问题"
    },
    {
      "taskName": "方法注释详细度",
      "taskType": "general_check",
      "executionStatus": "partial",
      "problemsFound": 1,
      "taskSummary": "方法注释详细度检查部分完成，发现1个问题"
    },
    {
      "taskName": "类注释准确性",
      "taskType": "general_check",
      "executionStatus": "partial",
      "problemsFound": 1,
      "taskSummary": "类注释准确性检查部分完成，发现1个问题"
    },
    {
      "taskName": "硬编码值处理",
      "taskType": "general_check",
      "executionStatus": "partial",
      "problemsFound": 1,
      "taskSummary": "硬编码值处理检查部分完成，发现1个问题"
    },
    {
      "taskName": "Redis集群配置",
      "taskType": "general_check",
      "executionStatus": "partial",
      "problemsFound": 1,
      "taskSummary": "Redis集群配置检查部分完成，发现1个问题"
    }
  ],
  "qualityGates": {
    "overallStatus": "FAILED",
    "passRate": 50,
    "canDeploy": false
  }
}

const TestCodeReview: React.FC = observer(() => {
  const [showResult, setShowResult] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const handleStartReview = () => {
    setIsLoading(true)
    setShowResult(true)

    // 模拟加载过程，8秒后显示结果
    setTimeout(() => {
      setIsLoading(false)
    }, 8000)
  }

  const handleComplete = () => {
    console.log('审查完成')
    setShowResult(false)
    setIsLoading(false)
  }

  const handleClose = () => {
    setShowResult(false)
    setIsLoading(false)
  }

  return (
    <div style={{
      minHeight: '100vh',
      padding: '24px',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
    }}>
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        minHeight: 'calc(100vh - 48px)'
      }}>
        <div style={{
          background: 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(20px)',
          borderRadius: '16px',
          border: '1px solid rgba(226, 232, 240, 0.6)',
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.08)',
          minHeight: 'calc(100vh - 48px)',
          display: 'flex',
          flexDirection: 'column'
        }}>
          <div style={{
            padding: '24px',
            borderBottom: '1px solid rgba(226, 232, 240, 0.6)',
            background: 'rgba(255, 255, 255, 0.95)',
            borderRadius: '16px 16px 0 0'
          }}>
            <h1 style={{
              margin: 0,
              fontSize: '24px',
              fontWeight: 600,
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text'
            }}>
              🧪 代码审查结果测试页面
            </h1>
            <p style={{
              color: '#64748b',
              fontSize: '14px',
              margin: '8px 0 0 0'
            }}>
              测试修复后的代码审查结果展示组件
            </p>
          </div>

          <div style={{ flex: 1, padding: '24px' }}>
            {!showResult ? (
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                height: '100%',
                textAlign: 'center'
              }}>
                <div style={{ fontSize: '64px', marginBottom: '24px' }}>🔍</div>
                <h2 style={{
                  fontSize: '20px',
                  fontWeight: 600,
                  marginBottom: '16px',
                  color: '#1a1a1a'
                }}>
                  准备开始代码审查测试
                </h2>
                <p style={{
                  color: '#64748b',
                  fontSize: '14px',
                  marginBottom: '32px',
                  maxWidth: '400px'
                }}>
                  点击下方按钮开始测试代码审查结果展示功能，将使用您提供的真实审查数据进行展示。
                </p>
                <Button
                  type="primary"
                  size="large"
                  onClick={handleStartReview}
                  style={{
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    border: 'none',
                    borderRadius: '8px',
                    padding: '12px 32px',
                    fontSize: '16px',
                    fontWeight: 600
                  }}
                >
                  🚀 开始审查测试
                </Button>
              </div>
            ) : (
              <div style={{ height: '100%' }}>
                <CodeReviewResultView
                  reviewData={testReviewData}
                  isLoading={isLoading}
                  onComplete={handleComplete}
                  onClose={handleClose}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
})

export default TestCodeReview
