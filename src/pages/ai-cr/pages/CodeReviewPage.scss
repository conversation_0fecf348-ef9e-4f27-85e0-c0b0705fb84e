  /* 现代化代码审查页面样式 - 统一设计系统优化版本 */

/* CSS变量定义 - 与全局设计系统保持一致 */
:root {
  /* 主色系 - 与全局变量统一 */
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --primary-color: #667eea;
  --primary-color-hover: #5a6fd8;
  --primary-light: rgba(102, 126, 234, 0.1);
  --primary-shadow: rgba(102, 126, 234, 0.3);

  /* 状态色系 */
  --success-color: #52c41a;
  --success-light: rgba(82, 196, 26, 0.1);
  --warning-color: #faad14;
  --warning-light: rgba(250, 173, 20, 0.1);
  --danger-color: #ff4d4f;
  --danger-light: rgba(255, 77, 79, 0.1);
  --info-color: #1890ff;
  --info-light: rgba(24, 144, 255, 0.1);

  /* 文字色系 */
  --text-primary: #1a1a1a;
  --text-secondary: #666666;
  --text-tertiary: #999999;
  --text-muted: #64748b;

  /* 背景色系 */
  --bg-primary: rgba(255, 255, 255, 0.95);
  --bg-secondary: rgba(248, 250, 252, 0.8);
  --bg-glass: rgba(255, 255, 255, 0.8);
  --bg-glass-hover: rgba(255, 255, 255, 0.9);
  --bg-card: rgba(255, 255, 255, 0.8);
  --bg-card-hover: rgba(255, 255, 255, 0.9);

  /* 边框色系 */
  --border-light: rgba(226, 232, 240, 0.4);
  --border-medium: rgba(226, 232, 240, 0.6);
  --border-strong: rgba(226, 232, 240, 0.8);

  /* 阴影系统 */
  --shadow-xs: 0 1px 4px rgba(0, 0, 0, 0.04);
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 20px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.08);
  --shadow-xl: 0 20px 60px rgba(0, 0, 0, 0.12);
  --shadow-primary: 0 8px 24px rgba(102, 126, 234, 0.3);

  /* 模糊效果 */
  --blur-light: blur(10px);
  --blur-normal: blur(20px);
  --blur-strong: blur(32px);

  /* 动画系统 - 统一的缓动函数 */
  --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-smooth: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);

  /* 间距系统 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 20px;
  --spacing-2xl: 24px;
  --spacing-3xl: 32px;
  --spacing-4xl: 48px;

  /* 圆角系统 */
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 20px;

  /* 字体系统 */
  --font-xs: 11px;
  --font-sm: 12px;
  --font-base: 14px;
  --font-md: 15px;
  --font-lg: 16px;
  --font-xl: 18px;
  --font-2xl: 20px;
  --font-3xl: 24px;

  /* 全局背景 */
  --global-background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

/* 页面容器 */
.code-review-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', sans-serif;
}

/* 页面头部 */
.page-header {
  background: var(--bg-glass);
  backdrop-filter: var(--blur-md);
  border-bottom: 1px solid var(--border-light);
  padding: 24px 32px;
  position: sticky;
  top: 64px;
  z-index: 100;
  box-shadow: var(--shadow-sm);
}

/* 头部内容 */
.header-content {
  max-width: 2400px; /* 统一最大宽度 */
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  animation: slideInDown 0.8s cubic-bezier(0.16, 1, 0.3, 1);
}

.header-title h1 {
  margin: 0 0 4px 0;
  font-size: 28px;
  font-weight: 800;
  color: var(--text-primary);
  background: var(--primary-gradient);
  -webkit-text-fill-color: transparent;
  letter-spacing: -0.5px;
  }

.header-title p {
  margin: 0;
  font-size: 14px;
  color: var(--text-muted);
  font-weight: 500;
  }

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
  }

.view-toggle {
  display: flex;
  background: var(--bg-secondary);
  border-radius: 12px;
  padding: 4px;
  border: 1px solid var(--border-light);
  }

.toggle-btn {
  padding: 8px 12px;
  border: none;
  background: transparent;
  border-radius: 8px;
  cursor: pointer;
  transition: var(--transition-quick);
  font-size: 16px;
  color: var(--text-muted);
}

.toggle-btn.active {
  background: var(--primary-gradient);
  color: white;
  box-shadow: var(--shadow-sm);
}

.toggle-btn:hover:not(.active) {
  background: var(--primary-light);
  color: var(--primary-color);
}

/* 增强统计卡片 */
.enhanced-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
  padding: 0;
}

.enhanced-stat-card {
  background: var(--bg-glass);
  backdrop-filter: var(--blur-md);
  border-radius: 20px;
  border: 1px solid var(--border-light);
  padding: 24px;
  position: relative;
  overflow: hidden;
  transition: var(--transition-smooth);
  cursor: pointer;
}

.enhanced-stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  border-radius: 20px 20px 0 0;
  transition: var(--transition-quick);
}

.enhanced-stat-card.primary::before {
  background: var(--primary-gradient);
}

.enhanced-stat-card.success::before {
  background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
}

.enhanced-stat-card.warning::before {
  background: linear-gradient(135deg, var(--warning-color) 0%, #d97706 100%);
}

.enhanced-stat-card.danger::before {
  background: linear-gradient(135deg, var(--danger-color) 0%, #dc2626 100%);
}

.enhanced-stat-card.info::before {
  background: linear-gradient(135deg, var(--info-color) 0%, #1d4ed8 100%);
}

.enhanced-stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: rgba(102, 126, 234, 0.3);
}

.stat-icon {
  font-size: 32px;
  margin-bottom: 16px;
  display: block;
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
}

.stat-content {
  margin-bottom: 12px;
}

.stat-value {
  font-size: 36px;
  font-weight: 800;
  color: var(--text-primary);
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-trend {
  font-size: 12px;
  color: var(--text-muted);
  font-weight: 500;
}

.stat-progress {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: var(--text-muted);
  font-weight: 600;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--success-color);
  border-radius: 3px;
  transition: width 0.6s ease;
}

/* 增强筛选区域 */
.enhanced-filter-section {
  background: var(--bg-glass);
  backdrop-filter: var(--blur-md);
  border-radius: 20px;
  border: 1px solid var(--border-light);
  padding: 24px;
  margin-bottom: 32px;
  box-shadow: var(--shadow-sm);
}

.filter-group {
  display: flex;
  gap: 20px;
  align-items: center;
  margin-bottom: 16px;
}

.search-container {
  flex: 1;
  position: relative;
  max-width: 400px;
}

.search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 16px;
  color: var(--text-muted);
  z-index: 2;
}

.enhanced-search-input {
  width: 100%;
  height: 48px;
  padding: 0 48px 0 48px;
  border: 2px solid var(--border-light);
  border-radius: 16px;
  font-size: 14px;
  font-weight: 500;
  background: var(--bg-primary);
  transition: var(--transition-quick);
  outline: none;
}

.enhanced-search-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4px var(--primary-light);
}

.enhanced-search-input::placeholder {
  color: var(--text-muted);
  font-weight: 400;
}

.clear-search {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  border: none;
  background: var(--text-muted);
  color: white;
  border-radius: 50%;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-fast);
}

.clear-search:hover {
  background: var(--danger-color);
}

.filter-controls {
  display: flex;
  gap: 12px;
}

.enhanced-filter-select {
  height: 48px;
  padding: 0 16px;
  border: 2px solid var(--border-light);
  border-radius: 12px;
  font-size: 14px;
  font-weight: 500;
  background: var(--bg-primary);
  cursor: pointer;
  transition: var(--transition-quick);
  outline: none;
  min-width: 140px;
}

.enhanced-filter-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4px var(--primary-light);
}

.results-info {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
}

.results-info strong {
  color: var(--primary-color);
  font-weight: 700;
}

/* 任务容器 */
.tasks-container {
  transition: var(--transition-smooth);
}

.tasks-container.grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 24px;
}

.tasks-container.list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 增强任务卡片 */
.enhanced-task-card {
  background: var(--bg-glass);
  backdrop-filter: var(--blur-md);
  border-radius: 20px;
  border: 2px solid var(--border-light);
  padding: 24px;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: var(--transition-smooth);
  min-height: 280px;
  display: flex;
  flex-direction: column;
}

.enhanced-task-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--primary-light) 0%, rgba(118, 75, 162, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.enhanced-task-card:hover::before {
  opacity: 1;
}

.enhanced-task-card:hover {
  transform: translateY(-6px);
  box-shadow: var(--shadow-xl);
  border-color: var(--primary-color);
}

.enhanced-task-card.selected {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4px var(--primary-light), var(--shadow-lg);
}

/* 任务状态指示器 */
.task-status-indicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  border-radius: 20px 20px 0 0;
}

.enhanced-task-card.pending .task-status-indicator {
  background: linear-gradient(90deg, var(--warning-color) 0%, #d97706 100%);
}

.enhanced-task-card.reviewing .task-status-indicator {
  background: linear-gradient(90deg, var(--info-color) 0%, #1d4ed8 100%);
}

.enhanced-task-card.completed .task-status-indicator {
  background: linear-gradient(90deg, var(--success-color) 0%, #059669 100%);
}

/* 任务卡片头部 */
.task-card-header {
  margin-bottom: 16px;
  position: relative;
  z-index: 2;
}

.task-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.pr-id {
  font-size: 14px;
  font-weight: 700;
  color: var(--primary-color);
  background: var(--primary-light);
  padding: 4px 8px;
  border-radius: 8px;
  border: 1px solid rgba(102, 126, 234, 0.2);
}

.task-badges {
  display: flex;
  align-items: center;
  gap: 8px;
}

.issues-badge {
  background: var(--danger-color);
  color: white;
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 11px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 任务卡片内容 */
.task-card-content {
  flex: 1;
  position: relative;
  z-index: 2;
}

.task-title {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.task-details {
  margin-bottom: 16px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 13px;
  color: var(--text-secondary);
}

.detail-icon {
  font-size: 14px;
  width: 16px;
  text-align: center;
}

.detail-text {
  font-weight: 500;
}

.branch-flow {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 12px;
  border: 1px solid var(--border-light);
  margin-bottom: 16px;
}

.branch-name {
  font-size: 12px;
  padding: 4px 8px;
  background: var(--bg-primary);
  border: 1px solid var(--border-medium);
  border-radius: 6px;
  font-weight: 500;
  color: var(--text-secondary);
}

.branch-arrow {
  color: var(--text-muted);
  font-weight: 600;
}

/* 任务卡片底部 */
.task-card-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 16px;
  border-top: 1px solid var(--border-light);
  position: relative;
  z-index: 2;
}

.view-details-btn {
  background: transparent;
  border: none;
  color: var(--primary-color);
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 6px;
  transition: var(--transition-fast);
}

.view-details-btn:hover {
  background: var(--primary-light);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80px 20px;
  color: var(--text-muted);
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 24px;
  opacity: 0.6;
}

.empty-state h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--text-secondary);
}

.empty-state p {
  margin: 0 0 24px 0;
  font-size: 14px;
  color: var(--text-muted);
}

.reset-filters-btn {
  background: var(--primary-gradient);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-quick);
  box-shadow: var(--shadow-sm);
}

.reset-filters-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* 增强详情面板 */
.enhanced-details-panel {
  position: absolute;
  top: 0;
  right: 0;
  width: 40%;
  height: 100%;
  background: var(--bg-glass);
  backdrop-filter: var(--blur-lg);
  border-left: 1px solid var(--border-medium);
  box-shadow: -8px 0 32px rgba(0, 0, 0, 0.12);
  overflow: auto;
  animation: slideInRight 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  z-index: 200;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.details-header {
  padding: 24px 32px;
  border-bottom: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--bg-primary);
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: var(--shadow-sm);
}

.details-title {
  display: flex;
  align-items: center;
  gap: 16px;
}

.details-title h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 700;
  color: var(--text-primary);
}

.task-status-badge {
  display: flex;
  align-items: center;
}

.close-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 10px;
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-quick);
}

.close-btn:hover {
  background: var(--danger-color);
  color: white;
  transform: scale(1.05);
}

.details-content {
  padding: 32px;
}

/* 任务概览 */
.task-overview {
  margin-bottom: 32px;
  padding: 24px;
  background: var(--bg-secondary);
  border-radius: 16px;
  border: 1px solid var(--border-light);
}

.overview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.overview-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--primary-color);
}

.task-main-title {
  margin: 0 0 24px 0;
  font-size: 22px;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1.3;
}

.task-metadata {
  display: grid;
  gap: 16px;
}

.metadata-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid var(--border-light);
}

.metadata-item:last-child {
  border-bottom: none;
}

.metadata-label {
  font-size: 13px;
  font-weight: 600;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.metadata-value {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-secondary);
}

.branch-info-detailed {
  display: flex;
  align-items: center;
  gap: 8px;
}

.branch-info-detailed code {
  font-size: 12px;
  padding: 4px 8px;
  background: var(--bg-primary);
  border: 1px solid var(--border-medium);
  border-radius: 6px;
  font-weight: 500;
  color: var(--text-secondary);
}

.branch-info-detailed span {
  color: var(--text-muted);
  font-weight: 600;
}

/* 问题区域 */
.issues-section {
  margin-bottom: 32px;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.section-header h4 {
  margin: 0;
  font-size: 18px;
  font-weight: 700;
  color: var(--text-primary);
}

.issues-count-badge {
  background: var(--danger-color);
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.issues-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.enhanced-issue-item {
  background: var(--bg-primary);
  border-radius: 16px;
  border: 1px solid var(--border-light);
  padding: 20px;
  transition: var(--transition-quick);
}

.enhanced-issue-item:hover {
  border-color: var(--danger-color);
  box-shadow: 0 4px 16px rgba(239, 68, 68, 0.1);
}

.enhanced-issue-item .issue-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.issue-badges {
  display: flex;
  gap: 8px;
}

.issue-level-badge,
.issue-type-badge {
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 10px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.issue-level-badge.high {
  background: var(--danger-color);
  color: white;
}

.issue-level-badge.medium {
  background: var(--warning-color);
  color: white;
}

.issue-level-badge.low {
  background: var(--success-color);
  color: white;
}

.issue-type-badge.security {
  background: #ef4444;
  color: white;
}

.issue-type-badge.performance {
  background: #f59e0b;
  color: white;
}

.issue-type-badge.code-style {
  background: #3b82f6;
  color: white;
}

.issue-type-badge.logic {
  background: #8b5cf6;
  color: white;
}

.issue-number {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-muted);
  background: var(--bg-secondary);
  padding: 4px 8px;
  border-radius: 6px;
}

.issue-content {
  position: relative;
}

.issue-message {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  line-height: 1.5;
}

.issue-location {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: var(--text-muted);
  background: var(--bg-secondary);
  padding: 8px 12px;
  border-radius: 8px;
  border: 1px solid var(--border-light);
}

.file-icon {
  font-size: 14px;
}

.file-path {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-weight: 500;
  color: var(--text-secondary);
}

.line-number {
  font-weight: 600;
  color: var(--primary-color);
}

/* 操作按钮 */
.details-actions {
  display: flex;
  gap: 12px;
  padding-top: 24px;
  border-top: 1px solid var(--border-light);
}

.action-btn {
  flex: 1;
  height: 48px;
  border: none;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-quick);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.action-btn.primary {
  background: var(--primary-gradient);
  color: white;
  box-shadow: var(--shadow-sm);
}

.action-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.action-btn.secondary {
  background: var(--bg-secondary);
  color: var(--text-secondary);
  border: 1px solid var(--border-medium);
}

.action-btn.secondary:hover {
  background: var(--primary-light);
  color: var(--primary-color);
  border-color: var(--primary-color);
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .enhanced-stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  }

  .enhanced-details-panel {
    width: 45%;
  }
}

@media (max-width: 1200px) {
  .page-header {
    padding: 20px 24px;
  }
  
  .tasks-container.grid {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  }

  .enhanced-details-panel {
    width: 50%;
  }
}

@media (max-width: 1024px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .enhanced-stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 16px;
  }

  .filter-group {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .search-container {
    max-width: none;
  }

  .filter-controls {
    justify-content: stretch;
  }

  .enhanced-filter-select {
    flex: 1;
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: 16px 20px;
  }

  .enhanced-stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .tasks-container.grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .enhanced-task-card {
    min-height: auto;
    padding: 20px;
  }
  
  .enhanced-details-panel {
    width: 100%;
    position: fixed;
    top: 64px;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
  }

  .details-header {
    padding: 20px;
  }

  .details-content {
    padding: 20px;
  }
}

@media (max-width: 480px) {
  .enhanced-stats-grid {
    grid-template-columns: 1fr;
  }

  .header-title h1 {
    font-size: 24px;
  }

  .view-toggle {
    display: none;
  }

  .enhanced-filter-section {
    padding: 20px;
  }

  .filter-controls {
    flex-direction: column;
  }
}

/* 页面内容区域 */
.page-content {
  position: relative;
  z-index: 1;
  max-width: 1600px; /* 统一最大宽度 */
  margin: 0 auto;
  padding: spacing(3xl) spacing(3xl) spacing(4xl);


}
