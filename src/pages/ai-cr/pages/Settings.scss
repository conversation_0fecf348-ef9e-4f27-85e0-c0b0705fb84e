/* Settings 页面样式 - 现代化企业级设计 */
@import '../styles/variables';
@import '../styles/mixins';

/* CSS变量定义 - 现代化设计系统 */
:root {
  /* 设置页面专用变量 */
  --settings-nav-width: 280px;
  --settings-content-padding: 40px;
  --settings-section-gap: 24px;

  /* 现代化颜色系统 */
  --settings-primary: #667eea;
  --settings-primary-light: rgba(102, 126, 234, 0.1);
  --settings-primary-dark: #5a6fd8;
  --settings-success: #10b981;
  --settings-warning: #f59e0b;
  --settings-error: #ef4444;
  --settings-info: #3b82f6;

  /* 现代化背景系统 */
  --settings-bg-primary: #ffffff;
  --settings-bg-secondary: #f8fafc;
  --settings-bg-tertiary: #f1f5f9;
  --settings-bg-glass: rgba(255, 255, 255, 0.8);
  --settings-bg-glass-strong: rgba(255, 255, 255, 0.95);

  /* 现代化边框和阴影 */
  --settings-border: rgba(226, 232, 240, 0.6);
  --settings-border-light: rgba(226, 232, 240, 0.3);
  --settings-shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --settings-shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --settings-shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --settings-shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);

  /* 动画变量 */
  --settings-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --settings-transition-slow: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
  --settings-spring: cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* 页面容器 - 现代化设计 */
.settings-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
  padding-top: 64px;

  /* 动态背景效果 */
  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 20%, rgba(102, 126, 234, 0.05) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, rgba(16, 185, 129, 0.05) 0%, transparent 50%),
      radial-gradient(circle at 40% 60%, rgba(245, 158, 11, 0.03) 0%, transparent 50%);
    animation: backgroundFloat 20s ease-in-out infinite;
    pointer-events: none;
    z-index: 0;
  }

  @media (max-width: 768px) {
    padding-top: 56px;
  }
}

/* 页面头部 - 现代化设计 */
.settings-header {
  position: sticky;
  top: 64px;
  z-index: 100;
  background: var(--settings-bg-glass-strong);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--settings-border-light);
  padding: 32px 40px;
  box-shadow: var(--settings-shadow-sm);

  /* 渐变顶部装饰 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg,
      var(--settings-primary) 0%,
      var(--settings-success) 50%,
      var(--settings-info) 100%);
    opacity: 0.8;
  }

  @media (max-width: 768px) {
    top: 56px;
    padding: 24px 20px;
  }
}

/* 头部内容 - 现代化布局 */
.settings-header-content {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  animation: slideInDown 0.8s var(--settings-spring);

  .header-title {
    h1 {
      margin: 0 0 12px 0;
      font-size: 32px;
      font-weight: 800;
      background: linear-gradient(135deg, var(--settings-primary) 0%, var(--settings-success) 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
      letter-spacing: -1px;
      transition: var(--settings-transition);
      position: relative;

      &:hover {
        transform: scale(1.02);
      }

      /* 文字阴影效果 */
      &::after {
        content: attr(data-text);
        position: absolute;
        top: 2px;
        left: 2px;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        z-index: -1;
      }

      @media (max-width: 768px) {
        font-size: 28px;
      }
    }

    p {
      margin: 0;
      color: #64748b;
      font-size: 16px;
      font-weight: 500;
      opacity: 0;
      animation: fadeIn 0.8s var(--settings-spring) 0.3s forwards;
    }
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 16px;

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: flex-end;
      gap: 12px;
    }
  }
}

/* 页面内容区域 - 现代化网格布局 */
.settings-content {
  position: relative;
  z-index: 1;
  max-width: 1400px;
  margin: 0 auto;
  padding: var(--settings-content-padding);
  display: grid;
  grid-template-columns: var(--settings-nav-width) 1fr;
  gap: 40px;
  min-height: calc(100vh - 200px);

  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
    gap: 32px;
    padding: 32px 24px;
  }

  @media (max-width: 768px) {
    padding: 24px 20px;
    gap: 24px;
  }
}

/* 设置导航 - 现代化卡片设计 */
.settings-nav {
  background: var(--settings-bg-glass-strong);
  backdrop-filter: blur(20px);
  border: 1px solid var(--settings-border);
  border-radius: 20px;
  padding: 28px;
  box-shadow: var(--settings-shadow-lg);
  height: fit-content;
  position: sticky;
  top: 24px;
  animation: slideInLeft 0.8s var(--settings-spring);

  /* 渐变边框效果 */
  &::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: linear-gradient(135deg,
      var(--settings-primary) 0%,
      var(--settings-success) 50%,
      var(--settings-info) 100%);
    border-radius: 20px;
    z-index: -1;
    opacity: 0.1;
  }

  /* 内部光效 */
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: linear-gradient(180deg,
      rgba(255, 255, 255, 0.8) 0%,
      transparent 100%);
    border-radius: 20px 20px 0 0;
    pointer-events: none;
  }

  @media (max-width: 1024px) {
    position: static;
    margin-bottom: 32px;
  }

  @media (max-width: 768px) {
    padding: 24px;
    border-radius: 16px;
  }
}

/* 导航头部 - 现代化设计 */
.settings-nav-header {
  margin-bottom: 32px;
  padding-bottom: 20px;
  border-bottom: 2px solid var(--settings-border-light);
  position: relative;

  /* 装饰性渐变线 */
  &::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, var(--settings-primary), var(--settings-success));
    border-radius: 1px;
  }

  h3 {
    margin: 0 0 12px 0;
    font-size: 20px;
    font-weight: 800;
    color: #1e293b;
    letter-spacing: -0.5px;
  }

  p {
    margin: 0;
    font-size: 14px;
    color: #64748b;
    line-height: 1.5;
    font-weight: 500;
  }
}

/* 导航列表 - 现代化间距 */
.settings-nav-list {
  display: flex;
  flex-direction: column;
  gap: 16px;

  @media (max-width: 1024px) {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

/* 导航项 - 现代化卡片设计 */
.settings-nav-item {
  position: relative;
  padding: 24px;
  border-radius: 16px;
  background: var(--settings-bg-primary);
  border: 2px solid var(--settings-border-light);
  cursor: pointer;
  transition: var(--settings-transition);
  overflow: hidden;

  /* 渐变背景 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.9) 0%,
      rgba(248, 250, 252, 0.9) 100%);
    z-index: 0;
    transition: var(--settings-transition);
  }

  /* 悬浮光效 */
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
      transparent,
      rgba(102, 126, 234, 0.1),
      transparent);
    transition: left 0.8s ease;
    z-index: 1;
  }

  &:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: var(--settings-shadow-xl);
    border-color: var(--settings-primary);

    &::before {
      background: linear-gradient(135deg,
        rgba(102, 126, 234, 0.05) 0%,
        rgba(255, 255, 255, 0.95) 100%);
    }

    &::after {
      left: 100%;
    }

    .nav-item-title {
      color: var(--settings-primary);
    }

    .nav-item-icon {
      transform: scale(1.1);
    }
  }

  &.active {
    background: linear-gradient(135deg,
      var(--settings-primary-light) 0%,
      rgba(255, 255, 255, 0.9) 100%);
    border-color: var(--settings-primary);
    box-shadow: var(--settings-shadow-lg);
    transform: translateY(-2px);

    .nav-item-indicator {
      opacity: 1;
      transform: scaleY(1);
    }

    .nav-item-title {
      color: var(--settings-primary);
      font-weight: 700;
    }

    .nav-item-icon {
      background: linear-gradient(135deg,
        var(--settings-primary) 0%,
        var(--settings-primary-dark) 100%) !important;
      color: white;
      box-shadow: var(--settings-shadow-md);
    }
  }
}

/* 导航项指示器 - 现代化设计 */
.nav-item-indicator {
  position: absolute;
  top: 20px;
  left: 0;
  width: 4px;
  height: calc(100% - 40px);
  background: linear-gradient(180deg,
    var(--settings-primary) 0%,
    var(--settings-success) 100%);
  border-radius: 0 2px 2px 0;
  opacity: 0;
  transform: scaleY(0);
  transition: var(--settings-transition);
  transform-origin: center;
}

/* 导航项内容 */
.nav-item-content {
  position: relative;
  z-index: 2;
}

/* 导航项头部 */
.nav-item-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

/* 导航项图标 - 现代化设计 */
.nav-item-icon {
  width: 48px;
  height: 48px;
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  flex-shrink: 0;
  transition: var(--settings-transition);
  box-shadow: var(--settings-shadow-sm);
  border: 1px solid var(--settings-border-light);
}

/* 导航项信息 */
.nav-item-info {
  flex: 1;
  min-width: 0;
}

/* 导航项标题 - 现代化字体 */
.nav-item-title {
  font-size: 16px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 6px;
  transition: var(--settings-transition);
  letter-spacing: -0.3px;
}

/* 导航项统计 - 现代化徽章 */
.nav-item-stats {
  font-size: 11px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 8px;
  display: inline-block;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 1px solid currentColor;
}

/* 导航项描述 - 现代化排版 */
.nav-item-description {
  font-size: 13px;
  color: #64748b;
  line-height: 1.5;
  font-weight: 500;
}

/* 设置主要内容 - 现代化设计 */
.settings-main {
  animation: slideInRight 0.8s var(--settings-spring);
  min-height: 600px;
  position: relative;
}

/* 头部操作按钮 - 现代化设计 */
.header-actions {
  .changes-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
    color: var(--settings-warning);
    font-weight: 600;
    padding: 10px 16px;
    background: rgba(245, 158, 11, 0.1);
    border-radius: 12px;
    border: 2px solid rgba(245, 158, 11, 0.2);
    backdrop-filter: blur(10px);

    .changes-dot {
      width: 8px;
      height: 8px;
      background: var(--settings-warning);
      border-radius: 50%;
      box-shadow: 0 0 8px rgba(245, 158, 11, 0.4);
    }
  }

  .action-btn {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 14px 24px;
    border-radius: 14px;
    font-size: 14px;
    font-weight: 700;
    cursor: pointer;
    transition: var(--settings-transition);
    border: none;
    outline: none;
    position: relative;
    overflow: hidden;

    /* 按钮光效 */
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.6s ease;
    }

    &:hover::before {
      left: 100%;
    }

    &.secondary {
      background: var(--settings-bg-primary);
      color: #64748b;
      border: 2px solid var(--settings-border);
      box-shadow: var(--settings-shadow-sm);

      &:hover {
        background: var(--settings-primary-light);
        color: var(--settings-primary);
        border-color: var(--settings-primary);
        transform: translateY(-2px);
        box-shadow: var(--settings-shadow-md);
      }
    }

    &.primary {
      background: linear-gradient(135deg, var(--settings-primary) 0%, var(--settings-primary-dark) 100%);
      color: white;
      box-shadow: var(--settings-shadow-md);

      &:hover {
        transform: translateY(-3px);
        box-shadow: var(--settings-shadow-xl);
      }

      &:disabled {
        background: #94a3b8;
        cursor: not-allowed;
        transform: none;
        box-shadow: var(--settings-shadow-sm);
      }
    }
  }

  .loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

/* 设置区域 - 现代化卡片设计 */
.settings-section {
  background: var(--settings-bg-glass-strong);
  backdrop-filter: blur(20px);
  border: 2px solid var(--settings-border);
  border-radius: 24px;
  padding: 40px;
  margin-bottom: 32px;
  position: relative;
  overflow: hidden;
  transition: var(--settings-transition);
  box-shadow: var(--settings-shadow-lg);

  /* 渐变顶部装饰 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg,
      var(--settings-primary) 0%,
      var(--settings-success) 50%,
      var(--settings-info) 100%);
    opacity: 0.8;
  }

  /* 内部光效 */
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 80px;
    background: linear-gradient(180deg,
      rgba(255, 255, 255, 0.6) 0%,
      transparent 100%);
    border-radius: 24px 24px 0 0;
    pointer-events: none;
  }

  &:hover {
    transform: translateY(-4px);
    box-shadow: var(--settings-shadow-xl);
    border-color: var(--settings-primary);
  }

  @media (max-width: 768px) {
    padding: 28px;
    margin-bottom: 24px;
    border-radius: 20px;
  }
}

/* 区域头部 - 现代化设计 */
.section-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 40px;
  padding-bottom: 24px;
  border-bottom: 2px solid var(--settings-border-light);
  position: relative;

  /* 装饰性渐变线 */
  &::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 80px;
    height: 2px;
    background: linear-gradient(90deg, var(--settings-primary), var(--settings-success));
    border-radius: 1px;
  }

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 20px;
    margin-bottom: 32px;
  }
}

/* 区域标题组 - 现代化布局 */
.section-title-group {
  display: flex;
  align-items: flex-start;
  gap: 20px;

  .section-icon {
    width: 56px;
    height: 56px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    flex-shrink: 0;
    box-shadow: var(--settings-shadow-md);
    border: 2px solid var(--settings-border-light);
  }

  h3 {
    margin: 0 0 12px 0;
    font-size: 24px;
    font-weight: 800;
    color: #1e293b;
    letter-spacing: -0.5px;
  }

  p {
    margin: 0;
    font-size: 15px;
    color: #64748b;
    line-height: 1.6;
    font-weight: 500;
  }
}

/* 区域统计 - 现代化徽章 */
.section-stats {
  .stats-badge {
    display: inline-flex;
    align-items: center;
    padding: 8px 16px;
    background: var(--settings-primary-light);
    color: var(--settings-primary);
    border-radius: 24px;
    font-size: 12px;
    font-weight: 700;
    border: 2px solid var(--settings-primary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: var(--settings-shadow-sm);
  }
}

/* 设置表单 - 现代化设计 */
.settings-form {
  position: relative;
  z-index: 1;

  .form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 32px;
    margin-bottom: 32px;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 24px;
    }
  }

  .form-group {
    margin-bottom: 32px;

    &:last-child {
      margin-bottom: 0;
    }

    label {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 12px;
      font-size: 15px;
      font-weight: 700;
      color: #1e293b;
      letter-spacing: -0.2px;

      .label-icon {
        font-size: 18px;
        opacity: 0.8;
      }
    }

    .form-help {
      display: flex;
      align-items: flex-start;
      gap: 8px;
      margin-top: 12px;
      font-size: 13px;
      color: #64748b;
      line-height: 1.5;
      font-weight: 500;
      padding: 12px 16px;
      background: rgba(248, 250, 252, 0.8);
      border-radius: 12px;
      border: 1px solid var(--settings-border-light);

      .help-icon {
        font-size: 16px;
        margin-top: 1px;
        flex-shrink: 0;
      }
    }
  }
}

/* 增强输入框 - 现代化设计 */
.enhanced-input {
  width: 100%;
  padding: 16px 20px;
  border: 2px solid var(--settings-border);
  border-radius: 16px;
  font-size: 15px;
  font-weight: 500;
  background: var(--settings-bg-primary);
  backdrop-filter: blur(10px);
  transition: var(--settings-transition);
  outline: none;
  box-shadow: var(--settings-shadow-sm);

  &:focus {
    border-color: var(--settings-primary);
    box-shadow: 0 0 0 4px var(--settings-primary-light), var(--settings-shadow-md);
    background: var(--settings-bg-primary);
    transform: translateY(-2px);
  }

  &:hover {
    border-color: var(--settings-primary);
    transform: translateY(-1px);
  }

  &::placeholder {
    color: #94a3b8;
    font-weight: 500;
  }
}

/* 增强选择框 - 现代化设计 */
.enhanced-select {
  .mtd-select-selection {
    border: 2px solid var(--settings-border) !important;
    border-radius: 16px !important;
    background: var(--settings-bg-primary) !important;
    backdrop-filter: blur(10px);
    transition: var(--settings-transition) !important;
    box-shadow: var(--settings-shadow-sm) !important;
    padding: 12px 16px !important;
    min-height: 52px !important;

    &:hover {
      border-color: var(--settings-primary) !important;
      transform: translateY(-1px);
    }

    &.mtd-select-selection-focused {
      border-color: var(--settings-primary) !important;
      box-shadow: 0 0 0 4px var(--settings-primary-light), var(--settings-shadow-md) !important;
      transform: translateY(-2px);
    }
  }

  .mtd-select-selection-rendered {
    font-weight: 500 !important;
    font-size: 15px !important;
  }
}

/* 开关组 - 现代化设计 */
.switch-group {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 开关项 - 现代化卡片设计 */
.switch-item {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 24px;
  background: var(--settings-bg-primary);
  border: 2px solid var(--settings-border);
  border-radius: 20px;
  transition: var(--settings-transition);
  position: relative;
  overflow: hidden;
  box-shadow: var(--settings-shadow-sm);

  /* 背景渐变 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.9) 0%,
      rgba(248, 250, 252, 0.9) 100%);
    z-index: 0;
  }

  &:hover {
    background: var(--settings-bg-primary);
    border-color: var(--settings-primary);
    transform: translateY(-2px);
    box-shadow: var(--settings-shadow-md);

    &::before {
      background: linear-gradient(135deg,
        rgba(102, 126, 234, 0.05) 0%,
        rgba(255, 255, 255, 0.95) 100%);
    }
  }

  &.enhanced {
    /* 悬浮光效 */
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg,
        transparent,
        rgba(102, 126, 234, 0.1),
        transparent);
      transition: left 0.8s ease;
      z-index: 1;
    }

    &:hover::after {
      left: 100%;
    }
  }

  &.critical {
    border-color: rgba(239, 68, 68, 0.3);

    &::before {
      background: linear-gradient(135deg,
        rgba(239, 68, 68, 0.05) 0%,
        rgba(255, 255, 255, 0.95) 100%);
    }

    &:hover {
      border-color: rgba(239, 68, 68, 0.5);

      &::before {
        background: linear-gradient(135deg,
          rgba(239, 68, 68, 0.1) 0%,
          rgba(255, 255, 255, 0.9) 100%);
      }
    }
  }

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
    padding: 20px;
  }
}

/* 开关内容 - 现代化布局 */
.switch-content {
  flex: 1;
  margin-right: 20px;
  position: relative;
  z-index: 2;

  @media (max-width: 768px) {
    margin-right: 0;
  }
}

/* 开关头部 - 现代化设计 */
.switch-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 12px;

  .switch-icon {
    font-size: 22px;
    opacity: 0.8;
  }

  .switch-title {
    font-size: 17px;
    font-weight: 700;
    color: #1e293b;
    letter-spacing: -0.3px;
  }
}

/* 开关描述 - 现代化排版 */
.switch-description {
  font-size: 14px;
  color: #64748b;
  line-height: 1.6;
  font-weight: 500;
}

/* 集成卡片 */
.integration-card {
  background: rgba(255, 255, 255, 0.6);
  border: 1px solid var(--border-light);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  transition: var(--settings-transition);

  &:hover {
    background: rgba(255, 255, 255, 0.8);
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
  }

  &:last-child {
    margin-bottom: 0;
  }
}

/* 集成头部 */
.integration-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--border-light);

  .integration-logo {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--border-light);
    flex-shrink: 0;
  }

  .integration-info {
    flex: 1;

    h4 {
      margin: 0 0 4px 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--text-primary);
    }

    p {
      margin: 0;
      font-size: 14px;
      color: var(--text-muted);
      line-height: 1.4;
    }
  }

  .integration-status {
    .status-badge {
      padding: 4px 12px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: 600;

      &.connected {
        background: rgba(82, 196, 26, 0.1);
        color: #52c41a;
        border: 1px solid rgba(82, 196, 26, 0.2);
      }

      &.disconnected {
        background: rgba(107, 114, 128, 0.1);
        color: #6b7280;
        border: 1px solid rgba(107, 114, 128, 0.2);
      }
    }
  }
}

/* 头像预览 */
.avatar-preview {
  margin-top: 16px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.5);
  border: 1px solid var(--border-light);
  border-radius: 12px;

  .preview-label {
    font-size: 12px;
    font-weight: 600;
    color: var(--text-muted);
    margin-bottom: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .avatar-image {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--border-light);
  }
}

/* 现代化动画定义 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-40px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(40px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes backgroundFloat {
  0%, 100% {
    transform: translate(0, 0) rotate(0deg) scale(1);
  }
  33% {
    transform: translate(20px, -20px) rotate(120deg) scale(1.05);
  }
  66% {
    transform: translate(-15px, 15px) rotate(240deg) scale(0.95);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

/* 动画类 */
.animate-fadeIn {
  animation: fadeIn 0.8s var(--settings-spring);
}

.animate-pulse {
  animation: pulse 2s infinite;
}

/* 响应式设计优化 */
@media (max-width: 1200px) {
  .settings-content {
    gap: 24px;
    padding: 24px;
  }

  .settings-nav {
    position: static;
    margin-bottom: 24px;
  }

  .settings-nav-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .settings-page {
    padding-top: 56px;
  }

  .settings-header {
    top: 56px;
    padding: 16px 20px;
  }

  .settings-header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;

    .header-actions {
      width: 100%;
      justify-content: flex-end;
    }
  }

  .settings-content {
    padding: 20px;
    grid-template-columns: 1fr;
  }

  .settings-nav-list {
    grid-template-columns: 1fr;
  }

  .settings-section {
    padding: 20px;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .switch-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .integration-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .settings-content {
    padding: 16px;
  }

  .settings-header {
    padding: 12px 16px;
  }

  .settings-nav {
    padding: 16px;
  }

  .settings-section {
    padding: 16px;
  }

  .form-row {
    grid-template-columns: 1fr;
  }
}




