/* Settings页面 - MTD组件样式覆盖 */
/* 强制覆盖所有MTD输入框组件 */
.mtd-input,
input[class*="mtd"],
[class*="mtd-input"],
.mtd-input-wrapper input,
.mtd-input-affix-wrapper input,
.mtd-input-content input {
  all: unset !important;
  display: block !important;
  width: 100% !important;
  height: 48px !important;
  padding: 0 16px !important;
  border: 2px solid rgba(226, 232, 240, 0.4) !important;
  border-radius: 16px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  background: rgba(255, 255, 255, 0.95) !important;
  color: #1e293b !important;
  line-height: 44px !important;
  transition: all 0.2s ease !important;
  outline: none !important;
  box-shadow: none !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif !important;
  box-sizing: border-box !important;
}

.mtd-input:focus,
input[class*="mtd"]:focus,
[class*="mtd-input"]:focus,
.mtd-input-wrapper input:focus,
.mtd-input-affix-wrapper input:focus,
.mtd-input-content input:focus {
  border-color: #667eea !important;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1) !important;
  background: rgba(255, 255, 255, 1) !important;
  outline: none !important;
}

.mtd-input:hover,
input[class*="mtd"]:hover,
[class*="mtd-input"]:hover,
.mtd-input-wrapper input:hover,
.mtd-input-affix-wrapper input:hover,
.mtd-input-content input:hover {
  border-color: #667eea !important;
}

.mtd-input::placeholder,
input[class*="mtd"]::placeholder,
[class*="mtd-input"]::placeholder,
.mtd-input-wrapper input::placeholder,
.mtd-input-affix-wrapper input::placeholder,
.mtd-input-content input::placeholder {
  color: #64748b !important;
  font-weight: 400 !important;
  line-height: 44px !important;
  opacity: 1 !important;
}

/* 重置MTD组件容器样式 */
.mtd-input-wrapper,
.mtd-input-affix-wrapper,
.mtd-input-content,
[class*="mtd-input-wrapper"],
[class*="mtd-input-affix"],
[class*="mtd-input-content"] {
  all: unset !important;
  display: block !important;
  width: 100% !important;
  background: transparent !important;
}

/* 覆盖MTD Select组件 */
.mtd-select,
.mtd-select-selection,
[class*="mtd-select"] {
  all: unset !important;
  display: block !important;
  width: 100% !important;
  height: 48px !important;
  padding: 0 16px !important;
  border: 2px solid rgba(226, 232, 240, 0.4) !important;
  border-radius: 12px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  background: rgba(255, 255, 255, 0.95) !important;
  color: #1e293b !important;
  line-height: 44px !important;
  transition: all 0.2s ease !important;
  outline: none !important;
  box-shadow: none !important;
  font-family: inherit !important;
  cursor: pointer !important;
}

.mtd-select:hover,
.mtd-select-selection:hover,
[class*="mtd-select"]:hover {
  border-color: #667eea !important;
}

.mtd-select:focus,
.mtd-select-selection:focus,
[class*="mtd-select"]:focus,
.mtd-select-selection.mtd-select-selection-focused {
  border-color: #667eea !important;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1) !important;
  background: rgba(255, 255, 255, 1) !important;
  outline: none !important;
}

/* enhanced-input类的特定样式 - 确保与MTD覆盖一致 */
.enhanced-input,
input.enhanced-input,
.mtd-input.enhanced-input,
.mtd-input-wrapper .enhanced-input {
  all: unset !important;
  display: block !important;
  width: 100% !important;
  height: 48px !important;
  padding: 0 16px !important;
  border: 2px solid rgba(226, 232, 240, 0.4) !important;
  border-radius: 16px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  background: rgba(255, 255, 255, 0.95) !important;
  color: #1e293b !important;
  line-height: 44px !important;
  transition: all 0.2s ease !important;
  outline: none !important;
  box-shadow: none !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif !important;
  box-sizing: border-box !important;
}

.enhanced-input:focus,
input.enhanced-input:focus,
.mtd-input.enhanced-input:focus,
.mtd-input-wrapper .enhanced-input:focus {
  border-color: #667eea !important;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1) !important;
  background: rgba(255, 255, 255, 1) !important;
  outline: none !important;
}

.enhanced-input:hover,
input.enhanced-input:hover,
.mtd-input.enhanced-input:hover,
.mtd-input-wrapper .enhanced-input:hover {
  border-color: #667eea !important;
}

.enhanced-input::placeholder,
input.enhanced-input::placeholder,
.mtd-input.enhanced-input::placeholder,
.mtd-input-wrapper .enhanced-input::placeholder {
  color: #64748b !important;
  font-weight: 400 !important;
  line-height: 44px !important;
  opacity: 1 !important;
}

/* 覆盖全局焦点样式 */
input:focus-visible,
textarea:focus-visible,
select:focus-visible,
.enhanced-input:focus-visible {
  outline: none !important;
  outline-offset: 0 !important;
}

/* 头部内容 */
.settings-header-content {
  max-width: 1200px; /* 调整最大宽度 */
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
}

.settings-header-content h1 {
  margin: 0 0 4px 0;
  font-size: 28px;
  font-weight: 800;
  color: #1e293b;
  letter-spacing: -0.5px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  transition: all 0.3s ease;
}

.settings-header-content h1:hover {
  transform: scale(1.02);
}

.settings-header-content p {
  margin: 0;
  color: #64748b;
  font-size: 14px;
  font-weight: 500;
}
/* 页面内容区域 */
.settings-content {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 32px;
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: 32px;
  min-height: calc(100vh - 200px);
}

/* 设置区块样式 */
.settings-section {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(226, 232, 240, 0.4);
  padding: 32px;
  margin-bottom: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.section-title {
  margin: 0 0 24px 0;
  font-size: 20px;
  font-weight: 700;
  color: #1e293b;
}

.section-description {
  margin: 0 0 32px 0;
  font-size: 14px;
  color: #64748b;
  line-height: 1.5;
}

/* 表单样式 */
.form-group {
  margin-bottom: 24px;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 24px;
}

.form-description {
  margin-top: 8px;
  font-size: 12px;
  color: #64748b;
  line-height: 1.4;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  outline: none;
  text-decoration: none;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.8);
  color: #475569;
  border: 1px solid rgba(226, 232, 240, 0.6);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 1);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .settings-content {
    grid-template-columns: 1fr;
    gap: 24px;
    padding: 24px;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .settings-content {
    padding: 20px;
    gap: 20px;
  }

  .settings-section {
    padding: 24px;
    border-radius: 16px;
  }

  .settings-header-content h1 {
    font-size: 24px;
  }
}
