/* Settings 页面样式 - 高级感优化版本 */
@import '../styles/variables';
@import '../styles/mixins';

/* 页面容器 - 增强版本 */
.settings-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #f1f5f9 100%);
  position: relative;
  overflow-x: hidden;

  /* 添加动态背景效果 */
  &::before {
    content: '';
    position: fixed;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle at 20% 30%, rgba(102, 126, 234, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(16, 185, 129, 0.03) 0%, transparent 50%);
    animation: backgroundFloat 30s ease-in-out infinite;
    pointer-events: none;
    z-index: 0;
  }
}

/* 页面头部 - 重新设计 */
.settings-header {
  position: relative;
  z-index: 1;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(226, 232, 240, 0.6);
  padding: spacing(3xl) spacing(3xl) spacing(2xl);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);

  /* 微妙的顶部光效 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(16, 185, 129, 0.4), transparent);
  }

  @include mobile {
    padding: spacing(2xl) spacing(xl) spacing(xl);
  }
}

/* 头部内容 */
.settings-header-content {
  max-width: 1200px;
  margin: 0 auto;
  animation: slideInDown 0.8s cubic-bezier(0.16, 1, 0.3, 1);

  h1 {
    margin: 0 0 spacing(sm) 0;
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, #10b981 0%, #047857 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    letter-spacing: -1px;
    transition: all 0.3s ease;

    &:hover {
      transform: scale(1.02);
    }

    @include mobile {
      font-size: 2rem;
    }
  }

  p {
    margin: 0;
    color: color(tertiary);
    font-size: map-get($font-size, lg);
    font-weight: map-get($font-weight, medium);
    opacity: 0;
    animation: fadeIn 0.8s cubic-bezier(0.16, 1, 0.3, 1) 0.3s forwards;
  }
}

/* 页面内容区域 */
.settings-content {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: spacing(3xl) spacing(3xl) spacing(4xl);
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: spacing(4xl);

  @include tablet {
    grid-template-columns: 1fr;
    gap: spacing(3xl);
    padding: spacing(2xl) spacing(xl);
  }

  @include mobile {
    padding: spacing(xl);
  }
}

/* 设置导航 - 重新设计 */
.settings-nav {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: radius(xl);
  padding: spacing(xl);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  height: fit-content;
  position: sticky;
  top: spacing(2xl);
  animation: slideInLeft 0.8s cubic-bezier(0.16, 1, 0.3, 1);

  /* 微妙的左侧光效 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 2px;
    height: 100%;
    background: linear-gradient(180deg, transparent, rgba(16, 185, 129, 0.4), transparent);
    border-radius: 0 radius(sm) radius(sm) 0;
  }

  @include tablet {
    position: static;
    display: flex;
    overflow-x: auto;
    padding: spacing(lg);

    @include scrollbar-custom;
  }
}

/* 导航列表 */
.settings-nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: spacing(xs);

  @include tablet {
    flex-direction: row;
    gap: spacing(md);
    min-width: max-content;
  }
}

/* 导航项 */
.settings-nav-item {
  display: block;
  padding: spacing(md) spacing(lg);
  color: color(secondary);
  text-decoration: none;
  border-radius: radius(md);
  font-size: map-get($font-size, base);
  font-weight: map-get($font-weight, medium);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  /* 悬浮光效 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(16, 185, 129, 0.1), transparent);
    transition: left 0.5s ease;
  }

  &:hover {
    background: rgba(16, 185, 129, 0.1);
    color: $success-color;
    transform: translateX(4px);

    &::before {
      left: 100%;
    }
  }

  &.active {
    background: linear-gradient(135deg, #10b981 0%, #047857 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
    transform: translateX(4px);

    &::after {
      content: '';
      position: absolute;
      top: 10%;
      left: 10%;
      width: 80%;
      height: 80%;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
      border-radius: inherit;
      pointer-events: none;
    }
  }

  @include tablet {
    white-space: nowrap;
    transform: none;

    &:hover,
    &.active {
      transform: translateY(-2px);
    }
  }
}

/* 设置主要内容 */
.settings-main {
  animation: slideInRight 0.8s cubic-bezier(0.16, 1, 0.3, 1);
}

/* 设置区域 */
.settings-section {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: radius(xl);
  padding: spacing(3xl);
  margin-bottom: spacing(3xl);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  /* 微妙的顶部光效 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(16, 185, 129, 0.3), transparent);
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    border-color: rgba(16, 185, 129, 0.2);
  }

  @include mobile {
    padding: spacing(2xl);
  }
}

/* 区域标题 */
.settings-section-title {
  margin: 0 0 spacing(xl) 0;
  font-size: map-get($font-size, xl);
  font-weight: map-get($font-weight, bold);
  color: color(primary);
  display: flex;
  align-items: center;
  gap: spacing(md);
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  letter-spacing: -0.5px;

  /* 图标样式 */
  svg {
    color: $success-color;
    filter: drop-shadow(0 2px 4px rgba(16, 185, 129, 0.3));
  }
}

/* 区域描述 */
.settings-section-desc {
  margin: 0 0 spacing(2xl) 0;
  color: color(tertiary);
  font-size: map-get($font-size, base);
  font-weight: map-get($font-weight, medium);
  line-height: map-get($line-height, relaxed);
}

/* 设置组 */
.settings-group {
  margin-bottom: spacing(2xl);

  &:last-child {
    margin-bottom: 0;
  }
}

/* 设置项 */
.settings-item {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: spacing(xl) 0;
  border-bottom: 1px solid rgba(226, 232, 240, 0.3);
  transition: all 0.3s ease;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background: rgba(16, 185, 129, 0.02);
    margin: 0 calc(-1 * spacing(xl));
    padding: spacing(xl);
    border-radius: radius(md);
  }

  @include mobile {
    flex-direction: column;
    align-items: flex-start;
    gap: spacing(lg);
  }
}

/* 设置项信息 */
.settings-item-info {
  flex: 1;
  margin-right: spacing(xl);

  @include mobile {
    margin-right: 0;
  }
}

/* 设置项标题 */
.settings-item-title {
  margin: 0 0 spacing(sm) 0;
  font-size: map-get($font-size, lg);
  font-weight: map-get($font-weight, semibold);
  color: color(primary);
  transition: color 0.3s ease;
}

.settings-item:hover .settings-item-title {
  color: $success-color;
}

/* 设置项描述 */
.settings-item-desc {
  margin: 0;
  color: color(tertiary);
  font-size: map-get($font-size, base);
  font-weight: map-get($font-weight, medium);
  line-height: map-get($line-height, relaxed);
}

/* 设置项控件 */
.settings-item-control {
  flex-shrink: 0;

  @include mobile {
    width: 100%;
  }
}

/* 开关控件 - 重新设计 */
.switch {
  position: relative;
  display: inline-block;
  width: 56px;
  height: 32px;
  cursor: pointer;
}

.switch-input {
  opacity: 0;
  width: 0;
  height: 0;
}

.switch-slider {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(107, 114, 128, 0.3);
  border-radius: 32px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);

  &::before {
    content: '';
    position: absolute;
    height: 24px;
    width: 24px;
    left: 4px;
    bottom: 4px;
    background: white;
    border-radius: 50%;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }
}

.switch-input:checked + .switch-slider {
  background: linear-gradient(135deg, #10b981 0%, #047857 100%);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);

  &::before {
    transform: translateX(24px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }
}

.switch:hover .switch-slider {
  transform: scale(1.05);
}

.switch:active .switch-slider {
  transform: scale(1.02);
}

/* 输入框 - 增强版本 */
.settings-input {
  width: 100%;
  max-width: 300px;
  padding: spacing(md) spacing(lg);
  border: 2px solid rgba(226, 232, 240, 0.6);
  border-radius: radius(md);
  font-size: map-get($font-size, base);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;

  &:focus {
    border-color: $success-color;
    box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.1);
    background: rgba(255, 255, 255, 1);
    transform: translateY(-1px);
  }

  &::placeholder {
    color: color(tertiary);
    font-weight: map-get($font-weight, medium);
  }

  @include mobile {
    max-width: none;
  }
}

/* 选择框 - 增强版本 */
.settings-select {
  width: 100%;
  max-width: 300px;
  padding: spacing(md) spacing(lg);
  border: 2px solid rgba(226, 232, 240, 0.6);
  border-radius: radius(md);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  font-size: map-get($font-size, base);
  font-weight: map-get($font-weight, medium);
  color: color(secondary);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;

  &:focus {
    border-color: $success-color;
    box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.1);
    transform: translateY(-1px);
  }

  &:hover {
    border-color: rgba(16, 185, 129, 0.3);
  }

  @include mobile {
    max-width: none;
  }
}

/* 文本域 - 增强版本 */
.settings-textarea {
  width: 100%;
  min-height: 120px;
  padding: spacing(lg);
  border: 2px solid rgba(226, 232, 240, 0.6);
  border-radius: radius(md);
  font-size: map-get($font-size, base);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
  resize: vertical;
  font-family: inherit;
  line-height: map-get($line-height, relaxed);

  &:focus {
    border-color: $success-color;
    box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.1);
    background: rgba(255, 255, 255, 1);
    transform: translateY(-1px);
  }

  &::placeholder {
    color: color(tertiary);
    font-weight: map-get($font-weight, medium);
  }
}

/* 按钮组 */
.settings-buttons {
  display: flex;
  gap: spacing(lg);
  margin-top: spacing(2xl);
  padding-top: spacing(2xl);
  border-top: 1px solid rgba(226, 232, 240, 0.6);

  @include mobile {
    flex-direction: column;
  }
}

/* 按钮 - 重新设计 */
.settings-btn {
  padding: spacing(md) spacing(2xl);
  border-radius: radius(md);
  font-size: map-get($font-size, base);
  font-weight: map-get($font-weight, semibold);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  position: relative;
  overflow: hidden;

  /* 按钮光效 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
  }

  &:hover::before {
    left: 100%;
  }

  &.primary {
    background: linear-gradient(135deg, #10b981 0%, #047857 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
    }

    &:active {
      transform: translateY(-1px);
    }
  }

  &.secondary {
    background: transparent;
    color: color(secondary);
    border: 2px solid rgba(226, 232, 240, 0.6);

    &:hover {
      background: rgba(16, 185, 129, 0.1);
      border-color: rgba(16, 185, 129, 0.3);
      color: $success-color;
      transform: translateY(-1px);
    }
  }

  &.danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
    }

    &:active {
      transform: translateY(-1px);
    }
  }
}

/* 标签组 */
.settings-tags {
  display: flex;
  flex-wrap: wrap;
  gap: spacing(sm);
  margin-top: spacing(md);
}

/* 标签 */
.settings-tag {
  padding: spacing(xs) spacing(md);
  background: rgba(16, 185, 129, 0.1);
  color: $success-color;
  border-radius: radius(sm);
  font-size: map-get($font-size, sm);
  font-weight: map-get($font-weight, medium);
  border: 1px solid rgba(16, 185, 129, 0.2);
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    background: rgba(16, 185, 129, 0.2);
    transform: scale(1.05);
  }

  &.removable {
    padding-right: spacing(xs);
    display: flex;
    align-items: center;
    gap: spacing(xs);

    .remove-tag {
      background: none;
      border: none;
      color: inherit;
      cursor: pointer;
      font-size: 14px;
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 16px;
      height: 16px;
      border-radius: 50%;
      transition: all 0.2s ease;

      &:hover {
        background: rgba(239, 68, 68, 0.2);
        color: $error-color;
      }
    }
  }
}

/* 卡片网格 */
.settings-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: spacing(xl);
  margin-top: spacing(xl);

  @include mobile {
    grid-template-columns: 1fr;
  }
}

/* 设置卡片 */
.settings-card {
  background: rgba(248, 250, 252, 0.6);
  border: 1px solid rgba(226, 232, 240, 0.4);
  border-radius: radius(lg);
  padding: spacing(xl);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  /* 悬浮光效 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(16, 185, 129, 0.05), transparent);
    transition: left 0.5s ease;
  }

  &:hover {
    background: rgba(255, 255, 255, 0.8);
    border-color: rgba(16, 185, 129, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);

    &::before {
      left: 100%;
    }
  }
}

/* 卡片标题 */
.settings-card-title {
  margin: 0 0 spacing(md) 0;
  font-size: map-get($font-size, lg);
  font-weight: map-get($font-weight, semibold);
  color: color(primary);
  display: flex;
  align-items: center;
  gap: spacing(sm);

  svg {
    color: $success-color;
  }
}

/* 卡片描述 */
.settings-card-desc {
  margin: 0 0 spacing(lg) 0;
  color: color(tertiary);
  font-size: map-get($font-size, base);
  font-weight: map-get($font-weight, medium);
  line-height: map-get($line-height, relaxed);
}

/* 卡片操作 */
.settings-card-actions {
  display: flex;
  gap: spacing(md);

  @include mobile {
    flex-direction: column;
  }
}

/* 通知样式 */
.settings-notification {
  padding: spacing(lg);
  border-radius: radius(md);
  margin-bottom: spacing(lg);
  display: flex;
  align-items: center;
  gap: spacing(md);
  font-size: map-get($font-size, base);
  font-weight: map-get($font-weight, medium);
  animation: slideInDown 0.4s cubic-bezier(0.4, 0, 0.2, 1);

  &.success {
    background: rgba(16, 185, 129, 0.1);
    color: $success-color;
    border: 1px solid rgba(16, 185, 129, 0.2);
  }
  
  &.error {
    background: rgba(239, 68, 68, 0.1);
    color: $error-color;
    border: 1px solid rgba(239, 68, 68, 0.2);
  }
  
  &.warning {
    background: rgba(245, 158, 11, 0.1);
    color: $warning-color;
    border: 1px solid rgba(245, 158, 11, 0.2);
  }

  &.info {
    background: rgba(59, 130, 246, 0.1);
    color: $info-color;
    border: 1px solid rgba(59, 130, 246, 0.2);
  }
}

/* 加载状态 */
.settings-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: spacing(4xl);
  color: color(tertiary);

  .loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid rgba(16, 185, 129, 0.2);
    border-top: 3px solid $success-color;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: spacing(lg);
  }
}

/* 动画定义 */
@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
  
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
  
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes backgroundFloat {
  0%, 100% {
    transform: translate(0, 0) rotate(0deg);
  }
  33% {
    transform: translate(25px, -25px) rotate(120deg);
  }
  66% {
    transform: translate(-20px, 20px) rotate(240deg);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式优化 */
@include mobile {
  .settings-section {
    padding: spacing(2xl);
  }

  .settings-item {
    padding: spacing(lg) 0;

    &:hover {
      margin: 0 calc(-1 * spacing(lg));
      padding: spacing(lg);
    }
  }

  .settings-card {
    padding: spacing(lg);
  }
}

/* 滚动条优化 */
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(16, 185, 129, 0.3) transparent;
}

*::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

*::-webkit-scrollbar-track {
  background: transparent;
}

*::-webkit-scrollbar-thumb {
  background: rgba(16, 185, 129, 0.3);
  border-radius: 3px;
  transition: background 0.3s ease;
}

*::-webkit-scrollbar-thumb:hover {
  background: rgba(16, 185, 129, 0.5);
}

/* 选择文本样式 */
::selection {
  background: rgba(16, 185, 129, 0.1);
  color: $success-color;
}

::-moz-selection {
  background: rgba(16, 185, 129, 0.1);
  color: $success-color;
}
