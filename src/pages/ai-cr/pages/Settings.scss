/* Settings 页面样式 - 与代码审查页保持一致的设计系统 */
/* 强制刷新样式 - 版本 2024.1 */

/* 确保样式优先级 */
.elegant-content .settings-page {
  /* 强制应用我们的样式 */
}

/* 全局MTD组件样式重置 - 最高优先级 */
/* 使用更强的选择器覆盖 */
.settings-page .mtd-input,
.settings-page input[class*="mtd"],
.settings-page [class*="mtd-input"],
.settings-page .mtd-input-wrapper input,
.settings-page .mtd-input-inner,
.settings-page .mtd-input-content input {
  border: none !important;
  box-shadow: none !important;
  background: transparent !important;
}

.settings-page .mtd-input:focus,
.settings-page input[class*="mtd"]:focus,
.settings-page [class*="mtd-input"]:focus,
.settings-page .mtd-input-wrapper input:focus,
.settings-page .mtd-input-inner:focus,
.settings-page .mtd-input-content input:focus {
  border: none !important;
  box-shadow: none !important;
}

/* 额外的MTD组件覆盖 */
.settings-page .mtd-input-wrapper,
.settings-page [class*="mtd-input-wrapper"],
.settings-page .mtd-input-inner,
.settings-page [class*="mtd-input-inner"] {
  border: none !important;
  box-shadow: none !important;
}

/* CSS变量定义 - 与代码审查页保持一致 */
:root {
  /* 主色系 - 与全局变量统一 */
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --primary-color: #667eea;
  --primary-color-hover: #5a6fd8;
  --primary-light: rgba(102, 126, 234, 0.1);
  --primary-shadow: rgba(102, 126, 234, 0.3);

  /* 状态色系 */
  --success-color: #52c41a;
  --success-light: rgba(82, 196, 26, 0.1);
  --warning-color: #faad14;
  --warning-light: rgba(250, 173, 20, 0.1);
  --danger-color: #ff4d4f;
  --danger-light: rgba(255, 77, 79, 0.1);
  --info-color: #1890ff;
  --info-light: rgba(24, 144, 255, 0.1);

  /* 文字色系 */
  --text-primary: #1a1a1a;
  --text-secondary: #666666;
  --text-tertiary: #999999;
  --text-muted: #64748b;

  /* 背景色系 */
  --bg-primary: rgba(255, 255, 255, 0.95);
  --bg-secondary: rgba(248, 250, 252, 0.8);
  --bg-glass: rgba(255, 255, 255, 0.8);
  --bg-glass-hover: rgba(255, 255, 255, 0.9);
  --bg-card: rgba(255, 255, 255, 0.8);
  --bg-card-hover: rgba(255, 255, 255, 0.9);

  /* 边框色系 */
  --border-light: rgba(226, 232, 240, 0.4);
  --border-medium: rgba(226, 232, 240, 0.6);
  --border-strong: rgba(226, 232, 240, 0.8);

  /* 阴影系统 */
  --shadow-xs: 0 1px 4px rgba(0, 0, 0, 0.04);
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 20px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.08);
  --shadow-xl: 0 20px 60px rgba(0, 0, 0, 0.12);
  --shadow-primary: 0 8px 24px rgba(102, 126, 234, 0.3);

  /* 模糊效果 */
  --blur-light: blur(10px);
  --blur-md: blur(20px);
  --blur-strong: blur(32px);

  /* 动画系统 - 统一的缓动函数 */
  --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-quick: all 0.2s ease;
  --transition-normal: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-smooth: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);

  /* 间距系统 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 20px;
  --spacing-2xl: 24px;
  --spacing-3xl: 32px;
  --spacing-4xl: 48px;

  /* 圆角系统 */
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 20px;

  /* 字体系统 */
  --font-xs: 11px;
  --font-sm: 12px;
  --font-base: 14px;
  --font-md: 15px;
  --font-lg: 16px;
  --font-xl: 18px;
  --font-2xl: 20px;
  --font-3xl: 24px;

  /* 全局背景 */
  --global-background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

/* 页面容器 - 与代码审查页保持一致 */
.settings-page {
  min-height: 100vh;
  background: var(--global-background);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', sans-serif;

  /* 测试样式生效 */
  position: relative;

  /* 添加微妙的背景动画 */
  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 20%, rgba(102, 126, 234, 0.03) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, rgba(16, 185, 129, 0.03) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
  }
}

/* 页面头部 - 与代码审查页保持一致 */
.settings-header {
  background: var(--bg-glass);
  backdrop-filter: var(--blur-md);
  border-bottom: 1px solid var(--border-light);
  padding: 24px 32px;
  position: sticky;
  top: 64px;
  z-index: 100;
  box-shadow: var(--shadow-sm);

  @media (max-width: 768px) {
    top: 56px;
    padding: 20px 24px;
  }
}

/* 头部内容 - 与代码审查页保持一致 */
.settings-header-content {
  max-width: 2400px; /* 统一最大宽度 */
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  animation: slideInDown 0.8s cubic-bezier(0.16, 1, 0.3, 1);
  gap: 20px; /* 添加间距防止挤压 */

  .header-title {
    flex: 1; /* 允许标题区域伸缩 */
    min-width: 0; /* 允许内容收缩 */

    h1 {
      margin: 0 0 4px 0;
      font-size: 28px;
      font-weight: 800;
      color: var(--text-primary);
      letter-spacing: -0.5px;
      white-space: nowrap; /* 防止标题换行 */
      overflow: hidden;
      text-overflow: ellipsis;

      @media (max-width: 1200px) {
        font-size: 24px;
      }

      @media (max-width: 768px) {
        font-size: 22px;
        white-space: normal; /* 移动端允许换行 */
      }
    }

    p {
      margin: 0;
      font-size: 14px;
      color: var(--text-muted);
      font-weight: 500;

      @media (max-width: 768px) {
        font-size: 13px;
      }
    }
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-shrink: 0; /* 防止按钮被压缩 */
    min-width: 0; /* 允许内容收缩 */

    @media (max-width: 1200px) {
      gap: 12px;
    }

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: flex-end;
      gap: 12px;
      width: 100%;
    }
  }
}

/* 页面内容区域 - 与代码审查页保持一致 */
.settings-content {
  max-width: 2400px; /* 统一最大宽度 */
  margin: 0 auto;
  padding: 32px;
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: 32px;
  min-height: calc(100vh - 200px);

  @media (max-width: 1200px) {
    padding: 24px;
    gap: 24px;
  }

  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
    gap: 24px;
    padding: 24px;
  }

  @media (max-width: 768px) {
    padding: 20px;
    gap: 20px;
  }
}

/* 设置导航 - 与代码审查页保持一致 */
.settings-nav {
  background: var(--bg-glass) !important;
  backdrop-filter: var(--blur-md) !important;
  border-radius: 20px !important;
  border: 1px solid var(--border-light) !important;
  padding: 24px !important;
  position: sticky !important;
  top: 24px !important;
  height: fit-content !important;
  box-shadow: var(--shadow-sm) !important;
  animation: slideInLeft 0.8s cubic-bezier(0.16, 1, 0.3, 1) !important;

  @media (max-width: 1024px) {
    position: static !important;
    margin-bottom: 24px !important;
  }

  @media (max-width: 768px) {
    padding: 20px !important;
    border-radius: 16px !important;
  }
}

/* 导航头部 - 与代码审查页保持一致 */
.settings-nav-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--border-light);

  h3 {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 700;
    color: var(--text-primary);
  }

  p {
    margin: 0;
    font-size: 14px;
    color: var(--text-muted);
    line-height: 1.4;
  }
}

/* 导航列表 - 与代码审查页保持一致 */
.settings-nav-list {
  display: flex;
  flex-direction: column;
  gap: 12px;

  @media (max-width: 1024px) {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

/* 导航项 - 与代码审查页保持一致 */
.settings-nav-item {
  position: relative !important;
  padding: 20px !important;
  border-radius: 16px !important;
  background: rgba(255, 255, 255, 0.5) !important;
  border: 1px solid var(--border-light) !important;
  cursor: pointer !important;
  transition: var(--transition-normal) !important;
  overflow: hidden !important;
  margin-bottom: 12px !important;

  /* 悬浮光效 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.6s ease;
  }

  &:hover {
    background: rgba(255, 255, 255, 0.8) !important;
    transform: translateY(-2px) !important;
    box-shadow: var(--shadow-md) !important;

    &::before {
      left: 100%;
    }

    .nav-item-title {
      color: var(--primary-color) !important;
    }
  }

  &.active {
    background: var(--primary-light) !important;
    border-color: var(--primary-color) !important;
    box-shadow: var(--shadow-primary) !important;

    .nav-item-indicator {
      opacity: 1 !important;
    }

    .nav-item-title {
      color: var(--primary-color) !important;
      font-weight: 600 !important;
    }
  }
}

/* 导航项指示器 */
.nav-item-indicator {
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--primary-gradient);
  opacity: 0;
  transition: var(--transition-normal);
}

/* 导航项内容 */
.nav-item-content {
  position: relative;
  z-index: 1;
}

/* 导航项头部 */
.nav-item-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

/* 导航项图标 */
.nav-item-icon {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  flex-shrink: 0;
}

/* 导航项信息 */
.nav-item-info {
  flex: 1;
  min-width: 0;
}

/* 导航项标题 */
.nav-item-title {
  font-size: 15px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
  transition: var(--transition-normal);
}

/* 导航项统计 */
.nav-item-stats {
  font-size: 11px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 4px;
  display: inline-block;
}

/* 导航项描述 */
.nav-item-description {
  font-size: 12px;
  color: var(--text-muted);
  line-height: 1.4;
}

/* 设置主要内容 */
.settings-main {
  animation: slideInRight 0.8s cubic-bezier(0.16, 1, 0.3, 1);
  min-height: 600px;
}

/* 头部操作按钮 */
.header-actions {
  .changes-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: var(--warning-color);
    font-weight: 500;
    padding: 8px 12px;
    background: rgba(250, 173, 20, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(250, 173, 20, 0.2);

    .changes-dot {
      width: 8px;
      height: 8px;
      background: var(--warning-color);
      border-radius: 50%;
    }
  }

  .action-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-normal);
    border: none;
    outline: none;
    white-space: nowrap; /* 防止按钮文字换行 */
    flex-shrink: 0; /* 防止按钮被压缩 */

    @media (max-width: 1200px) {
      padding: 10px 16px;
      font-size: 13px;
    }

    @media (max-width: 768px) {
      padding: 12px 20px;
      font-size: 14px;
      width: 100%; /* 移动端按钮占满宽度 */
      justify-content: center;
    }

    &.secondary {
      background: transparent;
      color: var(--text-secondary);
      border: 1px solid var(--border-medium);

      &:hover {
        background: var(--primary-light);
        color: var(--primary-color);
        border-color: var(--primary-color);
        transform: translateY(-1px);
      }
    }

    &.primary {
      background: var(--primary-gradient);
      color: white;
      box-shadow: var(--shadow-sm);

      &:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-primary);
      }

      &:disabled {
        background: #ccc;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
      }
    }
  }

  .loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

/* 设置区域 */
.settings-section {
  background: var(--bg-glass);
  backdrop-filter: var(--blur-md);
  border-radius: 20px;
  border: 1px solid var(--border-light);
  padding: 32px;
  margin-bottom: 32px;
  position: relative;
  overflow: hidden;
  transition: var(--transition-smooth);
  box-shadow: var(--shadow-sm);

  /* 微妙的顶部光效 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--primary-gradient);
    opacity: 0.6;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
  }

  @media (max-width: 768px) {
    padding: 24px;
    margin-bottom: 24px;
  }
}

/* 区域头部 */
.section-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 32px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--border-light);

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 16px;
    margin-bottom: 24px;
  }
}

/* 区域标题组 */
.section-title-group {
  display: flex;
  align-items: flex-start;
  gap: 16px;

  .section-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    flex-shrink: 0;
    box-shadow: var(--shadow-sm);
  }

  h3 {
    margin: 0 0 8px 0;
    font-size: 20px;
    font-weight: 700;
    color: var(--text-primary);
  }

  p {
    margin: 0;
    font-size: 14px;
    color: var(--text-muted);
    line-height: 1.5;
  }
}

/* 区域统计 */
.section-stats {
  .stats-badge {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    background: var(--primary-light);
    color: var(--primary-color);
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    border: 1px solid var(--primary-color);
  }
}

/* 设置表单 */
.settings-form {
  .form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;
    margin-bottom: 24px;

    @media (max-width: 1200px) {
      grid-template-columns: 1fr;
      gap: 20px;
    }

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 20px;
    }
  }

  .form-group {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }

    label {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 8px;
      font-size: 14px;
      font-weight: 600;
      color: var(--text-primary);

      .label-icon {
        font-size: 16px;
      }
    }

    .form-help {
      display: flex;
      align-items: center;
      gap: 6px;
      margin-top: 8px;
      font-size: 12px;
      color: var(--text-muted);
      line-height: 1.4;

      .help-icon {
        font-size: 14px;
      }
    }
  }
}

/* 增强输入框 - 参考代码审查页搜索框样式 */
/* 强制覆盖MTD组件样式 - 使用最高优先级 */
.settings-page .enhanced-input,
.settings-page .enhanced-input.mtd-input,
.settings-page .mtd-input.enhanced-input,
.settings-page input.enhanced-input,
.settings-page input.mtd-input.enhanced-input,
.settings-page .mtd-input-wrapper .enhanced-input,
.settings-page .mtd-input-wrapper input.enhanced-input {
  width: 100% !important;
  height: 48px !important;
  padding: 0 16px !important;
  border: 2px solid var(--border-light) !important;
  border-radius: 16px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  background: var(--bg-primary, rgba(255, 255, 255, 0.95)) !important;
  transition: var(--transition-quick, all 0.2s ease) !important;
  outline: none !important;
  box-shadow: none !important;
  line-height: normal !important;
  min-height: 48px !important;
  max-height: 48px !important;
  color: var(--text-primary) !important;
  position: relative !important;
  z-index: 1 !important;
}

.settings-page .enhanced-input:focus,
.settings-page .enhanced-input.mtd-input:focus,
.settings-page .mtd-input.enhanced-input:focus,
.settings-page input.enhanced-input:focus,
.settings-page input.mtd-input.enhanced-input:focus,
.settings-page .mtd-input-wrapper .enhanced-input:focus,
.settings-page .mtd-input-wrapper input.enhanced-input:focus {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 4px var(--primary-light) !important;
  background: rgba(255, 255, 255, 1) !important;
}

.settings-page .enhanced-input:hover,
.settings-page .enhanced-input.mtd-input:hover,
.settings-page .mtd-input.enhanced-input:hover,
.settings-page input.enhanced-input:hover,
.settings-page input.mtd-input.enhanced-input:hover,
.settings-page .mtd-input-wrapper .enhanced-input:hover,
.settings-page .mtd-input-wrapper input.enhanced-input:hover {
  border-color: var(--primary-color) !important;
}

.settings-page .enhanced-input::placeholder,
.settings-page .enhanced-input.mtd-input::placeholder,
.settings-page .mtd-input.enhanced-input::placeholder,
.settings-page input.enhanced-input::placeholder,
.settings-page input.mtd-input.enhanced-input::placeholder,
.settings-page .mtd-input-wrapper .enhanced-input::placeholder,
.settings-page .mtd-input-wrapper input.enhanced-input::placeholder {
  color: var(--text-muted) !important;
  font-weight: 400 !important;
}

/* 增强选择框 - 参考代码审查页筛选器样式 */
/* 强制覆盖MTD Select组件样式 */
.settings-page .enhanced-select .mtd-select-selection,
.settings-page .mtd-select.enhanced-select .mtd-select-selection {
  height: 48px !important;
  border: 2px solid var(--border-light) !important;
  border-radius: 12px !important;
  background: var(--bg-primary, rgba(255, 255, 255, 0.95)) !important;
  transition: var(--transition-quick, all 0.2s ease) !important;
  box-shadow: none !important;
  min-width: 140px !important;
  line-height: 44px !important;
  min-height: 48px !important;
  max-height: 48px !important;
}

.settings-page .enhanced-select .mtd-select-selection:hover,
.settings-page .mtd-select.enhanced-select .mtd-select-selection:hover {
  border-color: var(--primary-color) !important;
}

.settings-page .enhanced-select .mtd-select-selection.mtd-select-selection-focused,
.settings-page .mtd-select.enhanced-select .mtd-select-selection.mtd-select-selection-focused {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 4px var(--primary-light) !important;
}

.settings-page .enhanced-select .mtd-select-selection-rendered,
.settings-page .mtd-select.enhanced-select .mtd-select-selection-rendered {
  font-size: 14px !important;
  font-weight: 500 !important;
  color: var(--text-primary) !important;
  line-height: 44px !important;
  padding: 0 16px !important;
}

.settings-page .enhanced-select .mtd-select-selection-placeholder,
.settings-page .mtd-select.enhanced-select .mtd-select-selection-placeholder {
  color: var(--text-muted) !important;
  font-weight: 400 !important;
  line-height: 44px !important;
  padding: 0 16px !important;
}

.settings-page .enhanced-select .mtd-select-arrow,
.settings-page .mtd-select.enhanced-select .mtd-select-arrow {
  color: var(--text-muted) !important;
}

/* 开关组 */
.switch-group {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 开关项 */
.switch-item {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 20px;
  background: rgba(255, 255, 255, 0.5);
  border: 1px solid var(--border-light);
  border-radius: 16px;
  transition: var(--transition-normal);

  &:hover {
    background: rgba(255, 255, 255, 0.8);
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
  }

  &.enhanced {
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
      transition: left 0.6s ease;
    }

    &:hover::before {
      left: 100%;
    }
  }

  &.critical {
    border-color: rgba(239, 68, 68, 0.2);
    background: rgba(239, 68, 68, 0.05);

    &:hover {
      border-color: rgba(239, 68, 68, 0.4);
      background: rgba(239, 68, 68, 0.1);
    }
  }

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
}

/* 开关内容 */
.switch-content {
  flex: 1;
  margin-right: 16px;

  @media (max-width: 768px) {
    margin-right: 0;
  }
}

/* 开关头部 */
.switch-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;

  .switch-icon {
    font-size: 18px;
  }

  .switch-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
  }
}

/* 开关描述 */
.switch-description {
  font-size: 14px;
  color: var(--text-muted);
  line-height: 1.5;
}

/* 集成卡片 */
.integration-card {
  background: rgba(255, 255, 255, 0.6);
  border: 1px solid var(--border-light);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  transition: var(--settings-transition);

  &:hover {
    background: rgba(255, 255, 255, 0.8);
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
  }

  &:last-child {
    margin-bottom: 0;
  }
}

/* 集成头部 */
.integration-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--border-light);

  .integration-logo {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--border-light);
    flex-shrink: 0;
  }

  .integration-info {
    flex: 1;

    h4 {
      margin: 0 0 4px 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--text-primary);
    }

    p {
      margin: 0;
      font-size: 14px;
      color: var(--text-muted);
      line-height: 1.4;
    }
  }

  .integration-status {
    .status-badge {
      padding: 4px 12px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: 600;

      &.connected {
        background: rgba(82, 196, 26, 0.1);
        color: #52c41a;
        border: 1px solid rgba(82, 196, 26, 0.2);
      }

      &.disconnected {
        background: rgba(107, 114, 128, 0.1);
        color: #6b7280;
        border: 1px solid rgba(107, 114, 128, 0.2);
      }
    }
  }
}

/* 头像预览 */
.avatar-preview {
  margin-top: 16px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.5);
  border: 1px solid var(--border-light);
  border-radius: 12px;

  .preview-label {
    font-size: 12px;
    font-weight: 600;
    color: var(--text-muted);
    margin-bottom: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .avatar-image {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--border-light);
  }
}

/* 动画定义 - 与代码审查页保持一致 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes backgroundFloat {
  0%, 100% {
    transform: translate(0, 0) rotate(0deg);
  }
  33% {
    transform: translate(30px, -30px) rotate(120deg);
  }
  66% {
    transform: translate(-20px, 20px) rotate(240deg);
  }
}

/* 动画类 */
.animate-fadeIn {
  animation: fadeIn 0.6s cubic-bezier(0.16, 1, 0.3, 1);
}

.animate-pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 响应式设计优化 */
@media (max-width: 1200px) {
  .settings-content {
    gap: 24px;
    padding: 24px;
  }

  .settings-nav {
    position: static;
    margin-bottom: 24px;
  }

  .settings-nav-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .settings-page {
    padding-top: 56px;
  }

  .settings-header {
    top: 56px;
    padding: 16px 20px;
  }

  .settings-header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;

    .header-actions {
      width: 100%;
      justify-content: flex-end;
    }
  }

  .settings-content {
    padding: 20px;
    grid-template-columns: 1fr;
  }

  .settings-nav-list {
    grid-template-columns: 1fr;
  }

  .settings-section {
    padding: 20px;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .switch-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .integration-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .settings-content {
    padding: 16px;
  }

  .settings-header {
    padding: 12px 16px;
  }

  .settings-nav {
    padding: 16px;
  }

  .settings-section {
    padding: 16px;
  }

  .form-row {
    grid-template-columns: 1fr;
  }
}




