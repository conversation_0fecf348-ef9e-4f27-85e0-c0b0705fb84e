// AI助手页面样式 - 统一设计系统优化版本
@import '../styles/variables';
@import '../styles/mixins';

// 页面变量定义 - 与全局设计系统保持一致
$header-height: 64px;
$header-height-mobile: 56px;
$sidebar-width: 320px;
$sidebar-width-mobile: 280px;

// 主页面容器 - 优化设计
.ai-assistant-page {
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  background: $global-background;
  overflow: hidden;
  position: relative;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;

  // 页面内容区域
  &__content {
    flex: 1;
    display: flex;
    height: calc(100vh - #{$header-height});
    margin-top: $header-height;
    overflow: hidden;
    animation: fadeIn 0.6s cubic-bezier(0.16, 1, 0.3, 1);
    max-width: 2400px; /* 统一最大宽度 */
    margin-left: auto;
    margin-right: auto;
    padding: 0 20px; /* 添加左右内边距 */
  }

  // 聊天区域 - 整体卡片样式
  &__chat {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    margin: 24px;
    position: relative;
    min-width: 0; // 防止flex收缩问题
    overflow: hidden;
  }

  &__sidebar {
    width: 280px;
    min-width: 280px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    margin: 24px 24px 24px 0;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
  }


// 聊天头部 - 优化设计
.chat-header {
  background: $header-background;
  backdrop-filter: $backdrop-filter-normal;
  padding: spacing(xl) spacing(2xl);
  border-bottom: 1px solid $border-color;
  flex-shrink: 0;
  box-shadow: shadow(sm);
  position: relative;
  z-index: 10;

  &__content {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &__info {
    flex: 1;
  }

  &__title {
    margin: 0;
    font-size: map-get($font-size, xl);
    font-weight: map-get($font-weight, bold);
    background: $primary-gradient;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: flex;
    align-items: center;
    gap: spacing(md);
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    font-weight: 600;
    letter-spacing: -0.8px;
    transition: all map-get($transition, normal);

    @include mobile {
      font-size: map-get($font-size, lg);
    }

    // AI助手主图标 - 优化动画
    &::before {
      content: '◉';
      margin-right: spacing(md);
      font-weight: 300;
      text-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
      animation: pulse 2s ease-in-out infinite;
      transition: all map-get($transition, normal);
    }

    &:hover {
      transform: scale(1.02);

      &::before {
        transform: scale(1.1);
        text-shadow: 0 4px 8px rgba(102, 126, 234, 0.4);
      }
    }
  }

  &__subtitle {
    color: color(tertiary);
    font-size: map-get($font-size, sm);
    margin-top: spacing(xs);
    font-weight: map-get($font-weight, medium);
    opacity: 0;
    animation: fadeIn 0.8s cubic-bezier(0.16, 1, 0.3, 1) 0.3s forwards;
  }

  &__actions {
    display: flex;
    align-items: center;
    gap: spacing(sm);
  }
}

// 消息容器 - 优化滚动和布局
.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: spacing(xl) spacing(2xl);
  display: flex;
  flex-direction: column;
  gap: spacing(lg);
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.02) 0%, rgba(102, 126, 234, 0.01) 100%);
  position: relative;

  @include mobile {
    padding: spacing(lg) spacing(xl);
    gap: spacing(md);
  }

  // 优化滚动条样式
  @include scrollbar-custom;
}

// 消息项 - 重新设计
.message {
  display: flex;
  align-items: flex-start;
  gap: spacing(lg);
  animation: messageSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  animation-fill-mode: forwards;

  &--user {
    flex-direction: row-reverse;
  }

  // 为每条消息添加延迟动画
  &:nth-child(1) { animation-delay: 0.1s; }
  &:nth-child(2) { animation-delay: 0.2s; }
  &:nth-child(3) { animation-delay: 0.3s; }
  &:nth-child(4) { animation-delay: 0.4s; }
  &:nth-child(5) { animation-delay: 0.5s; }

  &__avatar {
    @include avatar(40px);
    border-radius: radius(md);
    font-size: spacing(lg);
    box-shadow: shadow(md);
    position: relative;
    overflow: hidden;
    transition: all map-get($transition, normal);

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
      pointer-events: none;
    }

    &--assistant {
      background: $primary-gradient;
      color: color(white);
    }

    &--user {
      background: linear-gradient(135deg, $success-color 0%, #389e0d 100%);
      color: color(white);
    }

    @include mobile {
      @include avatar(36px);
      font-size: spacing(md);
    }

    // 头像增强效果
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    font-weight: 300;
    font-size: 18px;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);

    &:hover {
      transform: scale(1.05) rotate(5deg);
      text-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
      box-shadow: shadow(lg);
    }
  }

  &__content {
    flex: 1;
    max-width: calc(100% - 72px);

    @include mobile {
      max-width: calc(100% - 60px);
    }
  }

  &__bubble {
    padding: spacing(lg) spacing(xl);
    border-radius: radius(lg);
    font-size: map-get($font-size, base);
    line-height: map-get($line-height, relaxed);
    word-wrap: break-word;
    white-space: pre-wrap;
    box-shadow: shadow(sm);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    transition: all map-get($transition, normal);

    &--assistant {
      background: $card-background-hover;
      color: color(primary);
      margin-right: 48px;
      border: 1px solid $border-color;

      @include mobile {
        margin-right: 24px;
      }

      &:hover {
        background: rgba(255, 255, 255, 1);
        box-shadow: shadow(md);
        transform: translateY(-2px);
      }
    }

    &--user {
      background: $primary-gradient;
      color: color(white);
      margin-left: 48px;
      box-shadow: shadow(primary);

      @include mobile {
        margin-left: 24px;
      }

      &:hover {
        box-shadow: 0 12px 32px rgba(102, 126, 234, 0.4);
        transform: translateY(-2px);
      }
    }

    @include mobile {
      padding: spacing(md) spacing(lg);
      font-size: map-get($font-size, sm);
    }
  }

  &__timestamp {
    font-size: map-get($font-size, xs);
    color: color(muted);
    margin-top: spacing(sm);
    text-align: center;
    opacity: 0.7;
    transition: opacity map-get($transition, normal);

    .message--user & {
      text-align: right;
    }

    .message--assistant & {
      text-align: left;
    }

    &:hover {
      opacity: 1;
    }
  }
}

// 输入区域 - 内置按钮设计
.chat-input {
  background: transparent;
  backdrop-filter: none;
  padding: spacing(xl) spacing(2xl) spacing(2xl);
  border-top: 1px solid rgba(226, 232, 240, 0.3);
  flex-shrink: 0;
  box-shadow: none;
  position: relative;
  z-index: 10;

  &__container {
    position: relative;
    max-width: 100%;
  }

  &__wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: rgba(248, 250, 252, 0.8);
    border: 2px solid rgba(226, 232, 240, 0.6);
    border-radius: 24px;
    padding: 12px 16px;
    transition: all 0.3s ease;

    &:focus-within {
      border-color: $primary-color;
      box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
      background: rgba(255, 255, 255, 1);
      transform: translateY(-1px);
    }
  }

  &__field {
    flex: 1;
    border: none;
    outline: none;
    background: transparent;
    font-size: 14px;
    font-family: inherit;
    resize: none;
    min-height: 24px;
    max-height: 120px;
    line-height: 1.5;
    padding: 0;
    margin-right: 12px;

    &::placeholder {
      color: #94a3b8;
      font-weight: 400;
    }
  }

  &__actions {
    display: flex;
    align-items: center;
    gap: 4px;
    flex-shrink: 0;
  }

  &__upload {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 8px;
    background: transparent;
    color: #64748b;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    font-size: 16px;

    &:hover {
      background: rgba(102, 126, 234, 0.1);
      color: $primary-color;
      transform: scale(1.1);
    }
  }

  &__send {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    border: none;
    background: $primary-gradient;
    color: white;
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    flex-shrink: 0;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);

    &:hover:not(:disabled) {
      transform: scale(1.1);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none;
    }
  }
}


// 侧边栏
.sidebar {
  display: flex;
  flex-direction: column;
  height: 100%;

  &__header {
    padding: spacing(xl) spacing(xl) spacing(lg);
    border-bottom: 1px solid $border-color;
    background: $header-background;
    backdrop-filter: $backdrop-filter-light;
    flex-shrink: 0;
  }

  &__title {
    margin: 0;
    font-size: map-get($font-size, lg);
    font-weight: map-get($font-weight, semibold);
    color: color(primary);
    background: $primary-gradient;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    font-weight: 600;
    letter-spacing: -0.5px;
    transition: all map-get($transition, normal);

    &:hover {
      transform: scale(1.02);
    }
  }

  &__subtitle {
    margin: spacing(xs) 0 0 0;
    font-size: map-get($font-size, xs);
    color: color(tertiary);
    font-weight: map-get($font-weight, medium);
    opacity: 0;
    animation: fadeIn 0.8s cubic-bezier(0.16, 1, 0.3, 1) 0.5s forwards;
  }

  &__content {
    flex: 1;
    overflow-y: auto;
    padding: 0;

    // 优化滚动条样式
    @include scrollbar-custom;
  }

  &__section {
    padding: spacing(xl);
    animation: slideInUp 0.6s cubic-bezier(0.16, 1, 0.3, 1);
    animation-fill-mode: both;

    &:not(:last-child) {
      border-bottom: 1px solid rgba(226, 232, 240, 0.3);
    }

    // 为每个section添加延迟动画
    &:nth-child(1) { animation-delay: 0.1s; }
    &:nth-child(2) { animation-delay: 0.2s; }
    &:nth-child(3) { animation-delay: 0.3s; }
  }

  &__section-title {
    margin: 0 0 spacing(lg) 0;
    font-size: map-get($font-size, base);
    font-weight: map-get($font-weight, semibold);
    color: color(secondary);
    display: flex;
    align-items: center;
    gap: spacing(sm);
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    font-weight: 500;
    letter-spacing: -0.3px;
    transition: all map-get($transition, normal);

    &:hover {
      color: $primary-color;
      transform: translateX(4px);
    }

    // 图标样式
    &::before {
      margin-right: spacing(sm);
      font-weight: 300;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      transition: all map-get($transition, normal);
    }

    &:hover::before {
      transform: scale(1.1);
    }
  }
}

// 快速操作网格
.quick-actions {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: spacing(md);

  &__item {
    border-radius: radius(md);
    border: 1px solid $border-color;
    cursor: pointer;
    transition: all map-get($transition, normal);
    padding: spacing(lg) spacing(md);
    text-align: center;
    background: $card-background;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
      opacity: 0;
      transition: opacity map-get($transition, normal);
      pointer-events: none;
    }

    &:hover {
      box-shadow: 0 4px 20px rgba(102, 126, 234, 0.15);
      transform: translateY(-2px) scale(1.02);
      border-color: rgba(102, 126, 234, 0.3);

      &::before {
        opacity: 1;
      }
    }
  }

  &__icon {
    font-size: 24px;
    margin-bottom: spacing(sm);
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    font-weight: 300;
    font-size: 28px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all map-get($transition, normal);
    transform-origin: center;
    position: relative;
    z-index: 2;
  }

  &__item:hover &__icon {
    transform: scale(1.2) rotate(5deg);
    text-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }

  &__label {
    font-size: map-get($font-size, xs);
    font-weight: map-get($font-weight, semibold);
    color: color(secondary);
    line-height: map-get($line-height, tight);
    position: relative;
    z-index: 2;
    transition: all map-get($transition, normal);
  }

  &__item:hover &__label {
    color: $primary-color;
  }
}

// 工作流模板 - 现代卡片设计
.workflow-templates {
  display: flex;
  flex-direction: column;
  gap: spacing(sm);

  &__item {
    border-radius: radius(lg);
    border: 1px solid rgba(226, 232, 240, 0.6);
    cursor: pointer;
    padding: spacing(lg);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
    backdrop-filter: blur(20px);
    transition: all map-get($transition, normal);
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(102, 126, 234, 0.06) 0%, rgba(118, 75, 162, 0.06) 100%);
      opacity: 0;
      transition: opacity map-get($transition, normal);
      pointer-events: none;
    }

    &:hover {
      box-shadow: 0 6px 20px rgba(102, 126, 234, 0.12);
      transform: translateY(-2px);
      border-color: rgba(102, 126, 234, 0.3);

      &::before {
        opacity: 1;
      }
    }

    &:active {
      transform: translateY(-1px);
    }
  }

  &__content {
    display: flex;
    align-items: center;
    gap: spacing(md);
  }

  &__icon {
    width: 40px;
    height: 40px;
    border-radius: radius(md);
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    flex-shrink: 0;
    color: $primary-color;
    transition: all map-get($transition, normal);
    position: relative;
    z-index: 2;
    border: 1px solid rgba(102, 126, 234, 0.2);
  }

  &__item:hover &__icon {
    transform: scale(1.1) rotate(5deg);
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.15) 100%);
    border-color: rgba(102, 126, 234, 0.4);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
  }

  &__info {
    flex: 1;
    min-width: 0;
    position: relative;
    z-index: 2;
  }

  &__title {
    font-size: map-get($font-size, sm);
    font-weight: map-get($font-weight, semibold);
    color: color(secondary);
    margin-bottom: spacing(xs);
    line-height: map-get($line-height, tight);
    transition: all map-get($transition, normal);
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    letter-spacing: -0.2px;
  }

  &__item:hover &__title {
    color: $primary-color;
    font-weight: map-get($font-weight, bold);
  }

  &__desc {
    font-size: map-get($font-size, xs);
    color: color(tertiary);
    line-height: map-get($line-height, normal);
    font-weight: map-get($font-weight, medium);
    transition: all map-get($transition, normal);
    opacity: 0.8;
  }

  &__item:hover &__desc {
    color: color(secondary);
    opacity: 1;
  }
}

// 加载状态
.loading {
  &__message {
    display: flex;
    align-items: center;
    gap: spacing(md);
    padding: spacing(lg) spacing(xl);
    background: $card-background-hover;
    border: 1px solid $border-color;
    border-radius: radius(lg);
    color: color(tertiary);
    font-size: map-get($font-size, base);
    box-shadow: shadow(sm);
    animation: fadeIn 0.4s ease-in-out;
  }

  &__dots {
    display: flex;
    gap: 4px;
  }

  &__dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: $primary-color;
    animation: loadingDot 1.4s ease-in-out infinite both;
    box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
    transition: all map-get($transition, normal);

    &:nth-child(1) { animation-delay: -0.32s; }
    &:nth-child(2) { animation-delay: -0.16s; }
    &:nth-child(3) { animation-delay: 0s; }
  }
}

// 动画定义
@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes loadingDot {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes ripple {
  0% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(0);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(2);
  }
}

// 响应式设计
@include mobile {
  .ai-assistant-page {
    &__content {
      flex-direction: column;
    }

    &__chat {
      border-right: none;
      border-bottom: 1px solid $border-color;
    }
  }

  .chat-header {
    padding: spacing(lg) spacing(xl);
  }

  .messages-container {
    padding: spacing(lg) spacing(xl);
  }

  .chat-input {
    padding: spacing(lg) spacing(xl);
  }
}

// 高级字体渲染
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

// 图标通用增强类
.icon-glow {
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 120%;
    height: 120%;
    background: radial-gradient(circle, currentColor 0%, transparent 70%);
    opacity: 0;
    transform: translate(-50%, -50%);
    transition: opacity map-get($transition, normal);
    pointer-events: none;
    z-index: -1;
  }

  &:hover::after {
    opacity: 0.1;
  }
}

// 图标样式增强
.icon-enhanced {
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-weight: 300;
  letter-spacing: -0.5px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  transition: all map-get($transition, normal);

  &:hover {
    transform: scale(1.1);
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }
}

// 滚动条优化
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(102, 126, 234, 0.3) transparent;
}

*::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

*::-webkit-scrollbar-track {
  background: transparent;
}

*::-webkit-scrollbar-thumb {
  background: rgba(102, 126, 234, 0.3);
  border-radius: 3px;
  transition: background map-get($transition, normal);
}

*::-webkit-scrollbar-thumb:hover {
  background: rgba(102, 126, 234, 0.5);
}

// 选择文本样式
::selection {
  background: rgba(102, 126, 234, 0.1);
  color: $primary-color;
}

::-moz-selection {
  background: rgba(102, 126, 234, 0.1);
  color: $primary-color;
}
@import '../styles/variables';
@import '../styles/mixins';

// 页面变量定义 - 与全局设计系统保持一致
$header-height: 64px;
$header-height-mobile: 56px;
$sidebar-width: 320px;
$sidebar-width-mobile: 280px;

// 主页面容器 - 优化设计
.ai-assistant-page {
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  background: $global-background;
  overflow: hidden;
  position: relative;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;

  // 页面内容区域
  &__content {
    flex: 1;
    display: flex;
    height: calc(100vh - #{$header-height});
    margin-top: $header-height;
    overflow: hidden;
    animation: fadeIn 0.6s cubic-bezier(0.16, 1, 0.3, 1);
    max-width: 2400px; /* 统一最大宽度 */
    margin-left: auto;
    margin-right: auto;
    padding: 0 20px; /* 添加左右内边距 */
  }

  // 聊天区域 - 整体卡片样式
  &__chat {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    margin: 24px;
    position: relative;
    min-width: 0; // 防止flex收缩问题
    overflow: hidden;
  }

  // 右侧面板 - 优化设计
  &__sidebar {
    width: $sidebar-width;
    min-width: $sidebar-width;
    max-width: 400px;
    background: $card-background;
    display: flex;
    flex-direction: column;
    border-left: 1px solid $border-color;
    box-shadow: -2px 0 8px rgba(0, 0, 0, 0.04);
    backdrop-filter: $backdrop-filter-normal;

    @include tablet {
      width: $sidebar-width-mobile;
      min-width: $sidebar-width-mobile;
    }

    @include mobile {
      display: none; // 移动端隐藏侧边栏
    }
  }
}

// 聊天头部 - 优化设计
.chat-header {
  background: $header-background;
  backdrop-filter: $backdrop-filter-normal;
  padding: spacing(xl) spacing(2xl);
  border-bottom: 1px solid $border-color;
  flex-shrink: 0;
  box-shadow: shadow(sm);
  position: relative;
  z-index: 10;

  &__content {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &__info {
    flex: 1;
  }

  &__title {
    margin: 0;
    font-size: map-get($font-size, xl);
    font-weight: map-get($font-weight, bold);
    background: $primary-gradient;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: flex;
    align-items: center;
    gap: spacing(md);
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    font-weight: 600;
    letter-spacing: -0.8px;
    transition: all map-get($transition, normal);

    @include mobile {
      font-size: map-get($font-size, lg);
    }

    // AI助手主图标 - 优化动画
    &::before {
      content: '◉';
      margin-right: spacing(md);
      font-weight: 300;
      text-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
      animation: pulse 2s ease-in-out infinite;
      transition: all map-get($transition, normal);
    }

    &:hover {
      transform: scale(1.02);

      &::before {
        transform: scale(1.1);
        text-shadow: 0 4px 8px rgba(102, 126, 234, 0.4);
      }
    }
  }

  &__subtitle {
    color: color(tertiary);
    font-size: map-get($font-size, sm);
    margin-top: spacing(xs);
    font-weight: map-get($font-weight, medium);
    opacity: 0;
    animation: fadeIn 0.8s cubic-bezier(0.16, 1, 0.3, 1) 0.3s forwards;
  }

  &__actions {
    display: flex;
    align-items: center;
    gap: spacing(sm);
  }
}

// 消息容器 - 优化滚动和布局
.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: spacing(xl) spacing(2xl);
  display: flex;
  flex-direction: column;
  gap: spacing(lg);
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.02) 0%, rgba(102, 126, 234, 0.01) 100%);
  position: relative;

  @include mobile {
    padding: spacing(lg) spacing(xl);
    gap: spacing(md);
  }

  // 优化滚动条样式
  @include scrollbar-custom;
}

// 消息项 - 重新设计
.message {
  display: flex;
  align-items: flex-start;
  gap: spacing(lg);
  animation: messageSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  animation-fill-mode: forwards;

  &--user {
    flex-direction: row-reverse;
  }

  // 为每条消息添加延迟动画
  &:nth-child(1) { animation-delay: 0.1s; }
  &:nth-child(2) { animation-delay: 0.2s; }
  &:nth-child(3) { animation-delay: 0.3s; }
  &:nth-child(4) { animation-delay: 0.4s; }
  &:nth-child(5) { animation-delay: 0.5s; }

  &__avatar {
    @include avatar(40px);
    border-radius: radius(md);
    font-size: spacing(lg);
    box-shadow: shadow(md);
    position: relative;
    overflow: hidden;
    transition: all map-get($transition, normal);

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
      pointer-events: none;
    }

    &--assistant {
      background: $primary-gradient;
      color: color(white);
    }

    &--user {
      background: linear-gradient(135deg, $success-color 0%, #389e0d 100%);
      color: color(white);
    }

    @include mobile {
      @include avatar(36px);
      font-size: spacing(md);
    }

    // 头像增强效果
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    font-weight: 300;
    font-size: 18px;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);

    &:hover {
      transform: scale(1.05) rotate(5deg);
      text-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
      box-shadow: shadow(lg);
    }
  }

  &__content {
    flex: 1;
    max-width: calc(100% - 72px);

    @include mobile {
      max-width: calc(100% - 60px);
    }
  }

  &__bubble {
    padding: spacing(lg) spacing(xl);
    border-radius: radius(lg);
    font-size: map-get($font-size, base);
    line-height: map-get($line-height, relaxed);
    word-wrap: break-word;
    white-space: pre-wrap;
    box-shadow: shadow(sm);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    transition: all map-get($transition, normal);

    &--assistant {
      background: $card-background-hover;
      color: color(primary);
      margin-right: 48px;
      border: 1px solid $border-color;

      @include mobile {
        margin-right: 24px;
      }

      &:hover {
        background: rgba(255, 255, 255, 1);
        box-shadow: shadow(md);
        transform: translateY(-2px);
      }
    }

    &--user {
      background: $primary-gradient;
      color: color(white);
      margin-left: 48px;
      box-shadow: shadow(primary);

      @include mobile {
        margin-left: 24px;
      }

      &:hover {
        box-shadow: 0 12px 32px rgba(102, 126, 234, 0.4);
        transform: translateY(-2px);
      }
    }

    @include mobile {
      padding: spacing(md) spacing(lg);
      font-size: map-get($font-size, sm);
    }
  }

  &__timestamp {
    font-size: map-get($font-size, xs);
    color: color(muted);
    margin-top: spacing(sm);
    text-align: center;
    opacity: 0.7;
    transition: opacity map-get($transition, normal);

    .message--user & {
      text-align: right;
    }

    .message--assistant & {
      text-align: left;
    }

    &:hover {
      opacity: 1;
    }
  }
}

// 输入区域 - 优化设计
.chat-input {
  background: $header-background;
  backdrop-filter: $backdrop-filter-normal;
  padding: spacing(xl) spacing(2xl) spacing(2xl);
  border-top: 1px solid $border-color;
  flex-shrink: 0;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.04);
  position: relative;
  z-index: 10;

  @include mobile {
    padding: spacing(lg) spacing(xl) spacing(xl);
  }

  &__container {
    display: flex;
    align-items: flex-end;
    gap: spacing(md);
    max-width: 100%;
  }

  &__wrapper {
    flex: 1;
    position: relative;
    background: $input-background;
    border: 2px solid $border-color;
    border-radius: radius(lg);
    transition: all map-get($transition, normal);
    display: flex;
    align-items: stretch;
    min-height: 48px;

    &:focus-within {
      border-color: $primary-color;
      box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
      background: rgba(255, 255, 255, 1);
      transform: translateY(-1px);
    }
  }

  &__field {
    flex: 1;
    min-height: 46px; // 减去border的2px
    max-height: 118px; // 减去border的2px
    padding: spacing(md) spacing(lg);
    padding-right: 100px; // 为内部按钮留出空间
    border: none;
    border-radius: radius(lg);
    font-size: map-get($font-size, base);
    font-family: inherit;
    resize: none;
    outline: none;
    background: transparent;
    transition: all map-get($transition, normal);
    line-height: map-get($line-height, normal);
    box-sizing: border-box;

    &::placeholder {
      color: color(muted);
      font-weight: map-get($font-weight, normal);
    }

    @include mobile {
      font-size: 16px; // 防止iOS缩放
      padding-right: 90px; // 移动端调整
    }
  }

  // 输入框内部按钮容器
  &__inner-actions {
    position: absolute;
    right: spacing(sm);
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    gap: spacing(xs);
    z-index: 2;
    height: auto;
  }

  &__actions {
    display: flex;
    align-items: center;
    gap: spacing(sm);
  }

  &__send {
    width: 36px;
    height: 36px;
    border-radius: radius(sm);
    border: none;
    background: $primary-gradient;
    color: color(white);
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all map-get($transition, normal);
    flex-shrink: 0;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
      opacity: 0;
      transition: opacity map-get($transition, normal);
    }

    &:hover {
      &::before {
        opacity: 1;
      }

      &:not(:disabled) {
        transform: scale(1.1);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
      }
    }

    &:active {
      transform: scale(1);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    // 发送按钮图标优化
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    font-weight: 300;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.2);
  }

  &__upload {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: radius(xs);
    background: transparent;
    color: color(tertiary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all map-get($transition, normal);
    font-size: 16px;
    position: relative;

    &:hover {
      background: rgba(102, 126, 234, 0.1);
      color: $primary-color;
      transform: scale(1.1);
      text-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
    }

    // 上传按钮图标优化
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    font-weight: 300;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }
}

// 侧边栏
.sidebar {
  display: flex;
  flex-direction: column;
  height: 100%;

  &__header {
    padding: spacing(xl) spacing(xl) spacing(lg);
    border-bottom: 1px solid $border-color;
    background: $header-background;
    backdrop-filter: $backdrop-filter-light;
    flex-shrink: 0;
  }

  &__title {
    margin: 0;
    font-size: map-get($font-size, lg);
    font-weight: map-get($font-weight, semibold);
    color: color(primary);
    background: $primary-gradient;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    font-weight: 600;
    letter-spacing: -0.5px;
    transition: all map-get($transition, normal);

    &:hover {
      transform: scale(1.02);
    }
  }

  &__subtitle {
    margin: spacing(xs) 0 0 0;
    font-size: map-get($font-size, xs);
    color: color(tertiary);
    font-weight: map-get($font-weight, medium);
    opacity: 0;
    animation: fadeIn 0.8s cubic-bezier(0.16, 1, 0.3, 1) 0.5s forwards;
  }

  &__content {
    flex: 1;
    overflow-y: auto;
    padding: 0;

    // 优化滚动条样式
    @include scrollbar-custom;
  }

  &__section {
    padding: spacing(xl);
    animation: slideInUp 0.6s cubic-bezier(0.16, 1, 0.3, 1);
    animation-fill-mode: both;

    &:not(:last-child) {
      border-bottom: 1px solid rgba(226, 232, 240, 0.3);
    }

    // 为每个section添加延迟动画
    &:nth-child(1) { animation-delay: 0.1s; }
    &:nth-child(2) { animation-delay: 0.2s; }
    &:nth-child(3) { animation-delay: 0.3s; }
  }

  &__section-title {
    margin: 0 0 spacing(lg) 0;
    font-size: map-get($font-size, base);
    font-weight: map-get($font-weight, semibold);
    color: color(secondary);
    display: flex;
    align-items: center;
    gap: spacing(sm);
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    font-weight: 500;
    letter-spacing: -0.3px;
    transition: all map-get($transition, normal);

    &:hover {
      color: $primary-color;
      transform: translateX(4px);
    }

    // 图标样式
    &::before {
      margin-right: spacing(sm);
      font-weight: 300;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      transition: all map-get($transition, normal);
    }

    &:hover::before {
      transform: scale(1.1);
    }
  }
}

// 快速操作网格 - 参考纳米AI搜索界面设计
.quick-actions {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: spacing(sm);

  &__item {
    border-radius: radius(lg);
    border: 1px solid rgba(226, 232, 240, 0.6);
    cursor: pointer;
    transition: all map-get($transition, normal);
    padding: spacing(lg) spacing(md);
    text-align: center;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
    backdrop-filter: blur(20px);
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(102, 126, 234, 0.08) 0%, rgba(118, 75, 162, 0.08) 100%);
      opacity: 0;
      transition: opacity map-get($transition, normal);
      pointer-events: none;
    }

    &:hover {
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
      transform: translateY(-3px) scale(1.02);
      border-color: rgba(102, 126, 234, 0.4);

      &::before {
        opacity: 1;
      }
    }

    &:active {
      transform: translateY(-1px) scale(1.01);
    }
  }

  &__icon {
    font-size: 32px;
    margin-bottom: spacing(sm);
    filter: drop-shadow(0 2px 8px rgba(0,0,0,0.1));
    transition: all map-get($transition, normal);
    transform-origin: center;
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    margin: 0 auto spacing(sm);
    border-radius: radius(md);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.4) 100%);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }

  &__item:hover &__icon {
    transform: scale(1.1) rotate(-3deg);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  }

  &__label {
    font-size: map-get($font-size, xs);
    font-weight: map-get($font-weight, semibold);
    color: color(secondary);
    line-height: map-get($line-height, tight);
    position: relative;
    z-index: 2;
    transition: all map-get($transition, normal);
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    letter-spacing: -0.2px;
  }

  &__item:hover &__label {
    color: $primary-color;
    font-weight: map-get($font-weight, bold);
  }
}

// 工作流模板 - 现代卡片设计
.workflow-templates {
  display: flex;
  flex-direction: column;
  gap: spacing(sm);

  &__item {
    border-radius: radius(lg);
    border: 1px solid rgba(226, 232, 240, 0.6);
    cursor: pointer;
    padding: spacing(lg);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
    backdrop-filter: blur(20px);
    transition: all map-get($transition, normal);
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(102, 126, 234, 0.06) 0%, rgba(118, 75, 162, 0.06) 100%);
      opacity: 0;
      transition: opacity map-get($transition, normal);
      pointer-events: none;
    }

    &:hover {
      box-shadow: 0 6px 20px rgba(102, 126, 234, 0.12);
      transform: translateY(-2px);
      border-color: rgba(102, 126, 234, 0.3);

      &::before {
        opacity: 1;
      }
    }

    &:active {
      transform: translateY(-1px);
    }
  }

  &__content {
    display: flex;
    align-items: center;
    gap: spacing(md);
  }

  &__icon {
    width: 40px;
    height: 40px;
    border-radius: radius(md);
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    flex-shrink: 0;
    color: $primary-color;
    transition: all map-get($transition, normal);
    position: relative;
    z-index: 2;
    border: 1px solid rgba(102, 126, 234, 0.2);
  }

  &__item:hover &__icon {
    transform: scale(1.1) rotate(5deg);
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.15) 100%);
    border-color: rgba(102, 126, 234, 0.4);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
  }

  &__info {
    flex: 1;
    min-width: 0;
    position: relative;
    z-index: 2;
  }

  &__title {
    font-size: map-get($font-size, sm);
    font-weight: map-get($font-weight, semibold);
    color: color(secondary);
    margin-bottom: spacing(xs);
    line-height: map-get($line-height, tight);
    transition: all map-get($transition, normal);
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    letter-spacing: -0.2px;
  }

  &__item:hover &__title {
    color: $primary-color;
    font-weight: map-get($font-weight, bold);
  }

  &__desc {
    font-size: map-get($font-size, xs);
    color: color(tertiary);
    line-height: map-get($line-height, normal);
    font-weight: map-get($font-weight, medium);
    transition: all map-get($transition, normal);
    opacity: 0.8;
  }

  &__item:hover &__desc {
    color: color(secondary);
    opacity: 1;
  }
}



// 加载状态
.loading {
  &__message {
    display: flex;
    align-items: center;
    gap: spacing(md);
    padding: spacing(lg) spacing(xl);
    background: $card-background-hover;
    border: 1px solid $border-color;
    border-radius: radius(lg);
    color: color(tertiary);
    font-size: map-get($font-size, base);
    box-shadow: shadow(sm);
    animation: fadeIn 0.4s ease-in-out;
  }

  &__dots {
    display: flex;
    gap: 4px;
  }

  &__dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: $primary-color;
    animation: loadingDot 1.4s ease-in-out infinite both;
    box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
    transition: all map-get($transition, normal);

    &:nth-child(1) { animation-delay: -0.32s; }
    &:nth-child(2) { animation-delay: -0.16s; }
    &:nth-child(3) { animation-delay: 0s; }
  }
}

// 动画定义
@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes loadingDot {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes ripple {
  0% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(0);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(2);
  }
}

// 响应式设计
@include mobile {
  .ai-assistant-page {
    &__content {
      flex-direction: column;
    }

    &__chat {
      border-right: none;
      border-bottom: 1px solid $border-color;
    }
  }

  .chat-header {
    padding: spacing(lg) spacing(xl);
  }

  .messages-container {
    padding: spacing(lg) spacing(xl);
  }

  .chat-input {
    padding: spacing(lg) spacing(xl);
  }
}

// 高级字体渲染
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

// 图标通用增强类
.icon-glow {
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 120%;
    height: 120%;
    background: radial-gradient(circle, currentColor 0%, transparent 70%);
    opacity: 0;
    transform: translate(-50%, -50%);
    transition: opacity map-get($transition, normal);
    pointer-events: none;
    z-index: -1;
  }

  &:hover::after {
    opacity: 0.1;
  }
}

// 图标样式增强
.icon-enhanced {
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-weight: 300;
  letter-spacing: -0.5px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  transition: all map-get($transition, normal);

  &:hover {
    transform: scale(1.1);
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }
}

// 滚动条优化
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(102, 126, 234, 0.3) transparent;
}

*::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

*::-webkit-scrollbar-track {
  background: transparent;
}

*::-webkit-scrollbar-thumb {
  background: rgba(102, 126, 234, 0.3);
  border-radius: 3px;
  transition: background map-get($transition, normal);
}

*::-webkit-scrollbar-thumb:hover {
  background: rgba(102, 126, 234, 0.5);
}

// 选择文本样式
::selection {
  background: rgba(102, 126, 234, 0.1);
  color: $primary-color;
}

::-moz-selection {
  background: rgba(102, 126, 234, 0.1);
  color: $primary-color;
}
// Agent 卡片列表
.agent-cards {
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow-y: auto;

  &__header {
    margin-bottom: 20px;
    
    h3 {
      margin: 0 0 8px 0;
      font-size: 18px;
      font-weight: 600;
      background: $primary-gradient;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
    
    p {
      margin: 0;
      font-size: 12px;
      color: #64748b;
    }
  }

  &__item {
    padding: 16px;
    border-radius: 12px;
    border: 1px solid rgba(226, 232, 240, 0.6);
    background: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
      border-color: rgba(102, 126, 234, 0.3);

      &::before {
        opacity: 1;
      }
    }
  }

  &__icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    margin-bottom: 12px;
    position: relative;
    z-index: 2;
  }

  &__title {
    font-size: 14px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 4px;
    position: relative;
    z-index: 2;
  }

  &__desc {
    font-size: 12px;
    color: #64748b;
    line-height: 1.4;
    position: relative;
    z-index: 2;
  }
}

// 工作流模板卡片
.workflow-cards {
  padding: 0 24px 24px;

  &__header {
    margin-bottom: 16px;
    padding: 0;
    
    h4 {
      margin: 0 0 8px 0;
      font-size: 16px;
      font-weight: 600;
      color: #374151;
    }
  }

  &__list {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  &__item {
    padding: 12px;
    border-radius: 8px;
    border: 1px solid rgba(226, 232, 240, 0.6);
    background: rgba(248, 250, 252, 0.8);
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 12px;

    &:hover {
      background: rgba(102, 126, 234, 0.05);
      border-color: rgba(102, 126, 234, 0.2);
    }
  }
}
  &__icon {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    background: $primary-gradient;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
    flex-shrink: 0;
  }

  &__info {
    flex: 1;
    min-width: 0;
  }

  &__title {
    font-size: 13px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 2px;
  }

  &__desc {
    font-size: 11px;
    color: #64748b;
    line-height: 1.3;
  }
}