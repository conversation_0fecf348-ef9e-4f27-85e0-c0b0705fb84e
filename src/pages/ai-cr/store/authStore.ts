// 使用 Stone 统一依赖管理
import { makeAutoObservable } from '@rome/stone/mobx'
import { model, loading } from '@nibfe/mobx-loading'
import type { User, AuthState } from '../types/auth'

const initialState: AuthState = {
  isAuthenticated: false,
  user: null,
  isLoading: false,
  error: null,
  rememberMe: false,
}

@model('AuthStore')
class AuthStore {
  constructor() {
    makeAutoObservable(this)
  }

  // 状态
  isAuthenticated = initialState.isAuthenticated
  user = initialState.user
  isLoading = initialState.isLoading
  error = initialState.error
  rememberMe = initialState.rememberMe

  // 操作方法
  setUser(user: User) {
    this.user = user
    this.isAuthenticated = true
    this.error = null
  }

  setAuthenticated(authenticated: boolean) {
    this.isAuthenticated = authenticated
    this.error = null
  }

  setLoading(loading: boolean) {
    this.isLoading = loading
  }

  setError(error: string | null) {
    this.error = error
  }

  setRememberMe(remember: boolean) {
    this.rememberMe = remember
  }

  logout() {
    this.isAuthenticated = false
    this.user = null
    this.error = null
    this.isLoading = false
  }

  clearError() {
    this.error = null
  }

  // 更新用户信息
  updateUser(updates: Partial<User>) {
    if (this.user) {
      this.user = { ...this.user, ...updates }
    }
  }

  // 异步登录检查
  @loading()
  async checkLoginStatus() {
    try {
      // 临时禁用SSO认证，直接设置为已认证状态
      this.setAuthenticated(true)
      this.setUser({
        id: 'temp-user',
        email: '<EMAIL>',
        nickname: '测试用户',
        login_channel: 'temp'
      })
    } catch (error) {
      this.setError('登录状态检查失败')
      console.error('Auth check failed:', error)
    }
  }
}

export default new AuthStore()
