// 使用 Stone 统一依赖管理
import React from '@rome/stone/react'
import ReactDOM from '@rome/stone/react-dom'
// import sso from '@/lib/sso'  // 已禁用SSO
import App from './app'
import './styles/index.scss'
import '@/lib/elink'

// 直接渲染应用，不使用SSO登录
const renderApp = () => {
  const rootElement = document.getElementById('root')
  if (rootElement) {
    ReactDOM.render(
      <React.StrictMode>
        <App />
      </React.StrictMode>,
      rootElement
    )
  } else {
    console.error('找不到root元素')
  }
}

// 直接渲染应用，跳过SSO登录
try {
  renderApp()
} catch (error) {
  console.error('应用渲染失败:', error)
}
