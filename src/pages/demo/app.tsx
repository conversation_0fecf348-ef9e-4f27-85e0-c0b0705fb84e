import React from '@rome/stone/react'
import { BrowserRouter as Router, Switch, Route } from '@rome/stone/react-router-dom'
// mtd-react 和 mtd-react-mobile 已按需接入，无需再进行配置，直接使用即可
import { Button } from '@ss/mtd-react'
/**
 * 使用@nibfe/mobx-loading管理异步操作
 * 📖DOC📖 http://npm.sankuai.com/v2/pkg/detail?name=%40nibfe%2Fmobx-loading
 */
import { loadingStore } from '@nibfe/mobx-loading'
import { observer } from '@rome/stone/mobx-react'
import routes from './routes'
import styles from './styles/app.module.scss'
import logo from './assets/logo.svg'

const Loading = ({ loading }: { loading: boolean }) => {
  return loading ? <div>加载中。。。</div> : null
}

const App: React.FC = observer(() => {
  return (
    <div className={styles.app}>
      <Loading loading={loadingStore.global} />
      <Router>
        <Switch>
          {routes.map(route => (
            <Route key={route.path} {...route} />
          ))}
        </Switch>
      </Router>
      <header className={styles.appHeader}>
        <img src={logo} className={styles.appLogo} alt="logo" />
        <p>🚀 Rome React 🚀</p>
        <Button.Link href="https://km.sankuai.com/space/rra" target="_blank" type="primary">
          使用文档
        </Button.Link>
      </header>
    </div>
  )
})

export default App
