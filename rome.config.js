import webpack from 'webpack'
import { defineConfig } from '@rome/core'

const devServer = {
  proxy: {
    // [保留]开店宝统一登录页代理，代替R站代理，解决本地循环跳转问题
    '/bizaccount/login': {
      target: process.env.APP_ECOM_HOST,
      changeOrigin: true,
    },
    // epassport settoken 接口
    '/gw/bsso': {
      target: process.env.APP_EPASSPORT_HOST,
      changeOrigin: true,
    },
    // sso settoken 接口 (已禁用)
    // '/sso/web/auth': {
    //   target: `http${process.env.HTTPS ? 's' : ''}:${process.env.APP_SSO_HOST}`,
    //   changeOrigin: true,
    // },
    // 水印
    '/api-wm': {
      target: process.env.WATERMARK_HOST,
      changeOrigin: true,
    },
    // AI代码审查后端API代理
    '/yunzhuan/api/v1': {
      target: 'http://127.0.0.1:9000',
      changeOrigin: true,
      pathRewrite: {
        '^/yunzhuan/api/v1': '/yunzhuan/api/v1'
      },
      logLevel: 'debug', // 开启调试日志
      onProxyReq: (proxyReq, req, res) => {
        console.log(`[Proxy] ${req.method} ${req.url} -> http://127.0.0.1:9000${req.url}`)
      },
      onError: (err, req, res) => {
        console.error(`[Proxy Error] ${req.url}:`, err.message)
      }
    },
    // 业务接口
    '^(.*)?/gw/': {
      target: process.env.APP_ECOM_HOST,
      changeOrigin: true,
    },
  },
}

const isEnvProduction = process.env.NODE_ENV === 'production'

export default defineConfig({
  pluginOptions: {
    /**
     * 🐞REPLACE🐞 下方的各个环境变量需要及时在 .env 文件中进行修改
     *
     * 📖DOC📖 rome配置文档：https://km.sankuai.com/space/rra
     */
    stone: {
      owl: {
        /** 📖DOC📖 appkey申请：https://avatar.mws.sankuai.com/#/home */
        devMode: process.env.APP_ENV !== 'production',
        project: process.env.APP_KEY,
      },
      lx: [
        {
          options: {
            category: 'merchant',
            defaultAppnm: 'merchant',
            autopv: 'off',
          },
          include: ['demo'],
        },
      ],
      watermark: {
        urlPrefix: '/api-wm',
      },
      epassport: {
        env: process.env.APP_EPASSPORT_ENV,
        appKey: process.env.APP_KEY,
        bgSource: 1,
      },
      // sso: {
      //   /** 📖DOC📖 clientId申请：https://km.sankuai.com/page/********* */
      //   clientId: process.env.APP_SSO_CLIENT_ID,
      //   accessEnv: process.env.APP_SSO_ENV,
      //   callbackUrl: 'window.location.pathname',
      //   schema: isEnvProduction ? 'https' : undefined,
      //   sameSite: isEnvProduction ? false : undefined,
      //   // https://km.sankuai.com/collabpage/2242293028#id-proxy%E4%BB%A3%E7%90%86
      //   xfwd: true,
      // },
      /**
       * 整个工程统一的路由前缀
       * 📖DOC📖 开发环境配置： https://km.sankuai.com/page/********* 场景1：工程有统一路由前缀
       * 📖DOC📖 生产环境配置：https://km.sankuai.com/page/1275166693 Q：如何配置请求路由到HTML的映射？
       */
      rootdir: 'ai-cr',
    },
    /** 若需开启移动端适配，请查阅https://km.sankuai.com/docs/rra/page/664270218 */
    // viewport: {
    //   options: {},
    //   pc: true,
    // },
  },
  configureWebpack: {
    plugins: [
      new webpack.ProvidePlugin({
        React: 'react',
        react: 'react',
      }),
    ],
  },
  devServer,
})
