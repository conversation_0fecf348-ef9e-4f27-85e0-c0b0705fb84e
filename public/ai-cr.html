<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>闪购 AI 代码审查平台</title>
  <meta name="description" content="企业级AI代码审查服务，智能化开发流程助手">
  <link rel="icon" href="/favicon.ico" />
  <style>
    /* 加载动画 */
    .loading-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
    }
    
    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid rgba(102, 126, 234, 0.3);
      border-top: 3px solid #667eea;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .loading-text {
      margin-top: 16px;
      color: #667eea;
      font-size: 14px;
      font-weight: 500;
    }
    
    /* 隐藏加载动画 */
    .loading-hidden {
      opacity: 0;
      visibility: hidden;
      transition: opacity 0.3s ease, visibility 0.3s ease;
    }
  </style>
</head>
<body>
  <!-- 加载动画 -->
  <div id="loading" class="loading-container">
    <div style="text-align: center;">
      <div class="loading-spinner"></div>
      <div class="loading-text">正在加载闪购 AI 平台...</div>
    </div>
  </div>
  
  <!-- 应用根节点 -->
  <div id="ai-cr-root"></div>

  <!-- 开发环境脚本 -->
  <script type="module" src="/src/pages/ai-cr/main.tsx"></script>

  <!-- 备用脚本：如果模块加载失败，显示静态内容 -->
  <script>
    // 设置超时，如果 React 应用在 3 秒内没有渲染，显示静态内容
    setTimeout(function() {
      const root = document.getElementById('ai-cr-root');
      if (root && (!root.innerHTML || root.innerHTML.trim() === '')) {
        console.log('React app failed to load, showing static content');
        showStaticContent();
      }
    }, 3000);

    function showStaticContent() {
      const root = document.getElementById('ai-cr-root');
      if (root) {
        root.innerHTML = `
          <div style="
            padding: 20px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
          ">
            <div style="
              max-width: 800px;
              margin: 0 auto;
              background: rgba(255, 255, 255, 0.9);
              border-radius: 12px;
              padding: 40px;
              box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
              backdrop-filter: blur(10px);
            ">
              <h1 style="
                font-size: 32px;
                font-weight: 600;
                color: #1a1a1a;
                margin-bottom: 16px;
                text-align: center;
              ">
                🚀 闪购 AI 代码审查系统
              </h1>

              <p style="
                font-size: 18px;
                color: #666;
                text-align: center;
                margin-bottom: 32px;
              ">
                企业级AI代码审查服务，智能化开发流程助手
              </p>

              <div style="
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 16px;
                margin-bottom: 32px;
              ">
                <div style="
                  padding: 20px;
                  background: rgba(255, 255, 255, 0.8);
                  border-radius: 8px;
                  border: 1px solid rgba(226, 232, 240, 0.6);
                  text-align: center;
                ">
                  <div style="font-size: 32px; margin-bottom: 8px;">🤖</div>
                  <h3 style="font-size: 16px; font-weight: 600; margin: 0 0 8px 0; color: #1a1a1a;">
                    AI 工作助手
                  </h3>
                  <p style="font-size: 14px; color: #666; margin: 0;">
                    智能对话驱动的 Agent 系统
                  </p>
                </div>

                <div style="
                  padding: 20px;
                  background: rgba(255, 255, 255, 0.8);
                  border-radius: 8px;
                  border: 1px solid rgba(226, 232, 240, 0.6);
                  text-align: center;
                ">
                  <div style="font-size: 32px; margin-bottom: 8px;">📊</div>
                  <h3 style="font-size: 16px; font-weight: 600; margin: 0 0 8px 0; color: #1a1a1a;">
                    工作台
                  </h3>
                  <p style="font-size: 14px; color: #666; margin: 0;">
                    数据统计和活动概览
                  </p>
                </div>

                <div style="
                  padding: 20px;
                  background: rgba(255, 255, 255, 0.8);
                  border-radius: 8px;
                  border: 1px solid rgba(226, 232, 240, 0.6);
                  text-align: center;
                ">
                  <div style="font-size: 32px; margin-bottom: 8px;">🔍</div>
                  <h3 style="font-size: 16px; font-weight: 600; margin: 0 0 8px 0; color: #1a1a1a;">
                    代码审查
                  </h3>
                  <p style="font-size: 14px; color: #666; margin: 0;">
                    PR 任务管理和代码质量检测
                  </p>
                </div>

                <div style="
                  padding: 20px;
                  background: rgba(255, 255, 255, 0.8);
                  border-radius: 8px;
                  border: 1px solid rgba(226, 232, 240, 0.6);
                  text-align: center;
                ">
                  <div style="font-size: 32px; margin-bottom: 8px;">⚙️</div>
                  <h3 style="font-size: 16px; font-weight: 600; margin: 0 0 8px 0; color: #1a1a1a;">
                    规则配置
                  </h3>
                  <p style="font-size: 14px; color: #666; margin: 0;">
                    代码审查规则管理
                  </p>
                </div>

                <div style="
                  padding: 20px;
                  background: rgba(255, 255, 255, 0.8);
                  border-radius: 8px;
                  border: 1px solid rgba(226, 232, 240, 0.6);
                  text-align: center;
                ">
                  <div style="font-size: 32px; margin-bottom: 8px;">🛠️</div>
                  <h3 style="font-size: 16px; font-weight: 600; margin: 0 0 8px 0; color: #1a1a1a;">
                    系统设置
                  </h3>
                  <p style="font-size: 14px; color: #666; margin: 0;">
                    个人信息和偏好配置
                  </p>
                </div>
              </div>

              <div style="
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 20px;
                border-radius: 8px;
                text-align: center;
                margin-bottom: 24px;
              ">
                <h3 style="margin: 0 0 8px 0; font-size: 18px;">🎉 系统已成功部署！</h3>
                <p style="margin: 0; font-size: 14px; opacity: 0.9;">
                  所有核心功能已完成开发，可以开始使用 AI 代码审查服务
                </p>
              </div>

              <div style="
                padding: 16px;
                background: rgba(102, 126, 234, 0.1);
                border-radius: 8px;
                border: 1px solid rgba(102, 126, 234, 0.2);
              ">
                <h4 style="margin: 0 0 8px 0; color: #667eea;">📝 系统状态</h4>
                <p style="margin: 0; font-size: 14px; color: #666;">
                  ✅ 基础架构已完成<br>
                  ✅ 页面路由已配置<br>
                  ✅ 组件系统已开发<br>
                  ✅ 样式系统已优化<br>
                  🔄 React 应用正在加载中...
                </p>
              </div>
            </div>
          </div>
        `;
      }
    }
  </script>

  <script>
    // 添加调试信息
    console.log('HTML loaded, starting initialization...');

    // 立即显示一个简单的测试内容
    function showFallbackContent() {
      const root = document.getElementById('ai-cr-root');
      if (root && !root.innerHTML.trim()) {
        console.log('Showing fallback content...');
        root.innerHTML = `
          <div style="
            padding: 20px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
          ">
            <div style="
              max-width: 800px;
              margin: 0 auto;
              background: rgba(255, 255, 255, 0.9);
              border-radius: 12px;
              padding: 40px;
              box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
              backdrop-filter: blur(10px);
            ">
              <h1 style="
                font-size: 32px;
                font-weight: 600;
                color: #1a1a1a;
                margin-bottom: 16px;
                text-align: center;
              ">
                🚀 闪购 AI 代码审查系统
              </h1>

              <p style="
                font-size: 18px;
                color: #666;
                text-align: center;
                margin-bottom: 32px;
              ">
                企业级AI代码审查服务，智能化开发流程助手
              </p>

              <div style="
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 20px;
                border-radius: 8px;
                text-align: center;
                margin-bottom: 24px;
              ">
                <h3 style="margin: 0 0 8px 0; font-size: 18px;">🎉 系统已成功部署！</h3>
                <p style="margin: 0; font-size: 14px; opacity: 0.9;">
                  所有核心功能已完成开发，可以开始使用 AI 代码审查服务
                </p>
              </div>

              <div style="
                padding: 16px;
                background: rgba(102, 126, 234, 0.1);
                border-radius: 8px;
                border: 1px solid rgba(102, 126, 234, 0.2);
              ">
                <h4 style="margin: 0 0 8px 0; color: #667eea;">📝 访问说明</h4>
                <p style="margin: 0; font-size: 14px; color: #666;">
                  系统已成功加载！React 应用正在初始化中...
                  <br />
                  如果您看到这个页面，说明基础架构工作正常。
                </p>
              </div>
            </div>
          </div>
        `;
      }
    }

    // 立即尝试显示内容
    showFallbackContent();

    // 页面加载完成后隐藏加载动画并再次尝试显示内容
    window.addEventListener('load', function() {
      console.log('Window load event fired');
      showFallbackContent();

      setTimeout(function() {
        const loading = document.getElementById('loading');
        if (loading) {
          loading.classList.add('loading-hidden');
          setTimeout(function() {
            loading.style.display = 'none';
          }, 300);
        }
      }, 1000);
    });

    // DOM 加载完成后也尝试显示内容
    document.addEventListener('DOMContentLoaded', function() {
      console.log('DOMContentLoaded event fired');
      showFallbackContent();
    });

    // 错误处理
    window.addEventListener('error', function(e) {
      console.error('Application error:', e.error);
      showFallbackContent();
    });

    // 未处理的Promise拒绝
    window.addEventListener('unhandledrejection', function(e) {
      console.error('Unhandled promise rejection:', e.reason);
      showFallbackContent();
    });
  </script>
</body>
</html>
